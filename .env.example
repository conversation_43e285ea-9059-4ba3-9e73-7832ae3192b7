# Environment Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_news_scraper
DB_USER=postgres
DB_PASSWORD=postgres

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true
API_TITLE=AI/LLM News Scraper API
API_VERSION=0.1.0

# Scraping Configuration
SCRAPER_USER_AGENT=AI-News-Scraper/1.0 (+https://github.com/your-repo)
SCRAPER_REQUEST_DELAY=1.0
SCRAPER_MAX_RETRIES=3
SCRAPER_TIMEOUT=30

# Twitter API Configuration
TWITTER_BEARER_TOKEN=
TWITTER_API_KEY=
TWITTER_API_SECRET=
TWITTER_ACCESS_TOKEN=
TWITTER_ACCESS_TOKEN_SECRET=

# Reddit API Configuration
REDDIT_CLIENT_ID=
REDDIT_CLIENT_SECRET=
REDDIT_USER_AGENT=AI-News-Scraper/1.0 by YourUsername

# News API Configuration
NEWS_API_KEY=
GUARDIAN_API_KEY=
