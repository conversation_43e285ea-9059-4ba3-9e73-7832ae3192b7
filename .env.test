# Test environment configuration - SQLite
# Database Configuration (SQLite for testing)
DATABASE_URL=sqlite:///./test_scraper.db

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true

# External APIs (test keys)
NEWS_API_KEY=test_key
GUARDIAN_API_KEY=test_key
TWITTER_BEARER_TOKEN=test_token
REDDIT_CLIENT_ID=test_id
REDDIT_CLIENT_SECRET=test_secret
REDDIT_USER_AGENT=test_agent

# Scraping Configuration
SCRAPING_DELAY=1
SCRAPING_TIMEOUT=30
SCRAPING_MAX_RETRIES=3
