["tests/test_api.py::test_health_endpoint", "tests/test_api.py::test_root_endpoint", "tests/test_content_processing.py::TestContentProcessing::test_analysis_summary_generation", "tests/test_content_processing.py::TestContentProcessing::test_confidence_threshold_filtering", "tests/test_content_processing.py::TestContentProcessing::test_content_processor_initialization", "tests/test_content_processing.py::TestContentProcessing::test_content_processor_multiple_articles", "tests/test_content_processing.py::TestContentProcessing::test_content_processor_single_article", "tests/test_content_processing.py::TestContentProcessing::test_empty_article_handling", "tests/test_content_processing.py::TestContentProcessing::test_keyword_extraction", "tests/test_content_processing.py::TestContentProcessing::test_keyword_extractor_initialization", "tests/test_content_processing.py::TestContentProcessing::test_processing_statistics", "tests/test_content_processing.py::TestContentProcessing::test_processor_shutdown", "tests/test_content_processing.py::TestContentProcessing::test_selective_analyzer_configuration", "tests/test_content_processing.py::TestContentProcessing::test_sentiment_analysis", "tests/test_content_processing.py::TestContentProcessing::test_sentiment_analyzer_initialization", "tests/test_content_processing.py::TestContentProcessing::test_stats_reset", "tests/test_content_processing.py::TestContentProcessing::test_topic_categorization", "tests/test_content_processing.py::TestContentProcessing::test_topic_categorizer_initialization", "tests/test_content_processing.py::TestContentProcessing::test_trend_detection", "tests/test_content_processing.py::TestContentProcessing::test_trend_detector_initialization", "tests/test_integration.py::TestScrapingIntegration::test_ai_keyword_detection", "tests/test_integration.py::TestScrapingIntegration::test_config_manager_initialization", "tests/test_models.py::test_article_tag_relationship", "tests/test_models.py::test_create_article", "tests/test_models.py::test_create_source", "tests/test_models.py::test_create_tag", "tests/test_phase8_integration.py::TestPhase8Integration::test_analysis_summary_generation", "tests/test_phase8_integration.py::TestPhase8Integration::test_clustering_integration", "tests/test_phase8_integration.py::TestPhase8Integration::test_content_summarization", "tests/test_phase8_integration.py::TestPhase8Integration::test_integrated_processing_pipeline", "tests/test_phase8_integration.py::TestPhase8Integration::test_performance_metrics", "tests/test_phase8_integration.py::TestPhase8Integration::test_quality_filtering", "tests/test_phase8_integration.py::TestPhase8Integration::test_relevance_scoring", "tests/test_phase8_integration.py::TestPhase8Integration::test_topic_clustering", "tests/test_scrapers.py::TestContentParser::test_title_extraction", "tests/test_scrapers.py::TestDuplicateDetector::test_detector_initialization", "tests/test_scrapers.py::TestDuplicateDetector::test_duplicate_detection", "tests/test_scrapers.py::TestDuplicateDetector::test_similarity_calculation", "tests/test_scrapers.py::TestDuplicateDetector::test_url_normalization", "tests/test_scrapers.py::TestRateLimiter::test_domain_extraction", "tests/test_scrapers.py::TestRateLimiter::test_rate_limiter_initialization", "tests/test_scrapers.py::TestRateLimiter::test_rate_limiting"]