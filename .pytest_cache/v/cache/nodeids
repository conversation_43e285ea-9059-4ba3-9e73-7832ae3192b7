["tests/test_api.py::test_health_endpoint", "tests/test_api.py::test_root_endpoint", "tests/test_integration.py::TestScrapingIntegration::test_ai_keyword_detection", "tests/test_integration.py::TestScrapingIntegration::test_config_manager_initialization", "tests/test_models.py::test_article_tag_relationship", "tests/test_models.py::test_create_article", "tests/test_models.py::test_create_source", "tests/test_models.py::test_create_tag", "tests/test_scrapers.py::TestContentParser::test_title_extraction", "tests/test_scrapers.py::TestDuplicateDetector::test_detector_initialization", "tests/test_scrapers.py::TestDuplicateDetector::test_duplicate_detection", "tests/test_scrapers.py::TestDuplicateDetector::test_similarity_calculation", "tests/test_scrapers.py::TestDuplicateDetector::test_url_normalization", "tests/test_scrapers.py::TestRateLimiter::test_domain_extraction", "tests/test_scrapers.py::TestRateLimiter::test_rate_limiter_initialization", "tests/test_scrapers.py::TestRateLimiter::test_rate_limiting"]