# AI/LLM News Scraper

An automated system that continuously monitors and aggregates AI and LLM-related news from multiple sources, providing comprehensive coverage of the rapidly evolving AI landscape.

## Features

- **Multi-source Integration**: Twitter, Reddit, Hacker News, AI publications, research repositories
- **Real-time Processing**: Continuous monitoring with configurable intervals
- **Content Classification**: Automatic tagging and categorization
- **Advanced Search**: Full-text search with filtering capabilities
- **REST API**: Programmatic access to collected data
- **Scalable Architecture**: Built for high-volume data processing

## Technology Stack

- **Backend**: Python 3.11, FastAPI, SQLAlchemy
- **Database**: PostgreSQL with full-text search
- **Task Queue**: Celery with Redis
- **Web Scraping**: BeautifulSoup4, Scrapy, Selenium
- **APIs**: Twitter API, Reddit API, News APIs
- **Infrastructure**: Docker, Docker Compose

## Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scraper-ai-llm
   ```

2. **Set up virtual environment**
   ```bash
   python3.11 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Start database services**
   ```bash
   docker-compose up -d postgres redis
   ```

6. **Initialize the database**
   ```bash
   python manage.py init-db
   ```

7. **Run the API server**
   ```bash
   python manage.py run-api
   ```

The API will be available at `http://localhost:8000` with documentation at `http://localhost:8000/docs`.

## Development

### Project Structure

```
├── api/                 # FastAPI application and endpoints
├── models/              # Database models and schemas
├── scrapers/            # Data collection modules
├── utils/               # Shared utilities
├── config/              # Configuration management
├── tests/               # Test suite
├── alembic/             # Database migrations
├── docs/                # Documentation
├── docker-compose.yml   # Docker services
├── requirements.txt     # Python dependencies
└── manage.py           # Management CLI
```

### Management Commands

```bash
# Database management
python manage.py init-db      # Initialize database
python manage.py reset-db     # Reset database
python manage.py test-db      # Test database connection

# Development server
python manage.py run-api      # Run API server
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_api.py
```

### Code Quality

```bash
# Format code
black .

# Lint code
flake8 .

# Type checking
mypy .
```

## Configuration

The application uses environment variables for configuration. Key settings include:

- **Database**: PostgreSQL connection settings
- **Redis**: Cache and task queue settings
- **API Keys**: Twitter, Reddit, News API credentials
- **Scraping**: Rate limiting and retry settings

See `.env.example` for all available configuration options.

## API Documentation

Once the server is running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Development Roadmap

This project follows a 13-phase development plan:

- **Phase 1**: ✅ Foundation & Core Infrastructure (Current)
- **Phase 2**: Database & Data Models
- **Phase 3**: Basic Web Scraping Framework
- **Phase 4**: First Data Source Integration (RSS)
- **Phase 5**: Reddit Integration
- **Phase 6**: Twitter/X API Integration
- **Phase 7**: News API Integration
- **Phase 8**: Content Processing & Classification
- **Phase 9**: API & Search Capabilities
- **Phase 10**: Web Scraping Enhancement
- **Phase 11**: Performance Optimization & Monitoring
- **Phase 12**: Advanced Features & Production
- **Phase 13**: Production Deployment & Documentation

See `Roadmap.txt` and `PRD.txt` for detailed specifications.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

[Add your license here]

## Support

[Add support information here]
