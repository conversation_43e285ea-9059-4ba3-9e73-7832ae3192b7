"""
Authentication and authorization module for the AI/LLM News Scraper API.

This module provides:
- JWT token-based authentication
- API key authentication
- Rate limiting with Redis
- Scope-based authorization
- Multiple rate limit tiers
"""

from .security import (
    Token, TokenData, APIKey, RateLimitTier,
    verify_password, get_password_hash,
    create_access_token, create_refresh_token, verify_token,
    generate_api_key, hash_api_key, verify_api_key,
    get_rate_limit_tier, create_token_pair,
    SecurityManager, security_manager,
    RATE_LIMIT_TIERS
)

from .rate_limiter import (
    RateLimitInfo, RateLimitExceeded,
    RateLimiter, rate_limiter
)

from .dependencies import (
    AuthContext,
    get_auth_context_optional, get_auth_context_required,
    enforce_rate_limit, require_scopes, require_scope,
    AuthContextOptional, AuthContextRequired, AuthContextWithRateLimit,
    ReadAccess, WriteAccess, AdminAccess,
    get_current_user_id, get_current_api_key,
    authenticate_request, check_request_rate_limit
)

__all__ = [
    # Security
    "Token", "TokenData", "APIKey", "RateLimitTier",
    "verify_password", "get_password_hash",
    "create_access_token", "create_refresh_token", "verify_token",
    "generate_api_key", "hash_api_key", "verify_api_key",
    "get_rate_limit_tier", "create_token_pair",
    "SecurityManager", "security_manager",
    "RATE_LIMIT_TIERS",
    
    # Rate Limiting
    "RateLimitInfo", "RateLimitExceeded",
    "RateLimiter", "rate_limiter",
    
    # Dependencies
    "AuthContext",
    "get_auth_context_optional", "get_auth_context_required",
    "enforce_rate_limit", "require_scopes", "require_scope",
    "AuthContextOptional", "AuthContextRequired", "AuthContextWithRateLimit",
    "ReadAccess", "WriteAccess", "AdminAccess",
    "get_current_user_id", "get_current_api_key",
    "authenticate_request", "check_request_rate_limit"
]
