"""
Authentication and authorization dependencies for FastAPI endpoints.

Provides:
- JWT token validation
- API key validation
- Rate limiting enforcement
- Scope-based authorization
"""

from typing import Optional, List, Annotated
from fastapi import Depends, HTTPException, status, Request, Header
from fastapi.security import HTTPAuthorizationCredentials

from api.auth.security import (
    security, verify_token, TokenData, APIKey, 
    security_manager, get_rate_limit_tier
)
from api.auth.rate_limiter import rate_limiter, RateLimitExceeded
from utils.logging import get_logger

logger = get_logger(__name__)


class AuthContext:
    """Authentication context for requests."""
    
    def __init__(self, user_id: Optional[str] = None, api_key_id: Optional[str] = None,
                 api_key: Optional[APIKey] = None, scopes: List[str] = None):
        self.user_id = user_id
        self.api_key_id = api_key_id
        self.api_key = api_key
        self.scopes = scopes or []
        self.is_authenticated = bool(user_id or api_key_id)
    
    def has_scope(self, required_scope: str) -> bool:
        """Check if the context has a required scope."""
        return required_scope in self.scopes
    
    def has_any_scope(self, required_scopes: List[str]) -> bool:
        """Check if the context has any of the required scopes."""
        return any(scope in self.scopes for scope in required_scopes)
    
    def get_rate_limit_tier(self) -> str:
        """Get the rate limit tier for this context."""
        if self.api_key:
            return self.api_key.rate_limit_tier
        return "basic"


async def get_auth_context_optional(
    request: Request,
    authorization: Annotated[Optional[str], Header()] = None,
    x_api_key: Annotated[Optional[str], Header()] = None
) -> AuthContext:
    """Get authentication context without requiring authentication."""
    
    # Try API key authentication first
    if x_api_key:
        api_key_obj = security_manager.validate_api_key(x_api_key)
        if api_key_obj:
            logger.debug(f"Authenticated with API key: {api_key_obj.name}")
            return AuthContext(
                api_key_id=str(api_key_obj.id),
                api_key=api_key_obj,
                scopes=api_key_obj.scopes
            )
    
    # Try JWT token authentication
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        try:
            token_data = verify_token(token)
            logger.debug(f"Authenticated with JWT token")
            return AuthContext(
                user_id=token_data.user_id,
                api_key_id=token_data.api_key_id,
                scopes=token_data.scopes
            )
        except HTTPException:
            pass  # Invalid token, continue as unauthenticated
    
    # Return unauthenticated context
    return AuthContext()


async def get_auth_context_required(
    request: Request,
    authorization: Annotated[Optional[str], Header()] = None,
    x_api_key: Annotated[Optional[str], Header()] = None
) -> AuthContext:
    """Get authentication context and require authentication."""
    
    context = await get_auth_context_optional(request, authorization, x_api_key)
    
    if not context.is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return context


async def enforce_rate_limit(
    request: Request,
    context: AuthContext = Depends(get_auth_context_optional)
) -> AuthContext:
    """Enforce rate limiting for the request."""
    
    tier_name = context.get_rate_limit_tier()
    tier = get_rate_limit_tier(tier_name)
    
    rate_limit_info = rate_limiter.check_rate_limit(
        request=request,
        tier=tier,
        user_id=context.user_id,
        api_key_id=context.api_key_id
    )
    
    if not rate_limit_info.allowed:
        raise RateLimitExceeded(rate_limit_info)
    
    # Add rate limit headers to response (will be handled by middleware)
    request.state.rate_limit_info = rate_limit_info
    
    return context


def require_scopes(required_scopes: List[str]):
    """Dependency factory for scope-based authorization."""
    
    def check_scopes(context: AuthContext = Depends(get_auth_context_required)) -> AuthContext:
        if not context.has_any_scope(required_scopes):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required scopes: {required_scopes}"
            )
        return context
    
    return check_scopes


def require_scope(required_scope: str):
    """Dependency factory for single scope authorization."""
    return require_scopes([required_scope])


# Common dependency combinations
AuthContextOptional = Annotated[AuthContext, Depends(get_auth_context_optional)]
AuthContextRequired = Annotated[AuthContext, Depends(get_auth_context_required)]
AuthContextWithRateLimit = Annotated[AuthContext, Depends(enforce_rate_limit)]

# Scope-based dependencies
ReadAccess = Annotated[AuthContext, Depends(require_scope("read"))]
WriteAccess = Annotated[AuthContext, Depends(require_scope("write"))]
AdminAccess = Annotated[AuthContext, Depends(require_scope("admin"))]

# Combined dependencies
ReadAccessWithRateLimit = Annotated[AuthContext, Depends(lambda request, context=Depends(enforce_rate_limit): context if context.has_scope("read") else (_ for _ in ()).throw(HTTPException(status_code=403, detail="Read access required")))]


async def get_current_user_id(context: AuthContextRequired) -> str:
    """Get the current user ID from authentication context."""
    if not context.user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User authentication required"
        )
    return context.user_id


async def get_current_api_key(context: AuthContextRequired) -> APIKey:
    """Get the current API key from authentication context."""
    if not context.api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key authentication required"
        )
    return context.api_key


# Utility functions for manual authentication checks
def authenticate_request(request: Request, 
                        authorization: Optional[str] = None,
                        x_api_key: Optional[str] = None) -> AuthContext:
    """Manually authenticate a request (synchronous version)."""
    
    # Try API key authentication first
    if x_api_key:
        api_key_obj = security_manager.validate_api_key(x_api_key)
        if api_key_obj:
            return AuthContext(
                api_key_id=str(api_key_obj.id),
                api_key=api_key_obj,
                scopes=api_key_obj.scopes
            )
    
    # Try JWT token authentication
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        try:
            token_data = verify_token(token)
            return AuthContext(
                user_id=token_data.user_id,
                api_key_id=token_data.api_key_id,
                scopes=token_data.scopes
            )
        except HTTPException:
            pass
    
    return AuthContext()


def check_request_rate_limit(request: Request, context: AuthContext) -> bool:
    """Check if a request is within rate limits."""
    tier_name = context.get_rate_limit_tier()
    tier = get_rate_limit_tier(tier_name)
    
    rate_limit_info = rate_limiter.check_rate_limit(
        request=request,
        tier=tier,
        user_id=context.user_id,
        api_key_id=context.api_key_id
    )
    
    return rate_limit_info.allowed
