"""
Rate limiting implementation using Redis for the AI/LLM News Scraper API.

Provides:
- Token bucket rate limiting
- Sliding window rate limiting
- Per-user and per-API-key rate limiting
- Different rate limit tiers
"""

import time
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Tuple
from uuid import UUID

import redis
from fastapi import HTTPException, status, Request
from pydantic import BaseModel

from config.settings import settings
from api.auth.security import RateLimitTier, get_rate_limit_tier
from utils.logging import get_logger

logger = get_logger(__name__)


class RateLimitInfo(BaseModel):
    """Rate limit information."""
    allowed: bool
    limit: int
    remaining: int
    reset_time: int
    retry_after: Optional[int] = None


class RateLimitExceeded(HTTPException):
    """Rate limit exceeded exception."""
    
    def __init__(self, rate_limit_info: RateLimitInfo):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded",
            headers={
                "X-RateLimit-Limit": str(rate_limit_info.limit),
                "X-RateLimit-Remaining": str(rate_limit_info.remaining),
                "X-RateLimit-Reset": str(rate_limit_info.reset_time),
                "Retry-After": str(rate_limit_info.retry_after) if rate_limit_info.retry_after else "60"
            }
        )


class RateLimiter:
    """Redis-based rate limiter with multiple algorithms."""
    
    def __init__(self):
        try:
            self.redis_client = redis.Redis.from_url(settings.redis.url, decode_responses=True)
            # Test connection
            self.redis_client.ping()
            logger.info("Rate limiter connected to Redis")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def _get_client_id(self, request: Request, user_id: Optional[str] = None, 
                      api_key_id: Optional[str] = None) -> str:
        """Generate a unique client identifier."""
        if api_key_id:
            return f"api_key:{api_key_id}"
        elif user_id:
            return f"user:{user_id}"
        else:
            # Fall back to IP address
            client_ip = request.client.host if request.client else "unknown"
            return f"ip:{client_ip}"
    
    def _sliding_window_check(self, key: str, limit: int, window_seconds: int) -> RateLimitInfo:
        """Sliding window rate limiting algorithm."""
        if not self.redis_client:
            # If Redis is not available, allow all requests
            return RateLimitInfo(
                allowed=True,
                limit=limit,
                remaining=limit,
                reset_time=int(time.time()) + window_seconds
            )
        
        now = time.time()
        window_start = now - window_seconds
        
        pipe = self.redis_client.pipeline()
        
        # Remove old entries
        pipe.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        pipe.zcard(key)
        
        # Add current request
        pipe.zadd(key, {str(now): now})
        
        # Set expiration
        pipe.expire(key, window_seconds)
        
        results = pipe.execute()
        current_count = results[1]
        
        remaining = max(0, limit - current_count - 1)
        reset_time = int(now + window_seconds)
        
        if current_count >= limit:
            # Remove the request we just added since it's not allowed
            self.redis_client.zrem(key, str(now))
            return RateLimitInfo(
                allowed=False,
                limit=limit,
                remaining=0,
                reset_time=reset_time,
                retry_after=int(window_seconds)
            )
        
        return RateLimitInfo(
            allowed=True,
            limit=limit,
            remaining=remaining,
            reset_time=reset_time
        )
    
    def _token_bucket_check(self, key: str, limit: int, window_seconds: int, 
                           burst_limit: int) -> RateLimitInfo:
        """Token bucket rate limiting algorithm."""
        if not self.redis_client:
            return RateLimitInfo(
                allowed=True,
                limit=limit,
                remaining=limit,
                reset_time=int(time.time()) + window_seconds
            )
        
        now = time.time()
        bucket_key = f"{key}:bucket"
        
        # Get current bucket state
        bucket_data = self.redis_client.hmget(bucket_key, ["tokens", "last_refill"])
        tokens = float(bucket_data[0]) if bucket_data[0] else burst_limit
        last_refill = float(bucket_data[1]) if bucket_data[1] else now
        
        # Calculate tokens to add based on time elapsed
        time_elapsed = now - last_refill
        tokens_to_add = (time_elapsed / window_seconds) * limit
        tokens = min(burst_limit, tokens + tokens_to_add)
        
        remaining = int(tokens)
        reset_time = int(now + (burst_limit - tokens) / (limit / window_seconds))
        
        if tokens >= 1:
            # Consume one token
            tokens -= 1
            
            # Update bucket state
            pipe = self.redis_client.pipeline()
            pipe.hmset(bucket_key, {"tokens": tokens, "last_refill": now})
            pipe.expire(bucket_key, window_seconds * 2)  # Keep bucket data longer
            pipe.execute()
            
            return RateLimitInfo(
                allowed=True,
                limit=limit,
                remaining=remaining - 1,
                reset_time=reset_time
            )
        else:
            return RateLimitInfo(
                allowed=False,
                limit=limit,
                remaining=0,
                reset_time=reset_time,
                retry_after=int((1 - tokens) / (limit / window_seconds))
            )
    
    def check_rate_limit(self, request: Request, tier: RateLimitTier,
                        user_id: Optional[str] = None, 
                        api_key_id: Optional[str] = None) -> RateLimitInfo:
        """Check rate limit for a request."""
        client_id = self._get_client_id(request, user_id, api_key_id)
        
        # Check minute limit (sliding window)
        minute_key = f"rate_limit:{client_id}:minute"
        minute_result = self._sliding_window_check(minute_key, tier.requests_per_minute, 60)
        
        if not minute_result.allowed:
            logger.warning(f"Rate limit exceeded (minute): {client_id}")
            return minute_result
        
        # Check hour limit (sliding window)
        hour_key = f"rate_limit:{client_id}:hour"
        hour_result = self._sliding_window_check(hour_key, tier.requests_per_hour, 3600)
        
        if not hour_result.allowed:
            logger.warning(f"Rate limit exceeded (hour): {client_id}")
            return hour_result
        
        # Check day limit (sliding window)
        day_key = f"rate_limit:{client_id}:day"
        day_result = self._sliding_window_check(day_key, tier.requests_per_day, 86400)
        
        if not day_result.allowed:
            logger.warning(f"Rate limit exceeded (day): {client_id}")
            return day_result
        
        # Check burst limit (token bucket)
        burst_key = f"rate_limit:{client_id}:burst"
        burst_result = self._token_bucket_check(
            burst_key, tier.requests_per_minute, 60, tier.burst_limit
        )
        
        if not burst_result.allowed:
            logger.warning(f"Rate limit exceeded (burst): {client_id}")
            return burst_result
        
        # Return the most restrictive limit info
        return RateLimitInfo(
            allowed=True,
            limit=tier.requests_per_minute,
            remaining=min(
                minute_result.remaining,
                hour_result.remaining,
                day_result.remaining,
                burst_result.remaining
            ),
            reset_time=minute_result.reset_time
        )
    
    def get_rate_limit_status(self, request: Request, tier: RateLimitTier,
                             user_id: Optional[str] = None,
                             api_key_id: Optional[str] = None) -> Dict[str, Any]:
        """Get current rate limit status without consuming a request."""
        if not self.redis_client:
            return {
                "minute": {"limit": tier.requests_per_minute, "remaining": tier.requests_per_minute},
                "hour": {"limit": tier.requests_per_hour, "remaining": tier.requests_per_hour},
                "day": {"limit": tier.requests_per_day, "remaining": tier.requests_per_day},
                "burst": {"limit": tier.burst_limit, "remaining": tier.burst_limit}
            }
        
        client_id = self._get_client_id(request, user_id, api_key_id)
        now = time.time()
        
        # Get current counts without modifying them
        minute_count = self.redis_client.zcount(f"rate_limit:{client_id}:minute", now - 60, now)
        hour_count = self.redis_client.zcount(f"rate_limit:{client_id}:hour", now - 3600, now)
        day_count = self.redis_client.zcount(f"rate_limit:{client_id}:day", now - 86400, now)
        
        # Get burst bucket status
        bucket_key = f"rate_limit:{client_id}:burst:bucket"
        bucket_data = self.redis_client.hmget(bucket_key, ["tokens", "last_refill"])
        tokens = float(bucket_data[0]) if bucket_data[0] else tier.burst_limit
        last_refill = float(bucket_data[1]) if bucket_data[1] else now
        
        # Calculate current burst tokens
        time_elapsed = now - last_refill
        tokens_to_add = (time_elapsed / 60) * tier.requests_per_minute
        current_burst_tokens = min(tier.burst_limit, tokens + tokens_to_add)
        
        return {
            "minute": {
                "limit": tier.requests_per_minute,
                "remaining": max(0, tier.requests_per_minute - minute_count)
            },
            "hour": {
                "limit": tier.requests_per_hour,
                "remaining": max(0, tier.requests_per_hour - hour_count)
            },
            "day": {
                "limit": tier.requests_per_day,
                "remaining": max(0, tier.requests_per_day - day_count)
            },
            "burst": {
                "limit": tier.burst_limit,
                "remaining": max(0, int(current_burst_tokens))
            }
        }


# Global rate limiter instance
rate_limiter = RateLimiter()
