"""
Authentication and security utilities for the AI/LLM News Scraper API.

Provides:
- JWT token generation and validation
- Password hashing and verification
- API key management
- Rate limiting utilities
"""

import secrets
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from uuid import UUID, uuid4

from fastapi import HTT<PERSON>Ex<PERSON>, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel

from config.settings import settings
from utils.logging import get_logger

logger = get_logger(__name__)

# Security configuration
SECRET_KEY = settings.api.secret_key if hasattr(settings.api, 'secret_key') else secrets.token_urlsafe(32)
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer()


class Token(BaseModel):
    """Token response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenData(BaseModel):
    """Token data model."""
    user_id: Optional[str] = None
    api_key_id: Optional[str] = None
    scopes: list[str] = []


class APIKey(BaseModel):
    """API Key model."""
    id: UUID
    name: str
    key_hash: str
    user_id: Optional[str] = None
    scopes: list[str] = []
    rate_limit_tier: str = "basic"  # basic, premium, enterprise
    is_active: bool = True
    created_at: datetime
    last_used: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class RateLimitTier(BaseModel):
    """Rate limit tier configuration."""
    name: str
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int


# Rate limit tiers
RATE_LIMIT_TIERS = {
    "basic": RateLimitTier(
        name="basic",
        requests_per_minute=60,
        requests_per_hour=1000,
        requests_per_day=10000,
        burst_limit=10
    ),
    "premium": RateLimitTier(
        name="premium",
        requests_per_minute=300,
        requests_per_hour=10000,
        requests_per_day=100000,
        burst_limit=50
    ),
    "enterprise": RateLimitTier(
        name="enterprise",
        requests_per_minute=1000,
        requests_per_hour=50000,
        requests_per_day=1000000,
        burst_limit=200
    )
}


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create JWT refresh token."""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> TokenData:
    """Verify and decode JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Verify token type
        if payload.get("type") != token_type:
            raise credentials_exception
        
        user_id: str = payload.get("sub")
        api_key_id: str = payload.get("api_key_id")
        scopes: list = payload.get("scopes", [])
        
        if user_id is None and api_key_id is None:
            raise credentials_exception
            
        token_data = TokenData(
            user_id=user_id,
            api_key_id=api_key_id,
            scopes=scopes
        )
        return token_data
        
    except JWTError:
        raise credentials_exception


def generate_api_key() -> str:
    """Generate a new API key."""
    return f"ains_{secrets.token_urlsafe(32)}"


def hash_api_key(api_key: str) -> str:
    """Hash an API key for storage."""
    return get_password_hash(api_key)


def verify_api_key(api_key: str, hashed_key: str) -> bool:
    """Verify an API key against its hash."""
    return verify_password(api_key, hashed_key)


def get_rate_limit_tier(tier_name: str) -> RateLimitTier:
    """Get rate limit configuration for a tier."""
    return RATE_LIMIT_TIERS.get(tier_name, RATE_LIMIT_TIERS["basic"])


def create_token_pair(user_id: Optional[str] = None, api_key_id: Optional[str] = None, 
                     scopes: list[str] = None) -> Token:
    """Create access and refresh token pair."""
    if scopes is None:
        scopes = []
    
    token_data = {
        "sub": user_id,
        "api_key_id": api_key_id,
        "scopes": scopes
    }
    
    access_token = create_access_token(token_data)
    refresh_token = create_refresh_token(token_data)
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


class SecurityManager:
    """Centralized security management."""
    
    def __init__(self):
        self.api_keys: Dict[str, APIKey] = {}
        logger.info("Security manager initialized")
    
    def create_api_key(self, name: str, user_id: Optional[str] = None, 
                      scopes: list[str] = None, tier: str = "basic",
                      expires_days: Optional[int] = None) -> tuple[str, APIKey]:
        """Create a new API key."""
        if scopes is None:
            scopes = ["read"]
        
        api_key = generate_api_key()
        key_hash = hash_api_key(api_key)
        
        expires_at = None
        if expires_days:
            expires_at = datetime.now(timezone.utc) + timedelta(days=expires_days)
        
        api_key_obj = APIKey(
            id=uuid4(),
            name=name,
            key_hash=key_hash,
            user_id=user_id,
            scopes=scopes,
            rate_limit_tier=tier,
            created_at=datetime.now(timezone.utc),
            expires_at=expires_at
        )
        
        self.api_keys[str(api_key_obj.id)] = api_key_obj
        logger.info(f"Created API key: {name}", key_id=str(api_key_obj.id))
        
        return api_key, api_key_obj
    
    def validate_api_key(self, api_key: str) -> Optional[APIKey]:
        """Validate an API key and return the key object."""
        for key_obj in self.api_keys.values():
            if verify_api_key(api_key, key_obj.key_hash):
                if not key_obj.is_active:
                    return None
                
                if key_obj.expires_at and datetime.now(timezone.utc) > key_obj.expires_at:
                    return None
                
                # Update last used timestamp
                key_obj.last_used = datetime.now(timezone.utc)
                return key_obj
        
        return None


# Global security manager instance
security_manager = SecurityManager()
