"""
Main FastAPI application for the AI/LLM News Scraper.
"""

from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

from config.settings import settings
from models.connection import get_db
from utils.logging import get_logger

logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.api.title,
    version=settings.api.version,
    description="API for AI/LLM News Scraper - Aggregating AI news from multiple sources",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    logger.info("Starting AI/LLM News Scraper API", version=settings.api.version)


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    logger.info("Shutting down AI/LLM News Scraper API")


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AI/LLM News Scraper API",
        "version": settings.api.version,
        "status": "running"
    }


@app.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """Health check endpoint."""
    try:
        # Test database connection
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        db_status = "unhealthy"
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "database": db_status,
        "version": settings.api.version
    }


# Include routers (will be added in future phases)
# app.include_router(articles.router, prefix="/api/v1/articles", tags=["articles"])
# app.include_router(sources.router, prefix="/api/v1/sources", tags=["sources"])
# app.include_router(tags.router, prefix="/api/v1/tags", tags=["tags"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug
    )
