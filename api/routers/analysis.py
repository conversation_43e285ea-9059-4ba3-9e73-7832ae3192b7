"""
Content Analysis API router for the AI/LLM News Scraper.

Provides endpoints for:
- Accessing content analysis results
- Triggering analysis for specific articles
- Batch processing operations
- Analysis statistics and insights
"""

from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc

from models.connection import get_db
from models.database import Article, SourceType
from models.schemas import (
    ArticleResponse,
    ContentAnalysisResponse,
    SentimentAnalysisResponse,
    TopicAnalysisResponse,
    TrendAnalysisResponse
)
from processing.base.analyzer import AnalyzerPipeline, AnalysisType
from processing.sentiment.analyzer import SentimentAnalyzer
from processing.topics.categorizer import TopicCategorizer
from processing.trends.detector import TrendDetector
from processing.batch.article_processor import process_unanalyzed_articles, reprocess_all_articles
from scrapers.base.content_parser import Parsed<PERSON>rticle
from utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()

# Global content processor (initialized on first use)
_content_processor = None


def get_content_processor() -> AnalyzerPipeline:
    """Get or initialize the content processing pipeline."""
    global _content_processor
    
    if _content_processor is None:
        _content_processor = AnalyzerPipeline([])
        
        # Initialize analyzers
        sentiment_analyzer = SentimentAnalyzer()
        sentiment_analyzer.initialize()
        _content_processor.add_analyzer(sentiment_analyzer)
        
        topic_categorizer = TopicCategorizer()
        topic_categorizer.initialize()
        _content_processor.add_analyzer(topic_categorizer)
        
        trend_detector = TrendDetector()
        trend_detector.initialize()
        _content_processor.add_analyzer(trend_detector)
        
        logger.info("Content processing pipeline initialized")
    
    return _content_processor


@router.get("/{article_id}", response_model=ContentAnalysisResponse)
async def get_article_analysis(
    article_id: UUID = Path(..., description="Article ID"),
    db: Session = Depends(get_db)
):
    """Get content analysis results for a specific article."""
    try:
        article = db.query(Article).filter(Article.id == article_id).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        if not article.analysis_timestamp:
            raise HTTPException(status_code=404, detail="Article has not been analyzed")
        
        # Build response from stored analysis data
        analysis_response = ContentAnalysisResponse()
        
        # Sentiment analysis
        if article.sentiment_score is not None:
            analysis_response.sentiment = SentimentAnalysisResponse(
                score=article.sentiment_score,
                label=article.sentiment_label or "neutral",
                confidence=article.sentiment_confidence or 0.0
            )
        
        # Topic analysis
        if article.topics_analysis:
            analysis_response.topics = [
                TopicAnalysisResponse(
                    category=topic.get('category', 'unknown'),
                    confidence=topic.get('confidence', 0.0),
                    subcategory=topic.get('subcategory')
                )
                for topic in article.topics_analysis
            ]
        
        # Trend analysis
        if article.trends_analysis:
            analysis_response.trends = [
                TrendAnalysisResponse(
                    trend_type=trend.get('trend_type', 'unknown'),
                    description=trend.get('description', ''),
                    confidence=trend.get('confidence', 0.0),
                    timeframe=trend.get('timeframe')
                )
                for trend in article.trends_analysis
            ]
        
        # Keywords (if available)
        if article.keywords_analysis:
            analysis_response.keywords = article.keywords_analysis
        
        # Overall metadata
        analysis_response.overall_confidence = article.analysis_confidence
        analysis_response.analysis_timestamp = article.analysis_timestamp
        
        logger.info(f"Retrieved analysis for article {article_id}")
        return analysis_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving analysis for article {article_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{article_id}/analyze", response_model=ContentAnalysisResponse)
async def analyze_article(
    article_id: UUID = Path(..., description="Article ID"),
    force_reanalysis: bool = Query(False, description="Force reanalysis even if already analyzed"),
    db: Session = Depends(get_db)
):
    """Trigger content analysis for a specific article."""
    try:
        article = db.query(Article).filter(Article.id == article_id).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        if not article.content:
            raise HTTPException(status_code=400, detail="Article has no content to analyze")
        
        if article.analysis_timestamp and not force_reanalysis:
            raise HTTPException(
                status_code=409, 
                detail="Article already analyzed. Use force_reanalysis=true to reanalyze"
            )
        
        # Convert to ParsedArticle for processing
        parsed_article = ParsedArticle(
            title=article.title,
            content=article.content,
            url=article.url,
            author=article.author,
            published_at=article.published_at,
            source_name=article.source_type.value if article.source_type else "unknown"
        )
        
        # Perform analysis
        processor = get_content_processor()
        analysis = processor.analyze(parsed_article)
        
        # Update article with analysis results
        sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
        if sentiment_result:
            article.sentiment_score = sentiment_result.data.get('score', 0)
            article.sentiment_label = sentiment_result.data.get('label', 'neutral')
            article.sentiment_confidence = sentiment_result.confidence
        
        topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
        if topic_results:
            article.topics_analysis = [
                {
                    'category': result.data.get('category', 'unknown'),
                    'confidence': result.confidence,
                    'subcategory': result.data.get('subcategory', '')
                }
                for result in topic_results
            ]
        
        trend_results = analysis.get_results_by_type(AnalysisType.TREND)
        if trend_results:
            article.trends_analysis = [
                {
                    'trend_type': result.data.get('trend_type', 'unknown'),
                    'description': result.data.get('description', ''),
                    'confidence': result.confidence,
                    'timeframe': result.data.get('timeframe', '')
                }
                for result in trend_results
            ]
        
        article.analysis_confidence = analysis.get_overall_confidence()
        article.analysis_timestamp = datetime.now(timezone.utc)
        article.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(article)
        
        # Return analysis results
        return await get_article_analysis(article_id, db)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing article {article_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sentiment/distribution", response_model=Dict[str, Any])
async def get_sentiment_distribution(
    source_type: Optional[SourceType] = Query(None, description="Filter by source type"),
    days_back: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    db: Session = Depends(get_db)
):
    """Get sentiment distribution statistics."""
    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_back)
        
        query = db.query(Article).filter(
            and_(
                Article.analysis_timestamp.isnot(None),
                Article.scraped_at >= cutoff_date
            )
        )
        
        if source_type:
            query = query.filter(Article.source_type == source_type)
        
        # Get sentiment distribution
        sentiment_counts = {}
        for label in ['positive', 'negative', 'neutral']:
            count = query.filter(Article.sentiment_label == label).count()
            sentiment_counts[label] = count
        
        total_analyzed = sum(sentiment_counts.values())
        
        # Calculate percentages
        sentiment_percentages = {}
        for label, count in sentiment_counts.items():
            sentiment_percentages[label] = (count / max(total_analyzed, 1)) * 100
        
        # Average sentiment score
        avg_sentiment = query.filter(Article.sentiment_score.isnot(None)).with_entities(
            func.avg(Article.sentiment_score)
        ).scalar() or 0.0
        
        return {
            "period_days": days_back,
            "total_analyzed_articles": total_analyzed,
            "sentiment_counts": sentiment_counts,
            "sentiment_percentages": sentiment_percentages,
            "average_sentiment_score": round(float(avg_sentiment), 3),
            "source_type_filter": source_type.value if source_type else None
        }
        
    except Exception as e:
        logger.error(f"Error getting sentiment distribution: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/topics/trending", response_model=List[Dict[str, Any]])
async def get_trending_topics(
    limit: int = Query(20, ge=1, le=100, description="Number of topics to return"),
    days_back: int = Query(7, ge=1, le=365, description="Number of days to look back"),
    source_type: Optional[SourceType] = Query(None, description="Filter by source type"),
    db: Session = Depends(get_db)
):
    """Get trending topics based on frequency and recency."""
    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_back)
        
        query = db.query(Article).filter(
            and_(
                Article.topics_analysis.isnot(None),
                Article.scraped_at >= cutoff_date
            )
        )
        
        if source_type:
            query = query.filter(Article.source_type == source_type)
        
        articles = query.all()
        
        # Count topic frequencies
        topic_counts = {}
        topic_confidences = {}
        
        for article in articles:
            if article.topics_analysis:
                for topic in article.topics_analysis:
                    category = topic.get('category', 'unknown')
                    confidence = topic.get('confidence', 0.0)
                    
                    if category not in topic_counts:
                        topic_counts[category] = 0
                        topic_confidences[category] = []
                    
                    topic_counts[category] += 1
                    topic_confidences[category].append(confidence)
        
        # Calculate average confidence and sort by frequency
        trending_topics = []
        for category, count in topic_counts.items():
            avg_confidence = sum(topic_confidences[category]) / len(topic_confidences[category])
            trending_topics.append({
                "category": category,
                "article_count": count,
                "average_confidence": round(avg_confidence, 3),
                "trend_score": count * avg_confidence  # Simple trending score
            })
        
        # Sort by trend score and limit results
        trending_topics.sort(key=lambda x: x["trend_score"], reverse=True)
        
        return trending_topics[:limit]
        
    except Exception as e:
        logger.error(f"Error getting trending topics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/batch/process", response_model=Dict[str, Any])
async def trigger_batch_processing(
    background_tasks: BackgroundTasks,
    max_articles: int = Query(100, ge=1, le=10000, description="Maximum articles to process"),
    unanalyzed_only: bool = Query(True, description="Process only unanalyzed articles"),
    source_type: Optional[SourceType] = Query(None, description="Filter by source type")
):
    """Trigger batch processing of articles in the background."""
    try:
        def run_batch_processing():
            try:
                if unanalyzed_only:
                    stats = process_unanalyzed_articles(max_articles=max_articles)
                else:
                    stats = reprocess_all_articles(max_articles=max_articles)
                
                logger.info(f"Batch processing completed", stats=stats)
            except Exception as e:
                logger.error(f"Batch processing failed: {e}")
        
        background_tasks.add_task(run_batch_processing)
        
        return {
            "message": "Batch processing started",
            "max_articles": max_articles,
            "unanalyzed_only": unanalyzed_only,
            "source_type_filter": source_type.value if source_type else None,
            "status": "processing"
        }
        
    except Exception as e:
        logger.error(f"Error starting batch processing: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats/analysis", response_model=Dict[str, Any])
async def get_analysis_stats(
    days_back: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    db: Session = Depends(get_db)
):
    """Get comprehensive analysis statistics."""
    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_back)
        
        # Basic analysis coverage
        total_articles = db.query(Article).filter(Article.scraped_at >= cutoff_date).count()
        analyzed_articles = db.query(Article).filter(
            and_(
                Article.analysis_timestamp.isnot(None),
                Article.scraped_at >= cutoff_date
            )
        ).count()
        
        # Analysis by source type
        source_analysis = {}
        for st in SourceType:
            total_source = db.query(Article).filter(
                and_(
                    Article.source_type == st,
                    Article.scraped_at >= cutoff_date
                )
            ).count()
            
            analyzed_source = db.query(Article).filter(
                and_(
                    Article.source_type == st,
                    Article.analysis_timestamp.isnot(None),
                    Article.scraped_at >= cutoff_date
                )
            ).count()
            
            source_analysis[st.value] = {
                "total": total_source,
                "analyzed": analyzed_source,
                "coverage_percentage": (analyzed_source / max(total_source, 1)) * 100
            }
        
        # Recent analysis activity
        recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=24)
        recent_analysis = db.query(Article).filter(
            Article.analysis_timestamp >= recent_cutoff
        ).count()
        
        return {
            "period_days": days_back,
            "total_articles": total_articles,
            "analyzed_articles": analyzed_articles,
            "unanalyzed_articles": total_articles - analyzed_articles,
            "overall_coverage_percentage": (analyzed_articles / max(total_articles, 1)) * 100,
            "source_breakdown": source_analysis,
            "recent_analysis_24h": recent_analysis
        }
        
    except Exception as e:
        logger.error(f"Error getting analysis statistics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
