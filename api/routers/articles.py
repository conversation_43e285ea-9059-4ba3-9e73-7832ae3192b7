"""
Articles API router for the AI/LLM News Scraper.

Provides endpoints for:
- Retrieving articles with filtering and pagination
- Searching articles by content and metadata
- Accessing content analysis results
- Managing article data
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc

from models.connection import get_db
from models.database import Article, SourceType
from models.schemas import (
    ArticleResponse, 
    ArticleUpdate,
    ContentAnalysisResponse,
    SentimentAnalysisResponse,
    TopicAnalysisResponse,
    TrendAnalysisResponse
)
from utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=List[ArticleResponse])
async def get_articles(
    skip: int = Query(0, ge=0, description="Number of articles to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Number of articles to return"),
    source_type: Optional[SourceType] = Query(None, description="Filter by source type"),
    author: Optional[str] = Query(None, description="Filter by author"),
    has_analysis: Optional[bool] = Query(None, description="Filter by analysis status"),
    sentiment_label: Optional[str] = Query(None, description="Filter by sentiment label"),
    min_sentiment_score: Optional[float] = Query(None, ge=-1.0, le=1.0, description="Minimum sentiment score"),
    max_sentiment_score: Optional[float] = Query(None, ge=-1.0, le=1.0, description="Maximum sentiment score"),
    topic_category: Optional[str] = Query(None, description="Filter by topic category"),
    published_after: Optional[datetime] = Query(None, description="Filter articles published after this date"),
    published_before: Optional[datetime] = Query(None, description="Filter articles published before this date"),
    sort_by: str = Query("scraped_at", description="Sort field (scraped_at, published_at, sentiment_score)"),
    sort_order: str = Query("desc", description="Sort order (asc, desc)"),
    db: Session = Depends(get_db)
):
    """
    Retrieve articles with filtering, pagination, and sorting.
    
    Supports various filters including:
    - Source type (Reddit, Twitter)
    - Author information
    - Analysis status and results
    - Sentiment analysis results
    - Topic categorization
    - Publication date ranges
    """
    try:
        query = db.query(Article)
        
        # Apply filters
        if source_type:
            query = query.filter(Article.source_type == source_type)
        
        if author:
            query = query.filter(Article.author.ilike(f"%{author}%"))
        
        if has_analysis is not None:
            if has_analysis:
                query = query.filter(Article.analysis_timestamp.isnot(None))
            else:
                query = query.filter(Article.analysis_timestamp.is_(None))
        
        if sentiment_label:
            query = query.filter(Article.sentiment_label == sentiment_label)
        
        if min_sentiment_score is not None:
            query = query.filter(Article.sentiment_score >= min_sentiment_score)
        
        if max_sentiment_score is not None:
            query = query.filter(Article.sentiment_score <= max_sentiment_score)
        
        if topic_category:
            # Search in topics_analysis JSON field
            query = query.filter(
                func.json_extract(Article.topics_analysis, '$[*].category').like(f'%{topic_category}%')
            )
        
        if published_after:
            query = query.filter(Article.published_at >= published_after)
        
        if published_before:
            query = query.filter(Article.published_at <= published_before)
        
        # Apply sorting
        sort_field = getattr(Article, sort_by, Article.scraped_at)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(asc(sort_field))
        
        # Apply pagination
        articles = query.offset(skip).limit(limit).all()
        
        logger.info(f"Retrieved {len(articles)} articles", skip=skip, limit=limit)
        return articles
        
    except Exception as e:
        logger.error(f"Error retrieving articles: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{article_id}", response_model=ArticleResponse)
async def get_article(
    article_id: UUID = Path(..., description="Article ID"),
    db: Session = Depends(get_db)
):
    """Get a specific article by ID."""
    try:
        article = db.query(Article).filter(Article.id == article_id).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        logger.info(f"Retrieved article {article_id}")
        return article
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving article {article_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{article_id}", response_model=ArticleResponse)
async def update_article(
    article_id: UUID = Path(..., description="Article ID"),
    article_update: ArticleUpdate = ...,
    db: Session = Depends(get_db)
):
    """Update an article."""
    try:
        article = db.query(Article).filter(Article.id == article_id).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        # Update fields
        update_data = article_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(article, field, value)
        
        article.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(article)
        
        logger.info(f"Updated article {article_id}")
        return article
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating article {article_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{article_id}")
async def delete_article(
    article_id: UUID = Path(..., description="Article ID"),
    db: Session = Depends(get_db)
):
    """Delete an article."""
    try:
        article = db.query(Article).filter(Article.id == article_id).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        db.delete(article)
        db.commit()
        
        logger.info(f"Deleted article {article_id}")
        return {"message": "Article deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting article {article_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search/", response_model=List[ArticleResponse])
async def search_articles(
    q: str = Query(..., min_length=1, description="Search query"),
    search_in: str = Query("all", description="Search in: title, content, author, or all"),
    skip: int = Query(0, ge=0, description="Number of articles to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Number of articles to return"),
    source_type: Optional[SourceType] = Query(None, description="Filter by source type"),
    has_analysis: Optional[bool] = Query(None, description="Filter by analysis status"),
    db: Session = Depends(get_db)
):
    """
    Search articles by content, title, or author.
    
    Supports full-text search across article fields with optional filtering.
    """
    try:
        query = db.query(Article)
        
        # Build search conditions
        search_conditions = []
        
        if search_in in ["title", "all"]:
            search_conditions.append(Article.title.ilike(f"%{q}%"))
        
        if search_in in ["content", "all"]:
            search_conditions.append(Article.content.ilike(f"%{q}%"))
        
        if search_in in ["author", "all"]:
            search_conditions.append(Article.author.ilike(f"%{q}%"))
        
        if search_conditions:
            query = query.filter(or_(*search_conditions))
        
        # Apply additional filters
        if source_type:
            query = query.filter(Article.source_type == source_type)
        
        if has_analysis is not None:
            if has_analysis:
                query = query.filter(Article.analysis_timestamp.isnot(None))
            else:
                query = query.filter(Article.analysis_timestamp.is_(None))
        
        # Order by relevance (could be improved with full-text search)
        query = query.order_by(desc(Article.scraped_at))
        
        # Apply pagination
        articles = query.offset(skip).limit(limit).all()
        
        logger.info(f"Search returned {len(articles)} articles", query=q, search_in=search_in)
        return articles
        
    except Exception as e:
        logger.error(f"Error searching articles: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats/", response_model=Dict[str, Any])
async def get_articles_stats(
    source_type: Optional[SourceType] = Query(None, description="Filter by source type"),
    db: Session = Depends(get_db)
):
    """Get statistics about articles in the database."""
    try:
        base_query = db.query(Article)
        
        if source_type:
            base_query = base_query.filter(Article.source_type == source_type)
        
        # Basic counts
        total_articles = base_query.count()
        analyzed_articles = base_query.filter(Article.analysis_timestamp.isnot(None)).count()
        
        # Source type breakdown
        source_stats = {}
        for st in SourceType:
            count = db.query(Article).filter(Article.source_type == st).count()
            source_stats[st.value] = count
        
        # Sentiment breakdown
        sentiment_stats = {}
        sentiment_query = base_query.filter(Article.sentiment_label.isnot(None))
        for label in ['positive', 'negative', 'neutral']:
            count = sentiment_query.filter(Article.sentiment_label == label).count()
            sentiment_stats[label] = count
        
        # Recent activity (last 24 hours)
        recent_cutoff = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        recent_articles = base_query.filter(Article.scraped_at >= recent_cutoff).count()
        
        stats = {
            "total_articles": total_articles,
            "analyzed_articles": analyzed_articles,
            "unanalyzed_articles": total_articles - analyzed_articles,
            "analysis_coverage_percentage": (analyzed_articles / max(total_articles, 1)) * 100,
            "source_breakdown": source_stats,
            "sentiment_breakdown": sentiment_stats,
            "recent_articles_24h": recent_articles
        }
        
        logger.info("Retrieved article statistics")
        return stats
        
    except Exception as e:
        logger.error(f"Error retrieving article statistics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
