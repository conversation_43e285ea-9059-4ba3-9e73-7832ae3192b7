"""
API management endpoints for the AI/LLM News Scraper API.

Provides:
- API key management
- Rate limit monitoring
- Cache statistics
- API analytics
- Health checks
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel, Field

from api.auth.dependencies import get_auth_context_required, require_scope
from api.auth.security import security_manager, get_rate_limit_tier
from api.auth.rate_limiter import rate_limiter
from api.cache import cache_manager
from utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


class CreateAPIKeyRequest(BaseModel):
    """Request model for creating API keys."""
    
    name: str = Field(..., min_length=1, max_length=100, description="API key name")
    scopes: List[str] = Field(default=["read"], description="API key scopes")
    tier: str = Field(default="basic", description="Rate limit tier")
    expires_days: Optional[int] = Field(None, ge=1, le=365, description="Expiration in days")


class APIKeyResponse(BaseModel):
    """Response model for API key information."""
    
    id: str
    name: str
    scopes: List[str]
    rate_limit_tier: str
    is_active: bool
    created_at: datetime
    last_used: Optional[datetime]
    expires_at: Optional[datetime]
    # Note: We don't return the actual key or hash for security


class APIKeyWithSecret(BaseModel):
    """Response model for newly created API key with secret."""
    
    api_key: str
    key_info: APIKeyResponse


class RateLimitStatus(BaseModel):
    """Rate limit status model."""
    
    tier: str
    limits: Dict[str, Any]
    current_usage: Dict[str, Any]


class APIStats(BaseModel):
    """API statistics model."""
    
    total_requests: int
    requests_by_endpoint: Dict[str, int]
    requests_by_hour: Dict[str, int]
    error_rate: float
    average_response_time: float


@router.post("/api-keys", response_model=APIKeyWithSecret)
async def create_api_key(
    request_data: CreateAPIKeyRequest,
    auth = Depends(require_scope("admin"))
):
    """
    Create a new API key.
    
    Requires admin access. Returns the API key secret only once.
    """
    
    try:
        api_key, key_obj = security_manager.create_api_key(
            name=request_data.name,
            user_id=auth.user_id,
            scopes=request_data.scopes,
            tier=request_data.tier,
            expires_days=request_data.expires_days
        )
        
        key_response = APIKeyResponse(
            id=str(key_obj.id),
            name=key_obj.name,
            scopes=key_obj.scopes,
            rate_limit_tier=key_obj.rate_limit_tier,
            is_active=key_obj.is_active,
            created_at=key_obj.created_at,
            last_used=key_obj.last_used,
            expires_at=key_obj.expires_at
        )
        
        logger.info(f"Created API key: {request_data.name}", user_id=auth.user_id)
        
        return APIKeyWithSecret(
            api_key=api_key,
            key_info=key_response
        )
        
    except Exception as e:
        logger.error(f"Failed to create API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create API key"
        )


@router.get("/api-keys", response_model=List[APIKeyResponse])
async def list_api_keys(
    auth = Depends(require_scope("admin"))
):
    """
    List all API keys.
    
    Requires admin access. Does not return key secrets.
    """
    
    try:
        api_keys = []
        for key_obj in security_manager.api_keys.values():
            if not auth.user_id or key_obj.user_id == auth.user_id:
                api_keys.append(APIKeyResponse(
                    id=str(key_obj.id),
                    name=key_obj.name,
                    scopes=key_obj.scopes,
                    rate_limit_tier=key_obj.rate_limit_tier,
                    is_active=key_obj.is_active,
                    created_at=key_obj.created_at,
                    last_used=key_obj.last_used,
                    expires_at=key_obj.expires_at
                ))
        
        return api_keys
        
    except Exception as e:
        logger.error(f"Failed to list API keys: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list API keys"
        )


@router.delete("/api-keys/{key_id}")
async def revoke_api_key(
    key_id: UUID,
    auth = Depends(require_scope("admin"))
):
    """
    Revoke an API key.
    
    Requires admin access.
    """
    
    try:
        key_str = str(key_id)
        if key_str in security_manager.api_keys:
            key_obj = security_manager.api_keys[key_str]
            
            # Check ownership if not admin
            if auth.user_id and key_obj.user_id != auth.user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Cannot revoke API key owned by another user"
                )
            
            key_obj.is_active = False
            logger.info(f"Revoked API key: {key_obj.name}", user_id=auth.user_id)
            
            return {"message": "API key revoked successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to revoke API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke API key"
        )


@router.get("/rate-limits", response_model=RateLimitStatus)
async def get_rate_limit_status(
    request: Request,
    auth = Depends(get_auth_context_required)
):
    """
    Get current rate limit status for the authenticated user/API key.
    """
    
    try:
        tier_name = auth.get_rate_limit_tier()
        tier = get_rate_limit_tier(tier_name)
        
        current_usage = rate_limiter.get_rate_limit_status(
            request=request,
            tier=tier,
            user_id=auth.user_id,
            api_key_id=auth.api_key_id
        )
        
        return RateLimitStatus(
            tier=tier_name,
            limits={
                "requests_per_minute": tier.requests_per_minute,
                "requests_per_hour": tier.requests_per_hour,
                "requests_per_day": tier.requests_per_day,
                "burst_limit": tier.burst_limit
            },
            current_usage=current_usage
        )
        
    except Exception as e:
        logger.error(f"Failed to get rate limit status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get rate limit status"
        )


@router.get("/cache/stats")
async def get_cache_stats(
    auth = Depends(require_scope("admin"))
):
    """
    Get cache statistics.
    
    Requires admin access.
    """
    
    try:
        stats = cache_manager.get_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get cache statistics"
        )


@router.delete("/cache")
async def clear_cache(
    pattern: Optional[str] = None,
    auth = Depends(require_scope("admin"))
):
    """
    Clear cache entries.
    
    Requires admin access. If pattern is provided, only matching keys are cleared.
    """
    
    try:
        if pattern:
            deleted = cache_manager.delete_pattern(f"ains:cache:{pattern}:*")
            message = f"Cleared {deleted} cache entries matching pattern: {pattern}"
        else:
            deleted = cache_manager.delete_pattern("ains:cache:*")
            message = f"Cleared {deleted} cache entries"
        
        logger.info(message, user_id=auth.user_id)
        return {"message": message, "deleted_count": deleted}
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )


@router.get("/health/detailed")
async def detailed_health_check():
    """
    Detailed health check including all system components.
    """
    
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "components": {}
    }
    
    # Check Redis connection
    try:
        if cache_manager.redis_client:
            cache_manager.redis_client.ping()
            health_status["components"]["redis"] = {"status": "healthy"}
        else:
            health_status["components"]["redis"] = {"status": "unavailable"}
    except Exception as e:
        health_status["components"]["redis"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "degraded"
    
    # Check rate limiter
    try:
        if rate_limiter.redis_client:
            rate_limiter.redis_client.ping()
            health_status["components"]["rate_limiter"] = {"status": "healthy"}
        else:
            health_status["components"]["rate_limiter"] = {"status": "unavailable"}
    except Exception as e:
        health_status["components"]["rate_limiter"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "degraded"
    
    # Check security manager
    health_status["components"]["security"] = {
        "status": "healthy",
        "active_api_keys": len([k for k in security_manager.api_keys.values() if k.is_active])
    }
    
    return health_status
