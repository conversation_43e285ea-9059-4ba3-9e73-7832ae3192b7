"""
Enhanced search endpoints for the AI/LLM News Scraper API.

Provides:
- Advanced article search with full-text search
- Comprehensive filtering by Phase 8 analysis results
- Multiple pagination strategies
- Relevance scoring and ranking
- Search analytics and trending queries
"""

from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Union
from uuid import UUID

from fastapi import APIRouter, Depends, Query, HTTPException, status, Request
from sqlalchemy import and_, or_, desc, asc, func, text, Float
from sqlalchemy.orm import Session, joinedload
from pydantic import BaseModel, Field

from models.connection import get_db
from models.database import Article, Source, Tag
from models.schemas import ArticleResponse, PaginatedResponse
from api.auth.dependencies import enforce_rate_limit
from api.cache import cache_manager, cached_response
from utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


class SearchFilters(BaseModel):
    """Advanced search filters."""
    
    # Text search
    query: Optional[str] = Field(None, description="Search query for title, content, and author")
    
    # Date filters
    date_from: Optional[datetime] = Field(None, description="Start date for articles")
    date_to: Optional[datetime] = Field(None, description="End date for articles")
    published_from: Optional[datetime] = Field(None, description="Start date for published articles")
    published_to: Optional[datetime] = Field(None, description="End date for published articles")
    
    # Source filters
    source_ids: Optional[List[UUID]] = Field(None, description="Filter by specific source IDs")
    source_types: Optional[List[str]] = Field(None, description="Filter by source types")
    source_names: Optional[List[str]] = Field(None, description="Filter by source names")
    
    # Content analysis filters (Phase 8)
    min_analysis_confidence: Optional[float] = Field(None, ge=0, le=1, description="Minimum analysis confidence score")
    max_analysis_confidence: Optional[float] = Field(None, ge=0, le=1, description="Maximum analysis confidence score")
    
    # Sentiment filters
    min_sentiment: Optional[float] = Field(None, ge=-1, le=1, description="Minimum sentiment score")
    max_sentiment: Optional[float] = Field(None, ge=-1, le=1, description="Maximum sentiment score")
    sentiment_labels: Optional[List[str]] = Field(None, description="Filter by sentiment labels")
    
    # Topic and trend filters
    topics: Optional[List[str]] = Field(None, description="Filter by topics")
    trends: Optional[List[str]] = Field(None, description="Filter by trends")
    keywords: Optional[List[str]] = Field(None, description="Filter by keywords")
    
    # Tag filters
    tag_names: Optional[List[str]] = Field(None, description="Filter by tag names")
    min_tag_confidence: Optional[float] = Field(None, ge=0, le=1, description="Minimum tag confidence")
    
    # Engagement filters
    min_engagement_score: Optional[float] = Field(None, ge=0, description="Minimum engagement score")
    max_engagement_score: Optional[float] = Field(None, ge=0, description="Maximum engagement score")
    
    # Processing status filters
    has_analysis: Optional[bool] = Field(None, description="Filter by analysis completion")
    has_summary: Optional[bool] = Field(None, description="Filter by summary availability")
    
    # Content length filters
    min_content_length: Optional[int] = Field(None, ge=0, description="Minimum content length")
    max_content_length: Optional[int] = Field(None, ge=0, description="Maximum content length")


class SortOptions(BaseModel):
    """Sorting options for search results."""
    
    field: str = Field("scraped_at", description="Field to sort by")
    direction: str = Field("desc", description="Sort direction: asc or desc")
    
    class Config:
        schema_extra = {
            "example": {
                "field": "scraped_at",
                "direction": "desc"
            }
        }


class PaginationOptions(BaseModel):
    """Pagination options."""
    
    # Offset-based pagination
    skip: Optional[int] = Field(None, ge=0, description="Number of records to skip")
    limit: Optional[int] = Field(None, ge=1, le=1000, description="Maximum number of records to return")
    
    # Cursor-based pagination
    cursor: Optional[str] = Field(None, description="Cursor for pagination")
    
    # Page-based pagination
    page: Optional[int] = Field(None, ge=1, description="Page number (1-based)")
    page_size: Optional[int] = Field(None, ge=1, le=1000, description="Number of records per page")


class SearchRequest(BaseModel):
    """Complete search request."""
    
    filters: SearchFilters = Field(default_factory=SearchFilters)
    sort: List[SortOptions] = Field(default_factory=lambda: [SortOptions()])
    pagination: PaginationOptions = Field(default_factory=PaginationOptions)
    include_facets: bool = Field(False, description="Include faceted search results")
    include_highlights: bool = Field(False, description="Include search term highlights")


class FacetResult(BaseModel):
    """Faceted search result."""
    
    field: str
    values: List[Dict[str, Any]]


class SearchResponse(BaseModel):
    """Enhanced search response."""
    
    articles: List[ArticleResponse]
    total_count: int
    page_info: Dict[str, Any]
    facets: Optional[List[FacetResult]] = None
    search_time_ms: int
    query_id: Optional[str] = None


def build_search_query(db: Session, filters: SearchFilters):
    """Build SQLAlchemy query from search filters."""
    
    query = db.query(Article).options(
        joinedload(Article.source),
        joinedload(Article.tags)
    )
    
    conditions = []
    
    # Text search
    if filters.query:
        search_terms = filters.query.split()
        text_conditions = []
        
        for term in search_terms:
            term_pattern = f"%{term}%"
            text_conditions.append(
                or_(
                    Article.title.ilike(term_pattern),
                    Article.content.ilike(term_pattern),
                    Article.author.ilike(term_pattern)
                )
            )
        
        if text_conditions:
            conditions.append(and_(*text_conditions))
    
    # Date filters
    if filters.date_from:
        conditions.append(Article.scraped_at >= filters.date_from)
    if filters.date_to:
        conditions.append(Article.scraped_at <= filters.date_to)
    if filters.published_from:
        conditions.append(Article.published_at >= filters.published_from)
    if filters.published_to:
        conditions.append(Article.published_at <= filters.published_to)
    
    # Source filters
    if filters.source_ids:
        conditions.append(Article.source_id.in_(filters.source_ids))
    if filters.source_types:
        conditions.append(Article.source.has(Source.source_type.in_(filters.source_types)))
    if filters.source_names:
        conditions.append(Article.source.has(Source.name.in_(filters.source_names)))
    
    # Analysis filters (Phase 8)
    if filters.min_analysis_confidence is not None:
        conditions.append(Article.analysis_confidence >= filters.min_analysis_confidence)
    if filters.max_analysis_confidence is not None:
        conditions.append(Article.analysis_confidence <= filters.max_analysis_confidence)
    
    # Sentiment filters
    if filters.min_sentiment is not None:
        conditions.append(Article.sentiment_score >= filters.min_sentiment)
    if filters.max_sentiment is not None:
        conditions.append(Article.sentiment_score <= filters.max_sentiment)
    if filters.sentiment_labels:
        conditions.append(Article.sentiment_label.in_(filters.sentiment_labels))
    
    # JSON field filters for topics, trends, keywords
    if filters.topics:
        for topic in filters.topics:
            conditions.append(
                func.json_extract_path_text(Article.topics_analysis, 'topics').ilike(f'%{topic}%')
            )
    
    if filters.trends:
        for trend in filters.trends:
            conditions.append(
                func.json_extract_path_text(Article.trends_analysis, 'trends').ilike(f'%{trend}%')
            )
    
    if filters.keywords:
        for keyword in filters.keywords:
            conditions.append(
                func.json_extract_path_text(Article.keywords_analysis, 'keywords').ilike(f'%{keyword}%')
            )
    
    # Tag filters
    if filters.tag_names:
        query = query.join(Article.tags).filter(Tag.name.in_(filters.tag_names))
        if filters.min_tag_confidence is not None:
            # This would require a proper many-to-many association table with confidence
            pass
    
    # Engagement filters
    if filters.min_engagement_score is not None:
        conditions.append(
            func.coalesce(
                func.cast(func.json_extract_path_text(Article.engagement_metrics, 'total_score'), Float),
                0
            ) >= filters.min_engagement_score
        )
    if filters.max_engagement_score is not None:
        conditions.append(
            func.coalesce(
                func.cast(func.json_extract_path_text(Article.engagement_metrics, 'total_score'), Float),
                0
            ) <= filters.max_engagement_score
        )
    
    # Processing status filters
    if filters.has_analysis is not None:
        if filters.has_analysis:
            conditions.append(Article.topics_analysis.isnot(None))
        else:
            conditions.append(Article.topics_analysis.is_(None))
    
    if filters.has_summary is not None:
        if filters.has_summary:
            conditions.append(Article.summary.isnot(None))
        else:
            conditions.append(Article.summary.is_(None))
    
    # Content length filters
    if filters.min_content_length is not None:
        conditions.append(func.length(Article.content) >= filters.min_content_length)
    if filters.max_content_length is not None:
        conditions.append(func.length(Article.content) <= filters.max_content_length)
    
    # Apply all conditions
    if conditions:
        query = query.filter(and_(*conditions))
    
    return query


def apply_sorting(query, sort_options: List[SortOptions]):
    """Apply sorting to the query."""

    sort_mapping = {
        "scraped_at": Article.scraped_at,
        "published_at": Article.published_at,
        "title": Article.title,
        "author": Article.author,
        "sentiment_score": Article.sentiment_score,
        "analysis_confidence": Article.analysis_confidence,
        "content_length": func.length(Article.content),
        "engagement_score": func.coalesce(
            func.cast(func.json_extract_path_text(Article.engagement_metrics, 'total_score'), Float),
            0
        )
    }

    for sort_option in sort_options:
        if sort_option.field in sort_mapping:
            sort_column = sort_mapping[sort_option.field]
            if sort_option.direction.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))

    return query


def apply_pagination(query, pagination: PaginationOptions):
    """Apply pagination to the query."""

    # Handle different pagination strategies
    if pagination.cursor:
        # Cursor-based pagination (for large datasets)
        # This is a simplified implementation
        # In production, you'd decode the cursor to get the last seen values
        pass
    elif pagination.page and pagination.page_size:
        # Page-based pagination
        skip = (pagination.page - 1) * pagination.page_size
        query = query.offset(skip).limit(pagination.page_size)
    elif pagination.skip is not None and pagination.limit is not None:
        # Offset-based pagination
        query = query.offset(pagination.skip).limit(pagination.limit)
    else:
        # Default pagination
        query = query.offset(0).limit(50)

    return query


def generate_facets(db: Session, base_query, filters: SearchFilters) -> List[FacetResult]:
    """Generate faceted search results."""

    facets = []

    # Source type facets
    source_facet_query = base_query.join(Source).with_entities(
        Source.type,
        func.count(Article.id).label('count')
    ).group_by(Source.type)

    source_facets = source_facet_query.all()
    if source_facets:
        facets.append(FacetResult(
            field="source_type",
            values=[{"value": item[0], "count": item[1]} for item in source_facets]
        ))

    # Sentiment label facets
    sentiment_facet_query = base_query.with_entities(
        Article.sentiment_label,
        func.count(Article.id).label('count')
    ).filter(Article.sentiment_label.isnot(None)).group_by(Article.sentiment_label)

    sentiment_facets = sentiment_facet_query.all()
    if sentiment_facets:
        facets.append(FacetResult(
            field="sentiment_label",
            values=[{"value": item[0], "count": item[1]} for item in sentiment_facets]
        ))

    # Date range facets (by month) - SQLite compatible
    date_facet_query = base_query.with_entities(
        func.strftime('%Y-%m', Article.scraped_at).label('month'),
        func.count(Article.id).label('count')
    ).group_by(func.strftime('%Y-%m', Article.scraped_at)).order_by(desc('month'))

    date_facets = date_facet_query.limit(12).all()  # Last 12 months
    if date_facets:
        facets.append(FacetResult(
            field="created_month",
            values=[{"value": item[0], "count": item[1]} for item in date_facets]
        ))

    return facets


@router.post("/search", response_model=SearchResponse)
async def search_articles(
    search_request: SearchRequest,
    request: Request,
    db: Session = Depends(get_db),
    auth = Depends(enforce_rate_limit)
):
    """
    Advanced article search with comprehensive filtering and faceting.

    Features:
    - Full-text search across title, content, and author
    - Advanced filtering by Phase 8 analysis results
    - Multiple sorting options
    - Flexible pagination strategies
    - Faceted search results
    - Search analytics tracking
    """

    start_time = datetime.now()

    # Check cache first
    cache_params = search_request.dict()
    cached_result = cache_manager.get_cached_search_results(cache_params)
    if cached_result:
        logger.debug("Returning cached search results")
        return SearchResponse(**cached_result)

    try:
        # Build the base query
        base_query = build_search_query(db, search_request.filters)

        # Get total count before pagination
        total_count = base_query.count()

        # Apply sorting
        sorted_query = apply_sorting(base_query, search_request.sort)

        # Apply pagination
        paginated_query = apply_pagination(sorted_query, search_request.pagination)

        # Execute query
        articles = paginated_query.all()

        # Convert to response models
        article_responses = [ArticleResponse.from_orm(article) for article in articles]

        # Generate facets if requested
        facets = None
        if search_request.include_facets:
            facets = generate_facets(db, base_query, search_request.filters)

        # Calculate pagination info
        page_info = {}
        if search_request.pagination.page and search_request.pagination.page_size:
            total_pages = (total_count + search_request.pagination.page_size - 1) // search_request.pagination.page_size
            page_info = {
                "current_page": search_request.pagination.page,
                "page_size": search_request.pagination.page_size,
                "total_pages": total_pages,
                "has_next": search_request.pagination.page < total_pages,
                "has_previous": search_request.pagination.page > 1
            }
        else:
            skip = search_request.pagination.skip or 0
            limit = search_request.pagination.limit or 50
            page_info = {
                "skip": skip,
                "limit": limit,
                "has_more": skip + limit < total_count
            }

        # Calculate search time
        search_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)

        # Log search analytics (simplified)
        logger.info(
            "Article search performed",
            query=search_request.filters.query,
            total_results=total_count,
            search_time_ms=search_time_ms,
            user_id=auth.user_id,
            api_key_id=auth.api_key_id
        )

        response = SearchResponse(
            articles=article_responses,
            total_count=total_count,
            page_info=page_info,
            facets=facets,
            search_time_ms=search_time_ms
        )

        # Cache the response
        cache_manager.cache_search_results(cache_params, response.dict())

        return response

    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search operation failed"
        )


@router.get("/", response_model=List[ArticleResponse])
async def get_articles_enhanced(
    request: Request,
    db: Session = Depends(get_db),
    auth = Depends(enforce_rate_limit),

    # Text search
    q: Optional[str] = Query(None, description="Search query"),

    # Pagination
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Maximum number of records"),

    # Sorting
    sort_by: str = Query("scraped_at", description="Field to sort by"),
    sort_order: str = Query("desc", description="Sort order: asc or desc"),

    # Date filters
    date_from: Optional[datetime] = Query(None, description="Start date"),
    date_to: Optional[datetime] = Query(None, description="End date"),

    # Source filters
    source_type: Optional[str] = Query(None, description="Source type"),
    source_name: Optional[str] = Query(None, description="Source name"),

    # Analysis filters
    min_relevance: Optional[float] = Query(None, ge=0, le=1, description="Minimum relevance score"),
    min_quality: Optional[float] = Query(None, ge=0, le=1, description="Minimum quality score"),
    sentiment: Optional[str] = Query(None, description="Sentiment label"),

    # Processing filters
    has_analysis: Optional[bool] = Query(None, description="Has analysis"),
    has_summary: Optional[bool] = Query(None, description="Has summary")
):
    """
    Enhanced article listing with comprehensive filtering.

    This endpoint provides a simplified interface for common search operations
    while maintaining backward compatibility with the existing API.
    """

    # Build search request from query parameters
    filters = SearchFilters(
        query=q,
        date_from=date_from,
        date_to=date_to,
        source_types=[source_type] if source_type else None,
        source_names=[source_name] if source_name else None,
        min_analysis_confidence=min_relevance,
        sentiment_labels=[sentiment] if sentiment else None,
        has_analysis=has_analysis,
        has_summary=has_summary
    )

    sort_options = [SortOptions(field=sort_by, direction=sort_order)]
    pagination = PaginationOptions(skip=skip, limit=limit)

    search_request = SearchRequest(
        filters=filters,
        sort=sort_options,
        pagination=pagination
    )

    # Use the main search function
    search_response = await search_articles(search_request, request, db, auth)
    return search_response.articles


@router.get("/facets")
async def get_search_facets(
    q: Optional[str] = Query(None, description="Search query to filter facets"),
    db: Session = Depends(get_db),
    auth = Depends(enforce_rate_limit)
):
    """
    Get available facets for search filtering.

    Returns aggregated counts for different filter categories
    to help users understand available filtering options.
    """

    # Check cache first
    cached_facets = cache_manager.get_cached_facets(q)
    if cached_facets:
        logger.debug("Returning cached facets")
        return cached_facets

    filters = SearchFilters(query=q) if q else SearchFilters()
    base_query = build_search_query(db, filters)
    facets = generate_facets(db, base_query, filters)

    result = {"facets": facets}

    # Cache the result
    cache_manager.cache_facets(q, result)

    return result


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., min_length=2, description="Partial search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of suggestions"),
    db: Session = Depends(get_db),
    auth = Depends(enforce_rate_limit)
):
    """
    Get search suggestions based on partial query.

    Provides autocomplete suggestions for:
    - Article titles
    - Author names
    - Topics and trends
    - Keywords
    """

    # Check cache first
    cached_suggestions = cache_manager.get_cached_suggestions(q)
    if cached_suggestions:
        logger.debug("Returning cached suggestions")
        return cached_suggestions

    suggestions = []

    # Title suggestions
    title_suggestions = db.query(Article.title).filter(
        Article.title.ilike(f"%{q}%")
    ).distinct().limit(limit // 4).all()

    suggestions.extend([
        {"type": "title", "text": title[0], "category": "Articles"}
        for title in title_suggestions
    ])

    # Author suggestions
    author_suggestions = db.query(Article.author).filter(
        and_(
            Article.author.ilike(f"%{q}%"),
            Article.author.isnot(None)
        )
    ).distinct().limit(limit // 4).all()

    suggestions.extend([
        {"type": "author", "text": author[0], "category": "Authors"}
        for author in author_suggestions
    ])

    # Topic suggestions (from JSON analysis)
    # This is a simplified version - in production you'd want a proper search index
    topic_articles = db.query(Article.topics_analysis).filter(
        Article.topics_analysis.isnot(None)
    ).limit(100).all()

    topics = set()
    for article in topic_articles:
        if article[0] and isinstance(article[0], dict):
            article_topics = article[0].get('topics', [])
            for topic in article_topics:
                if isinstance(topic, str) and q.lower() in topic.lower():
                    topics.add(topic)
                elif isinstance(topic, dict) and 'name' in topic:
                    if q.lower() in topic['name'].lower():
                        topics.add(topic['name'])

    suggestions.extend([
        {"type": "topic", "text": topic, "category": "Topics"}
        for topic in list(topics)[:limit // 4]
    ])

    result = {"suggestions": suggestions[:limit]}

    # Cache the result
    cache_manager.cache_suggestions(q, result)

    return result


@router.get("/trending")
async def get_trending_searches(
    period: str = Query("24h", description="Time period: 1h, 24h, 7d, 30d"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of trending items"),
    db: Session = Depends(get_db),
    auth = Depends(enforce_rate_limit)
):
    """
    Get trending search terms and topics.

    Returns popular search queries and emerging topics
    based on recent search activity and article analysis.
    """

    # Check cache first
    cached_trending = cache_manager.get_cached_trending(period)
    if cached_trending:
        logger.debug("Returning cached trending data")
        return cached_trending

    # Calculate time window
    now = datetime.now(timezone.utc)
    if period == "1h":
        since = now - timedelta(hours=1)
    elif period == "24h":
        since = now - timedelta(days=1)
    elif period == "7d":
        since = now - timedelta(days=7)
    elif period == "30d":
        since = now - timedelta(days=30)
    else:
        since = now - timedelta(days=1)

    # Get trending topics from recent articles
    trending_topics = []
    recent_articles = db.query(Article.topics_analysis).filter(
        and_(
            Article.scraped_at >= since,
            Article.topics_analysis.isnot(None)
        )
    ).all()

    topic_counts = {}
    for article in recent_articles:
        if article[0] and isinstance(article[0], dict):
            topics = article[0].get('topics', [])
            for topic in topics:
                topic_name = topic if isinstance(topic, str) else topic.get('name', '')
                if topic_name:
                    topic_counts[topic_name] = topic_counts.get(topic_name, 0) + 1

    # Sort by frequency and take top items
    sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
    trending_topics = [
        {"term": topic, "count": count, "type": "topic"}
        for topic, count in sorted_topics[:limit]
    ]

    result = {
        "period": period,
        "trending": trending_topics,
        "generated_at": now.isoformat()
    }

    # Cache the result
    cache_manager.cache_trending(period, result)

    return result
