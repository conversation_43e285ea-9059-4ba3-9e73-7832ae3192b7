"""
Configuration settings for the web scraping framework.
"""

from typing import List, Dict, Any
from dataclasses import dataclass, field
from pydantic import Field
from pydantic_settings import BaseSettings

from scrapers.base.scraper import ScraperConfig
from scrapers.base.rate_limiter import RateLimit


class ScrapingSettings(BaseSettings):
    """Settings for the scraping framework."""
    
    # General scraping settings
    max_articles_per_source: int = Field(default=50, description="Maximum articles to scrape per source")
    scraping_interval_hours: int = Field(default=6, description="Hours between scraping runs")
    enable_duplicate_detection: bool = Field(default=True, description="Enable duplicate article detection")
    similarity_threshold: float = Field(default=0.85, description="Similarity threshold for duplicate detection")
    
    # Rate limiting settings
    default_requests_per_second: float = Field(default=1.0, description="Default requests per second")
    default_delay_between_requests: float = Field(default=1.0, description="Default delay between requests")
    respect_robots_txt: bool = Field(default=True, description="Respect robots.txt files")
    
    # Request settings
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum request retries")
    verify_ssl: bool = Field(default=True, description="Verify SSL certificates")
    
    # Content filtering
    min_content_length: int = Field(default=100, description="Minimum content length for articles")
    max_content_length: int = Field(default=50000, description="Maximum content length for articles")
    
    # Proxy settings
    enable_proxy_rotation: bool = Field(default=False, description="Enable proxy rotation")
    proxy_list: List[str] = Field(default_factory=list, description="List of proxy URLs")
    
    # User agent settings
    rotate_user_agents: bool = Field(default=True, description="Rotate user agents")
    prefer_desktop_agents: bool = Field(default=True, description="Prefer desktop user agents")
    
    class Config:
        env_prefix = "SCRAPER_"
        case_sensitive = False


@dataclass
class SourceConfig:
    """Configuration for a specific scraping source."""
    name: str
    base_url: str
    scraper_type: str  # 'news', 'reddit', 'twitter', etc.
    enabled: bool = True
    rate_limit: RateLimit = field(default_factory=RateLimit)
    custom_selectors: Dict[str, List[str]] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    cookies: Dict[str, str] = field(default_factory=dict)
    priority: int = 1  # Higher priority sources are scraped first
    tags: List[str] = field(default_factory=list)


# Predefined source configurations
NEWS_SOURCES = [
    SourceConfig(
        name="techcrunch",
        base_url="https://techcrunch.com",
        scraper_type="news",
        rate_limit=RateLimit(delay_between_requests=2.0),
        priority=5,
        tags=["tech", "startup", "ai"]
    ),
    SourceConfig(
        name="venturebeat",
        base_url="https://venturebeat.com",
        scraper_type="news", 
        rate_limit=RateLimit(delay_between_requests=2.0),
        priority=4,
        tags=["tech", "ai", "business"]
    ),
    SourceConfig(
        name="theverge",
        base_url="https://www.theverge.com",
        scraper_type="news",
        rate_limit=RateLimit(delay_between_requests=2.0),
        priority=4,
        tags=["tech", "consumer"]
    ),
    SourceConfig(
        name="ars_technica",
        base_url="https://arstechnica.com",
        scraper_type="news",
        rate_limit=RateLimit(delay_between_requests=3.0),
        priority=3,
        tags=["tech", "science"]
    ),
    SourceConfig(
        name="wired",
        base_url="https://www.wired.com",
        scraper_type="news",
        rate_limit=RateLimit(delay_between_requests=3.0),
        priority=3,
        tags=["tech", "culture", "ai"]
    ),
    SourceConfig(
        name="mit_tech_review",
        base_url="https://www.technologyreview.com",
        scraper_type="news",
        rate_limit=RateLimit(delay_between_requests=4.0),
        priority=5,
        tags=["ai", "research", "science"]
    )
]

REDDIT_SOURCES = [
    SourceConfig(
        name="reddit_ml",
        base_url="https://www.reddit.com",
        scraper_type="reddit",
        rate_limit=RateLimit(delay_between_requests=1.0),
        priority=4,
        tags=["reddit", "ml", "research"],
        custom_selectors={
            "subreddits": ["MachineLearning", "deeplearning", "LanguageTechnology"]
        }
    ),
    SourceConfig(
        name="reddit_ai",
        base_url="https://www.reddit.com", 
        scraper_type="reddit",
        rate_limit=RateLimit(delay_between_requests=1.0),
        priority=3,
        tags=["reddit", "ai", "discussion"],
        custom_selectors={
            "subreddits": ["artificial", "singularity", "OpenAI", "ChatGPT"]
        }
    ),
    SourceConfig(
        name="reddit_programming",
        base_url="https://www.reddit.com",
        scraper_type="reddit", 
        rate_limit=RateLimit(delay_between_requests=1.0),
        priority=2,
        tags=["reddit", "programming", "tech"],
        custom_selectors={
            "subreddits": ["programming", "compsci", "technology"]
        }
    )
]


class ScrapingConfigManager:
    """Manager for scraping configurations."""
    
    def __init__(self):
        self.settings = ScrapingSettings()
        self.news_sources = NEWS_SOURCES.copy()
        self.reddit_sources = REDDIT_SOURCES.copy()
        self.custom_sources = []
    
    def get_all_sources(self) -> List[SourceConfig]:
        """Get all configured sources."""
        all_sources = []
        all_sources.extend(self.news_sources)
        all_sources.extend(self.reddit_sources)
        all_sources.extend(self.custom_sources)
        
        # Filter enabled sources and sort by priority
        enabled_sources = [s for s in all_sources if s.enabled]
        return sorted(enabled_sources, key=lambda s: s.priority, reverse=True)
    
    def get_sources_by_type(self, scraper_type: str) -> List[SourceConfig]:
        """Get sources by scraper type."""
        all_sources = self.get_all_sources()
        return [s for s in all_sources if s.scraper_type == scraper_type]
    
    def add_custom_source(self, source: SourceConfig):
        """Add a custom source configuration."""
        self.custom_sources.append(source)
    
    def disable_source(self, source_name: str):
        """Disable a source by name."""
        for source_list in [self.news_sources, self.reddit_sources, self.custom_sources]:
            for source in source_list:
                if source.name == source_name:
                    source.enabled = False
                    break
    
    def enable_source(self, source_name: str):
        """Enable a source by name."""
        for source_list in [self.news_sources, self.reddit_sources, self.custom_sources]:
            for source in source_list:
                if source.name == source_name:
                    source.enabled = True
                    break
    
    def get_source_config(self, source_name: str) -> SourceConfig:
        """Get configuration for a specific source."""
        all_sources = []
        all_sources.extend(self.news_sources)
        all_sources.extend(self.reddit_sources)
        all_sources.extend(self.custom_sources)
        
        for source in all_sources:
            if source.name == source_name:
                return source
        
        raise ValueError(f"Source '{source_name}' not found")
    
    def create_scraper_config(self, source: SourceConfig) -> ScraperConfig:
        """Create a ScraperConfig from a SourceConfig."""
        return ScraperConfig(
            name=source.name,
            base_url=source.base_url,
            rate_limit=source.rate_limit,
            timeout=self.settings.request_timeout,
            max_retries=self.settings.max_retries,
            headers=source.headers,
            cookies=source.cookies,
            verify_ssl=self.settings.verify_ssl
        )
    
    def get_ai_keywords(self) -> List[str]:
        """Get list of AI/ML keywords for content filtering."""
        return [
            'artificial intelligence', 'machine learning', 'deep learning',
            'neural network', 'llm', 'large language model', 'gpt', 'bert',
            'transformer', 'nlp', 'natural language processing', 'computer vision',
            'reinforcement learning', 'ai', 'ml', 'chatgpt', 'openai', 'anthropic',
            'google ai', 'deepmind', 'tensorflow', 'pytorch', 'hugging face',
            'generative ai', 'foundation model', 'multimodal', 'diffusion model',
            'stable diffusion', 'midjourney', 'dall-e', 'claude', 'bard',
            'llama', 'alpaca', 'vicuna', 'palm', 'lamda', 'chinchilla',
            'gato', 'flamingo', 'imagen', 'parti', 'minerva', 'pathways'
        ]
    
    def update_settings(self, **kwargs):
        """Update scraping settings."""
        for key, value in kwargs.items():
            if hasattr(self.settings, key):
                setattr(self.settings, key, value)
    
    def export_config(self) -> Dict[str, Any]:
        """Export configuration to dictionary."""
        return {
            'settings': self.settings.dict(),
            'news_sources': [
                {
                    'name': s.name,
                    'base_url': s.base_url,
                    'enabled': s.enabled,
                    'priority': s.priority,
                    'tags': s.tags
                }
                for s in self.news_sources
            ],
            'reddit_sources': [
                {
                    'name': s.name,
                    'enabled': s.enabled,
                    'priority': s.priority,
                    'subreddits': s.custom_selectors.get('subreddits', [])
                }
                for s in self.reddit_sources
            ]
        }


# Global configuration manager instance
config_manager = ScrapingConfigManager()
