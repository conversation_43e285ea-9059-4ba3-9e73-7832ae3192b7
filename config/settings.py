"""
Configuration settings for the AI/LLM News Scraper.
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    # Direct URL override (useful for SQLite testing)
    url_override: Optional[str] = Field(default=None, env="DATABASE_URL")

    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    name: str = Field(default="ai_news_scraper", env="DB_NAME")
    user: str = Field(default="postgres", env="DB_USER")
    password: str = Field(default="", env="DB_PASSWORD")

    @property
    def url(self) -> str:
        """Generate database URL."""
        if self.url_override:
            return self.url_override
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"

    class Config:
        extra = "ignore"


class RedisSettings(BaseSettings):
    """Redis configuration settings."""
    
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    db: int = Field(default=0, env="REDIS_DB")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    @property
    def url(self) -> str:
        """Generate Redis URL."""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"

    class Config:
        extra = "ignore"


class APISettings(BaseSettings):
    """API configuration settings."""
    
    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=False, env="API_DEBUG")
    title: str = Field(default="AI/LLM News Scraper API", env="API_TITLE")
    version: str = Field(default="0.1.0", env="API_VERSION")

    class Config:
        extra = "ignore"


class ScrapingSettings(BaseSettings):
    """Scraping configuration settings."""
    
    user_agent: str = Field(
        default="AI-News-Scraper/1.0 (+https://github.com/your-repo)",
        env="SCRAPER_USER_AGENT"
    )
    request_delay: float = Field(default=1.0, env="SCRAPER_REQUEST_DELAY")
    max_retries: int = Field(default=3, env="SCRAPER_MAX_RETRIES")
    timeout: int = Field(default=30, env="SCRAPER_TIMEOUT")

    class Config:
        extra = "ignore"


class TwitterSettings(BaseSettings):
    """Twitter API configuration settings."""
    
    bearer_token: Optional[str] = Field(default=None, env="TWITTER_BEARER_TOKEN")
    api_key: Optional[str] = Field(default=None, env="TWITTER_API_KEY")
    api_secret: Optional[str] = Field(default=None, env="TWITTER_API_SECRET")
    access_token: Optional[str] = Field(default=None, env="TWITTER_ACCESS_TOKEN")
    access_token_secret: Optional[str] = Field(default=None, env="TWITTER_ACCESS_TOKEN_SECRET")

    class Config:
        extra = "ignore"


class RedditSettings(BaseSettings):
    """Reddit API configuration settings."""
    
    client_id: Optional[str] = Field(default=None, env="REDDIT_CLIENT_ID")
    client_secret: Optional[str] = Field(default=None, env="REDDIT_CLIENT_SECRET")
    user_agent: str = Field(
        default="AI-News-Scraper/1.0 by YourUsername",
        env="REDDIT_USER_AGENT"
    )

    class Config:
        extra = "ignore"


class NewsAPISettings(BaseSettings):
    """News API configuration settings."""
    
    api_key: Optional[str] = Field(default=None, env="NEWS_API_KEY")
    guardian_api_key: Optional[str] = Field(default=None, env="GUARDIAN_API_KEY")

    class Config:
        extra = "ignore"


class Settings(BaseSettings):
    """Main application settings."""

    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")

    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")

    # Direct database URL override
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")

    # Component settings
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    api: APISettings = APISettings()
    scraping: ScrapingSettings = ScrapingSettings()
    twitter: TwitterSettings = TwitterSettings()
    reddit: RedditSettings = RedditSettings()
    news_api: NewsAPISettings = NewsAPISettings()

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Override database URL if provided
        if self.database_url:
            self.database.url_override = self.database_url
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra environment variables


# Global settings instance
settings = Settings()
