#!/usr/bin/env python3
"""
Basic example of using the AI news scraping framework.

This example demonstrates how to:
1. Configure and initialize scrapers
2. Scrape articles from news websites and Reddit
3. Handle duplicate detection
4. Store results in the database
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrapers import (
    NewsScraper, RedditScraper, ScraperConfig, RateLimit,
    DuplicateDetector, UserAgentManager, ProxyManager
)
from models.connection import DatabaseManager
from models.article import Article
from config.settings import settings


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('scraping.log')
        ]
    )


def create_news_scraper_config(base_url: str, name: str) -> ScraperConfig:
    """Create a scraper configuration for a news website."""
    return ScraperConfig(
        name=name,
        base_url=base_url,
        rate_limit=RateLimit(
            requests_per_second=0.5,  # 1 request every 2 seconds
            delay_between_requests=2.0,
            respect_robots_txt=True
        ),
        timeout=30,
        max_retries=3,
        verify_ssl=True
    )


def create_reddit_scraper_config() -> ScraperConfig:
    """Create a scraper configuration for Reddit."""
    return ScraperConfig(
        name="reddit",
        base_url="https://www.reddit.com",
        rate_limit=RateLimit(
            requests_per_second=1.0,  # 1 request per second
            delay_between_requests=1.0,
            respect_robots_txt=True
        ),
        timeout=30,
        max_retries=3
    )


def scrape_news_websites():
    """Scrape articles from various news websites."""
    logger = logging.getLogger("news_scraper")
    
    # Example news websites (replace with actual AI/tech news sites)
    news_sites = [
        ("https://techcrunch.com", "TechCrunch"),
        ("https://venturebeat.com", "VentureBeat"),
        ("https://www.theverge.com", "The Verge")
    ]
    
    duplicate_detector = DuplicateDetector(similarity_threshold=0.85)
    articles_found = []
    
    for base_url, site_name in news_sites:
        try:
            logger.info(f"Scraping {site_name}...")
            
            # Create scraper configuration
            config = create_news_scraper_config(base_url, site_name.lower().replace(" ", "_"))
            
            # Initialize scraper
            with NewsScraper(config) as scraper:
                article_count = 0
                
                # Scrape articles
                for article in scraper.scrape_all():
                    if article and not duplicate_detector.is_duplicate(article):
                        duplicate_detector.add_article(article)
                        articles_found.append(article)
                        article_count += 1
                        
                        logger.info(f"Found article: {article.title[:50]}...")
                        
                        # Limit articles per site for demo
                        if article_count >= 5:
                            break
                
                # Print scraper statistics
                stats = scraper.get_stats()
                logger.info(f"{site_name} stats: {stats}")
                
        except Exception as e:
            logger.error(f"Failed to scrape {site_name}: {e}")
            continue
    
    return articles_found


def scrape_reddit():
    """Scrape AI-related posts from Reddit."""
    logger = logging.getLogger("reddit_scraper")
    
    try:
        logger.info("Scraping Reddit for AI content...")
        
        # Create Reddit scraper
        config = create_reddit_scraper_config()
        
        # AI/ML subreddits to monitor
        ai_subreddits = [
            'MachineLearning',
            'artificial', 
            'deeplearning',
            'OpenAI',
            'ChatGPT'
        ]
        
        duplicate_detector = DuplicateDetector(similarity_threshold=0.85)
        articles_found = []
        
        with RedditScraper(config, subreddits=ai_subreddits) as scraper:
            article_count = 0
            
            # Scrape articles
            for article in scraper.scrape_all():
                if article and not duplicate_detector.is_duplicate(article):
                    duplicate_detector.add_article(article)
                    articles_found.append(article)
                    article_count += 1
                    
                    logger.info(f"Found Reddit post: {article.title[:50]}...")
                    
                    # Limit for demo
                    if article_count >= 10:
                        break
            
            # Print scraper statistics
            stats = scraper.get_stats()
            logger.info(f"Reddit stats: {stats}")
    
    except Exception as e:
        logger.error(f"Failed to scrape Reddit: {e}")
        return []
    
    return articles_found


def save_articles_to_database(articles):
    """Save scraped articles to the database."""
    logger = logging.getLogger("database")
    
    try:
        # Initialize database connection
        db_manager = DatabaseManager()
        
        saved_count = 0
        
        for article_data in articles:
            try:
                # Create Article model instance
                article = Article(
                    title=article_data.title,
                    content=article_data.content,
                    url=article_data.url,
                    author=article_data.author,
                    published_at=article_data.published_at or datetime.utcnow(),
                    source_name=article_data.source_name,
                    tags=article_data.tags,
                    summary=article_data.summary,
                    content_hash=article_data.content_hash
                )
                
                # Save to database
                with db_manager.get_session() as session:
                    # Check if article already exists
                    existing = session.query(Article).filter_by(url=article.url).first()
                    if not existing:
                        session.add(article)
                        session.commit()
                        saved_count += 1
                        logger.info(f"Saved article: {article.title[:50]}...")
                    else:
                        logger.debug(f"Article already exists: {article.url}")
                        
            except Exception as e:
                logger.error(f"Failed to save article {article_data.url}: {e}")
                continue
        
        logger.info(f"Saved {saved_count} new articles to database")
        return saved_count
        
    except Exception as e:
        logger.error(f"Database error: {e}")
        return 0


def main():
    """Main scraping workflow."""
    setup_logging()
    logger = logging.getLogger("main")
    
    logger.info("Starting AI news scraping...")
    
    all_articles = []
    
    # Scrape news websites
    logger.info("=== Scraping News Websites ===")
    news_articles = scrape_news_websites()
    all_articles.extend(news_articles)
    logger.info(f"Found {len(news_articles)} articles from news websites")
    
    # Scrape Reddit
    logger.info("=== Scraping Reddit ===")
    reddit_articles = scrape_reddit()
    all_articles.extend(reddit_articles)
    logger.info(f"Found {len(reddit_articles)} articles from Reddit")
    
    # Save to database
    logger.info("=== Saving to Database ===")
    saved_count = save_articles_to_database(all_articles)
    
    # Summary
    logger.info("=== Scraping Complete ===")
    logger.info(f"Total articles found: {len(all_articles)}")
    logger.info(f"Articles saved to database: {saved_count}")
    
    # Print sample articles
    if all_articles:
        logger.info("=== Sample Articles ===")
        for i, article in enumerate(all_articles[:3]):
            logger.info(f"{i+1}. {article.title}")
            logger.info(f"   Source: {article.source_name}")
            logger.info(f"   URL: {article.url}")
            logger.info(f"   Published: {article.published_at}")
            logger.info("")


if __name__ == "__main__":
    main()
