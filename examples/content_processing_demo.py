#!/usr/bin/env python3
"""
Demonstration of Phase 4 Content Processing & Analysis Framework.

This example shows how to use the complete content processing pipeline
to analyze AI/LLM news articles with sentiment analysis, topic categorization,
trend detection, and keyword extraction.
"""

import sys
import os
from datetime import datetime
from typing import List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from processing import (
    ContentProcessor, ProcessingConfig, 
    SentimentAnalyzer, TopicCategorizer, TrendDetector, KeywordExtractor
)
from scrapers.base.content_parser import ParsedArticle


def create_sample_articles() -> List[ParsedArticle]:
    """Create sample AI/LLM news articles for demonstration."""
    
    articles = [
        ParsedArticle(
            url="https://example.com/gpt-breakthrough",
            title="OpenAI Announces GPT-5 with Revolutionary Multimodal Capabilities",
            content="""
            OpenAI has unveiled GPT-5, a groundbreaking large language model that represents
            a significant leap forward in artificial intelligence capabilities. The new model
            demonstrates unprecedented performance in multimodal understanding, combining text,
            images, audio, and video processing in a unified architecture.

            The breakthrough comes from a novel transformer architecture that enables the model
            to process and generate content across multiple modalities simultaneously. Early
            benchmarks show GPT-5 achieving human-level performance on complex reasoning tasks
            and demonstrating emergent capabilities in scientific problem-solving.

            "This represents the most significant advancement in AI since the original transformer
            architecture," said Sam Altman, CEO of OpenAI. "GPT-5's ability to understand and
            reason across different types of media opens up entirely new possibilities for AI
            applications."

            The model's training involved a massive dataset of 50 trillion tokens and required
            innovative techniques to handle the computational complexity. The team also implemented
            advanced safety measures to ensure responsible deployment of the technology.

            Industry experts predict that GPT-5 will accelerate adoption of AI across enterprise
            applications, from autonomous systems to scientific research. However, concerns about
            AI safety and potential job displacement continue to be debated.
            """,
            author="Sarah Johnson",
            published_at=datetime.now(),
            source_name="AI Tech News",
            tags=["OpenAI", "GPT-5", "LLM", "Multimodal", "AI"],
            summary="OpenAI releases GPT-5 with revolutionary multimodal capabilities"
        ),
        
        ParsedArticle(
            url="https://example.com/ai-regulation",
            title="EU Passes Comprehensive AI Regulation Framework Affecting Global Tech Giants",
            content="""
            The European Union has passed landmark legislation establishing comprehensive
            regulations for artificial intelligence systems, marking the world's first major
            regulatory framework for AI technology. The AI Act will significantly impact how
            tech giants like Google, Microsoft, and Meta develop and deploy AI systems.

            The regulation categorizes AI systems by risk level, with strict requirements for
            high-risk applications including autonomous vehicles, medical devices, and critical
            infrastructure. Companies must demonstrate compliance with safety standards and
            provide transparency about their AI decision-making processes.

            "This legislation sets a global precedent for responsible AI governance," said
            Dr. Elena Rodriguez, EU Commissioner for Digital Policy. "We're ensuring that AI
            development serves humanity while protecting fundamental rights and safety."

            The regulation includes provisions for algorithmic auditing, bias testing, and
            mandatory human oversight for certain AI applications. Companies face significant
            fines for non-compliance, with penalties reaching up to 6% of global annual revenue.

            Tech industry leaders have expressed mixed reactions, with some praising the clarity
            while others worry about innovation constraints. The regulation will be phased in
            over the next two years, giving companies time to adapt their practices.
            """,
            author="Michael Chen",
            published_at=datetime.now(),
            source_name="European Tech Report",
            tags=["EU", "AI Regulation", "Policy", "Compliance", "Tech Giants"],
            summary="EU passes comprehensive AI regulation affecting global tech companies"
        ),
        
        ParsedArticle(
            url="https://example.com/quantum-ai",
            title="Quantum-AI Hybrid System Achieves Breakthrough in Drug Discovery",
            content="""
            Researchers at MIT have developed a revolutionary quantum-AI hybrid system that
            dramatically accelerates drug discovery processes. The system combines quantum
            computing capabilities with advanced machine learning algorithms to simulate
            molecular interactions at unprecedented scale and accuracy.

            The breakthrough addresses one of the most computationally challenging problems
            in pharmaceutical research: predicting how potential drug compounds will interact
            with target proteins. Traditional methods require years of testing, but the new
            system can evaluate thousands of compounds in days.

            "We're seeing a 1000x speedup in certain molecular simulations," explained
            Dr. Jennifer Liu, lead researcher on the project. "This could revolutionize how
            we discover and develop new medications, potentially bringing life-saving drugs
            to market years earlier."

            The system uses quantum algorithms to handle the complex quantum mechanical
            properties of molecules, while machine learning models predict biological
            activity and toxicity. Initial tests have successfully identified promising
            compounds for cancer and Alzheimer's treatments.

            Major pharmaceutical companies including Pfizer and Roche have expressed interest
            in licensing the technology. The research team is now working to scale the system
            for commercial applications while ensuring broad access to the technology.
            """,
            author="Dr. Robert Kim",
            published_at=datetime.now(),
            source_name="Science & Technology Today",
            tags=["Quantum Computing", "AI", "Drug Discovery", "Healthcare", "Research"],
            summary="Quantum-AI hybrid system revolutionizes drug discovery with 1000x speedup"
        )
    ]
    
    return articles


def demonstrate_individual_analyzers():
    """Demonstrate each analyzer component individually."""
    print("=" * 80)
    print("INDIVIDUAL ANALYZER DEMONSTRATIONS")
    print("=" * 80)
    
    # Create a sample article
    articles = create_sample_articles()
    sample_article = articles[0]
    
    print(f"\nAnalyzing article: '{sample_article.title}'\n")
    
    # Sentiment Analysis
    print("1. SENTIMENT ANALYSIS")
    print("-" * 40)
    sentiment_analyzer = SentimentAnalyzer()
    sentiment_analyzer.initialize()
    
    sentiment_result = sentiment_analyzer.analyze(sample_article)
    if sentiment_result:
        print(f"Polarity: {sentiment_result.data['polarity']}")
        print(f"Score: {sentiment_result.data['score']:.3f}")
        print(f"Confidence: {sentiment_result.confidence:.3f}")
        print(f"Confidence Level: {sentiment_result.confidence_level}")
    
    # Topic Categorization
    print("\n2. TOPIC CATEGORIZATION")
    print("-" * 40)
    topic_categorizer = TopicCategorizer()
    topic_categorizer.initialize()
    
    topic_result = topic_categorizer.analyze(sample_article)
    if topic_result:
        print(f"Primary Categories: {topic_result.data['primary_categories']}")
        print(f"Secondary Categories: {topic_result.data['secondary_categories']}")
        print(f"Confidence: {topic_result.confidence:.3f}")
    
    # Trend Detection
    print("\n3. TREND DETECTION")
    print("-" * 40)
    trend_detector = TrendDetector()
    trend_detector.initialize()
    
    trend_result = trend_detector.analyze(sample_article)
    if trend_result:
        print(f"Primary Trends: {trend_result.data['primary_trends']}")
        print(f"Overall Strength: {trend_result.data['overall_strength']}")
        print(f"Signal Count: {trend_result.data['signal_count']}")
        print(f"Confidence: {trend_result.confidence:.3f}")
    
    # Keyword Extraction
    print("\n4. KEYWORD EXTRACTION")
    print("-" * 40)
    keyword_extractor = KeywordExtractor()
    keyword_extractor.initialize()
    
    keyword_result = keyword_extractor.analyze(sample_article)
    if keyword_result:
        top_keywords = keyword_result.data['keywords'][:10]
        print("Top Keywords:")
        for kw in top_keywords:
            print(f"  - {kw['keyword']} (score: {kw['score']:.3f}, category: {kw['category']})")
        print(f"Total Keywords: {keyword_result.data['total_keywords']}")
        print(f"Confidence: {keyword_result.confidence:.3f}")


def demonstrate_content_processor():
    """Demonstrate the unified content processor."""
    print("\n" + "=" * 80)
    print("UNIFIED CONTENT PROCESSOR DEMONSTRATION")
    print("=" * 80)
    
    # Create processor configuration
    config = ProcessingConfig(
        enable_sentiment=True,
        enable_topics=True,
        enable_trends=True,
        enable_keywords=True,
        parallel_processing=False,
        min_confidence_threshold=0.3
    )
    
    # Initialize processor
    processor = ContentProcessor(config)
    if not processor.initialize():
        print("Failed to initialize content processor!")
        return
    
    print(f"Initialized processor with {len(processor.analyzers)} analyzers")
    
    # Process multiple articles
    articles = create_sample_articles()
    print(f"\nProcessing {len(articles)} articles...")
    
    analyses = processor.process_articles(articles)
    print(f"Successfully processed {len(analyses)} articles")
    
    # Display results for each article
    for i, analysis in enumerate(analyses):
        article = articles[i]
        print(f"\n--- ARTICLE {i+1}: {article.title[:50]}... ---")
        print(f"Overall Confidence: {analysis.overall_confidence:.3f}")
        print(f"Analysis Results: {len(analysis.analysis_results)}")
        
        for result in analysis.analysis_results:
            print(f"  - {result.analysis_type.value}: {result.confidence:.3f} confidence")
    
    # Generate and display summary
    print("\n" + "-" * 60)
    print("ANALYSIS SUMMARY")
    print("-" * 60)
    
    summary = processor.get_analysis_summary(analyses)
    
    # Summary statistics
    stats = summary['summary_stats']
    print(f"Total Articles: {stats['total_articles']}")
    print(f"Average Confidence: {stats['average_confidence']:.3f}")
    print(f"Analysis Types Used: {', '.join(stats['analysis_types_used'])}")
    
    # Sentiment summary
    if 'sentiment_summary' in summary and summary['sentiment_summary']:
        sentiment = summary['sentiment_summary']
        print(f"\nSentiment Distribution: {sentiment.get('polarity_distribution', {})}")
        print(f"Average Sentiment Score: {sentiment.get('average_sentiment_score', 0):.3f}")
    
    # Topic summary
    if 'topic_summary' in summary and summary['topic_summary']:
        topics = summary['topic_summary']
        print(f"\nTop Categories: {list(topics.get('primary_category_distribution', {}).keys())[:5]}")
    
    # Trend summary
    if 'trend_summary' in summary and summary['trend_summary']:
        trends = summary['trend_summary']
        print(f"\nTrend Types: {list(trends.get('trend_type_distribution', {}).keys())}")
    
    # Keyword summary
    if 'keyword_summary' in summary and summary['keyword_summary']:
        keywords = summary['keyword_summary']
        top_keywords = list(keywords.get('top_keywords', {}).keys())[:10]
        print(f"\nTop Keywords: {', '.join(top_keywords)}")
    
    # Processing statistics
    processing_stats = processor.get_processing_stats()
    print(f"\nProcessing Statistics:")
    print(f"  - Success Rate: {processing_stats['success_rate']:.1%}")
    print(f"  - Average Processing Time: {processing_stats['average_processing_time_ms']:.1f}ms")
    print(f"  - Total Processing Time: {processing_stats['total_processing_time_ms']:.1f}ms")
    
    # Cleanup
    processor.shutdown()


def main():
    """Main demonstration function."""
    print("AI/LLM News Content Processing Framework Demo")
    print("Phase 4: Content Processing & Analysis")
    print("=" * 80)
    
    try:
        # Demonstrate individual analyzers
        demonstrate_individual_analyzers()
        
        # Demonstrate unified processor
        demonstrate_content_processor()
        
        print("\n" + "=" * 80)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
