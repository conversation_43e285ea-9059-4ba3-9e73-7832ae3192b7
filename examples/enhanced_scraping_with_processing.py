#!/usr/bin/env python3
"""
Enhanced scraping example with integrated content processing.

This example demonstrates the complete pipeline:
1. Scraping articles from AI news sources
2. Automatic content analysis (sentiment, topics, trends, keywords)
3. Storing both articles and analysis results in the database
4. Displaying comprehensive statistics and results

Usage:
    python examples/enhanced_scraping_with_processing.py
"""

import sys
import os
import logging
from datetime import datetime
from typing import List, Tuple, Optional

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrapers.enhanced import (
    create_enhanced_techcrunch_scraper,
    create_enhanced_venturebeat_scraper,
    create_enhanced_theverge_scraper
)
from scrapers.base.content_parser import ParsedArticle
from processing import ContentAnalysis
from models.database import Article
from models.connection import db_manager
from scrapers.utils.duplicate_detector import DuplicateDetector
from config.settings import settings


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('enhanced_scraping.log')
        ]
    )


def save_article_with_analysis(
    article: ParsedArticle, 
    analysis: Optional[ContentAnalysis],
    session
) -> bool:
    """
    Save an article and its analysis results to the database.
    
    Args:
        article: The scraped article
        analysis: Content analysis results (may be None)
        session: Database session
    
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        # Create Article model instance
        db_article = Article(
            title=article.title,
            content=article.content,
            url=article.url,
            author=article.author,
            published_at=article.published_at,
            source_name=article.source_name,
            content_hash=article.content_hash,
            tags=article.tags or []
        )
        
        # Add analysis results if available
        if analysis:
            # Sentiment analysis
            if analysis.sentiment:
                db_article.sentiment_score = analysis.sentiment.score
                db_article.sentiment_label = analysis.sentiment.label
                db_article.sentiment_confidence = analysis.sentiment.confidence

            # Topic categorization
            if analysis.topics:
                db_article.topics_analysis = [
                    {
                        'category': topic.category,
                        'confidence': topic.confidence,
                        'subcategory': topic.subcategory
                    }
                    for topic in analysis.topics
                ]

            # Trend detection
            if analysis.trends:
                db_article.trends_analysis = [
                    {
                        'trend_type': trend.trend_type,
                        'description': trend.description,
                        'confidence': trend.confidence,
                        'timeframe': trend.timeframe
                    }
                    for trend in analysis.trends
                ]

            # Keyword extraction
            if analysis.keywords:
                db_article.keywords_analysis = [
                    {
                        'keyword': kw.keyword,
                        'relevance': kw.relevance,
                        'category': kw.category
                    }
                    for kw in analysis.keywords
                ]

            # Overall analysis metadata
            db_article.analysis_confidence = analysis.overall_confidence
            db_article.analysis_timestamp = datetime.utcnow()
            
        session.add(db_article)
        session.commit()
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to save article {article.url}: {e}")
        session.rollback()
        return False


def display_analysis_summary(analysis: ContentAnalysis, article_title: str):
    """Display a summary of the content analysis results."""
    print(f"\n📊 Analysis for: {article_title[:60]}...")
    print("=" * 70)
    
    # Sentiment Analysis
    if analysis.sentiment:
        sentiment_emoji = {
            'positive': '😊',
            'negative': '😞', 
            'neutral': '😐'
        }.get(analysis.sentiment.label, '❓')
        
        print(f"🎭 Sentiment: {sentiment_emoji} {analysis.sentiment.label.title()} "
              f"(confidence: {analysis.sentiment.confidence:.3f})")
    
    # Topic Categorization
    if analysis.topics:
        print(f"🏷️  Topics: {', '.join([f'{t.category} ({t.confidence:.2f})' for t in analysis.topics[:3]])}")
    
    # Trend Detection
    if analysis.trends:
        print(f"📈 Trends: {', '.join([f'{t.trend_type}: {t.description}' for t in analysis.trends[:2]])}")
    
    # Keywords
    if analysis.keywords:
        top_keywords = [f"{kw.keyword} ({kw.relevance:.2f})" for kw in analysis.keywords[:5]]
        print(f"🔑 Keywords: {', '.join(top_keywords)}")
    
    print(f"✅ Overall Confidence: {analysis.overall_confidence:.3f}")


def run_enhanced_scraping_demo():
    """Run the enhanced scraping demonstration."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🚀 Enhanced AI News Scraping with Content Processing")
    print("=" * 60)
    
    # Initialize database
    try:
        session = db_manager.get_session_sync()
        logger.info("Database connection established")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return
    
    # Initialize duplicate detector
    duplicate_detector = DuplicateDetector()
    
    # Create enhanced scrapers
    scrapers = [
        create_enhanced_techcrunch_scraper(enable_all_processing=True),
        create_enhanced_venturebeat_scraper(enable_all_processing=True),
        create_enhanced_theverge_scraper(enable_all_processing=True)
    ]
    
    # Statistics tracking
    total_articles = 0
    total_processed = 0
    total_saved = 0
    total_duplicates = 0
    total_errors = 0
    
    print(f"\n📰 Starting scraping from {len(scrapers)} sources...")
    
    try:
        for scraper in scrapers:
            print(f"\n🔍 Scraping from {scraper.config.name}...")
            
            try:
                article_count = 0
                for article, analysis in scraper.scrape_all():
                    total_articles += 1
                    article_count += 1
                    
                    # Check for duplicates
                    if duplicate_detector.is_duplicate(article):
                        total_duplicates += 1
                        logger.debug(f"Duplicate article skipped: {article.url}")
                        continue
                    
                    duplicate_detector.add_article(article)
                    
                    # Display analysis if available
                    if analysis:
                        total_processed += 1
                        display_analysis_summary(analysis, article.title)
                    else:
                        print(f"\n📄 Article: {article.title[:60]}... (no analysis)")
                    
                    # Save to database
                    if save_article_with_analysis(article, analysis, session):
                        total_saved += 1
                        print(f"💾 Saved to database")
                    else:
                        total_errors += 1
                        print(f"❌ Failed to save")
                    
                    # Limit articles per source for demo
                    if article_count >= 3:
                        print(f"📊 Processed {article_count} articles from {scraper.config.name}")
                        break
                
                # Display scraper statistics
                stats = scraper.get_enhanced_stats()
                print(f"\n📈 {scraper.config.name} Statistics:")
                print(f"   Articles scraped: {stats.get('articles_scraped', 0)}")
                print(f"   Articles processed: {stats.get('articles_processed', 0)}")
                print(f"   Processing errors: {stats.get('processing_errors', 0)}")
                print(f"   Avg processing time: {stats.get('avg_processing_time', 0):.2f}s")
                print(f"   Processing success rate: {stats.get('processing_success_rate', 0):.1f}%")
                
            except Exception as e:
                logger.error(f"Error scraping from {scraper.config.name}: {e}")
                total_errors += 1
            
            finally:
                scraper.close()
    
    finally:
        session.close()
    
    # Display final summary
    print(f"\n🎯 Final Summary")
    print("=" * 40)
    print(f"📊 Total articles found: {total_articles}")
    print(f"🔄 Articles processed: {total_processed}")
    print(f"💾 Articles saved: {total_saved}")
    print(f"🔁 Duplicates skipped: {total_duplicates}")
    print(f"❌ Errors encountered: {total_errors}")
    
    if total_processed > 0:
        processing_rate = (total_processed / total_articles) * 100
        print(f"📈 Processing success rate: {processing_rate:.1f}%")
    
    print(f"\n✅ Enhanced scraping demonstration completed!")


if __name__ == "__main__":
    run_enhanced_scraping_demo()
