#!/usr/bin/env python3
"""
Phase 8 Content Processing & Classification Demo

This example demonstrates all Phase 8 capabilities:
1. Content Relevance Scoring
2. Automatic Topic Clustering  
3. Spam and Quality Filtering
4. Content Summarization

Run this script to see Phase 8 in action with sample AI/LLM news articles.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from processing.processor import ContentProcessor, ProcessingConfig
from processing.relevance.scorer import ContentRelevanceScorer
from processing.clustering.topic_clusterer import TopicClusterer
from processing.quality.filter import ContentQualityFilter
from processing.summarization.summarizer import ContentSummarizer
from scrapers.base.content_parser import ParsedArticle


def create_sample_articles():
    """Create diverse sample articles for testing Phase 8 capabilities."""
    return [
        # High-quality AI research article
        ParsedArticle(
            url="https://arxiv.org/abs/2023.12345",
            title="Efficient Attention Mechanisms for Large Language Models: A Breakthrough in Transformer Architecture",
            content="""
            Researchers at Stanford University have developed a revolutionary new attention mechanism 
            that significantly improves the efficiency of large language models. The new approach, 
            called Sparse Attention with Dynamic Routing (SADR), reduces computational complexity 
            by 45% while maintaining or even improving performance on standard benchmarks.
            
            The study, published in Nature Machine Intelligence, demonstrates that SADR can process 
            natural language tasks with unprecedented speed and accuracy. The methodology involves 
            a sophisticated attention mechanism that dynamically routes information through the most 
            relevant pathways in the neural network, eliminating redundant computations.
            
            "This breakthrough represents a fundamental shift in how we approach attention mechanisms 
            in transformer architectures," said Dr. Sarah Chen, lead researcher on the project. 
            "We're seeing 40-50% improvements in inference speed with no loss in quality."
            
            The research team conducted extensive experiments on multiple datasets including GLUE, 
            SuperGLUE, and custom benchmarks. Results show consistent improvements across all metrics, 
            with particular strength in language understanding, code generation, and mathematical 
            reasoning tasks. The model also demonstrates better few-shot learning capabilities.
            
            The implications for the AI industry are significant. This efficiency gain could make 
            large language models more accessible for deployment in resource-constrained environments, 
            potentially democratizing access to advanced AI capabilities.
            """,
            author="Dr. Sarah Chen, Dr. Michael Rodriguez",
            published_at=datetime.now() - timedelta(hours=2),
            source_name="arXiv"
        ),
        
        # Medium-quality tech news
        ParsedArticle(
            url="https://techcrunch.com/2023/openai-gpt-update",
            title="OpenAI Announces GPT-4.5 with Enhanced Reasoning Capabilities",
            content="""
            OpenAI today announced the release of GPT-4.5, their latest language model featuring 
            significantly improved reasoning capabilities and better factual accuracy. The new model 
            shows remarkable improvements in mathematical problem solving, code generation, and 
            complex reasoning tasks.
            
            According to the company, GPT-4.5 has been trained on a larger and more diverse dataset, 
            incorporating new training techniques that enhance its ability to understand context and 
            generate coherent responses. Early testing shows promising results across various benchmarks.
            
            The announcement comes amid increasing competition in the AI space, with companies like 
            Google, Anthropic, and others releasing their own advanced language models. Industry 
            experts believe this could accelerate innovation in AI applications and services.
            
            GPT-4.5 will be available through OpenAI's API starting next month, with pricing details 
            to be announced soon. The company also plans to integrate the new model into ChatGPT 
            and other products in the coming weeks.
            """,
            author="Tech Reporter",
            published_at=datetime.now() - timedelta(hours=6),
            source_name="TechCrunch"
        ),
        
        # Low-quality clickbait article
        ParsedArticle(
            url="https://clickbait-site.com/shocking-ai-secret",
            title="You Won't BELIEVE This SHOCKING AI Discovery That Will Change Everything Forever!",
            content="""
            OMG this is absolutely INCREDIBLE!!! Scientists have discovered something so amazing 
            about AI that it will literally blow your mind and change your life forever!!!
            
            This one weird trick that AI researchers don't want you to know will make you rich 
            and successful beyond your wildest dreams! Click here now to discover the secret 
            that big tech companies are desperately trying to hide from you!
            
            Don't miss out on this limited time opportunity to learn the insider secrets that 
            could make you millions! Act now before it's too late! This offer won't last long!
            
            Buy our exclusive course for just $99 (normally $999) and get instant access to 
            the AI secrets that will transform your life! Guaranteed results or your money back!
            """,
            author=None,
            published_at=datetime.now() - timedelta(days=1),
            source_name="ClickbaitSite"
        ),
        
        # Duplicate/similar content
        ParsedArticle(
            url="https://ai-news-blog.com/stanford-breakthrough",
            title="Stanford Researchers Develop New Transformer Architecture for LLMs",
            content="""
            Researchers at Stanford University have developed a new transformer architecture 
            that significantly improves the efficiency of large language models. The new approach 
            reduces computational complexity while maintaining performance on benchmarks.
            
            The study demonstrates that the new method can process natural language tasks with 
            improved speed. The methodology involves an attention mechanism that focuses on 
            relevant parts of the input sequence, eliminating unnecessary computations.
            
            This breakthrough could have significant implications for AI deployment in production 
            environments, making large language models more accessible and efficient.
            """,
            author="AI News Writer",
            published_at=datetime.now() - timedelta(hours=4),
            source_name="AI News Blog"
        ),
        
        # Irrelevant content
        ParsedArticle(
            url="https://cooking-blog.com/chocolate-cookies",
            title="The Ultimate Chocolate Chip Cookie Recipe That Will Amaze Your Family",
            content="""
            Looking for the perfect chocolate chip cookie recipe? Look no further! This amazing 
            recipe will give you the most delicious, chewy, and perfectly golden cookies you've 
            ever tasted. The secret is in the butter temperature and mixing technique.
            
            Ingredients: 2 cups all-purpose flour, 1 cup butter (room temperature), 3/4 cup brown 
            sugar, 1/2 cup white sugar, 2 large eggs, 2 tsp vanilla extract, 1 tsp baking soda, 
            1 tsp salt, 2 cups chocolate chips.
            
            Instructions: Cream butter and sugars until light and fluffy. Add eggs and vanilla. 
            Mix in dry ingredients gradually. Fold in chocolate chips. Drop spoonfuls on baking 
            sheet and bake at 375°F for 9-11 minutes until golden brown.
            
            These cookies are perfect for any occasion and will become your family's new favorite!
            """,
            author="Chef Baker",
            published_at=datetime.now() - timedelta(hours=8),
            source_name="Cooking Blog"
        ),
        
        # Technical AI article
        ParsedArticle(
            url="https://ai-research.org/neural-scaling",
            title="Neural Scaling Laws and Emergent Capabilities in Large Language Models",
            content="""
            Recent research has revealed fascinating insights into neural scaling laws and the 
            emergence of new capabilities in large language models as they grow in size. This 
            comprehensive analysis examines how model performance scales with parameters, data, 
            and compute resources.
            
            The study identifies several key scaling relationships that govern LLM performance. 
            As models increase in size, they exhibit predictable improvements in perplexity and 
            downstream task performance. However, certain capabilities appear to emerge suddenly 
            at specific scale thresholds, challenging our understanding of gradual improvement.
            
            Emergent capabilities include few-shot learning, chain-of-thought reasoning, and 
            complex instruction following. These abilities seem to appear abruptly rather than 
            developing gradually, suggesting phase transitions in model behavior.
            
            The implications for future AI development are profound. Understanding these scaling 
            laws could help researchers predict when new capabilities will emerge and guide 
            resource allocation for training larger models.
            """,
            author="Dr. Alex Thompson, Dr. Lisa Wang",
            published_at=datetime.now() - timedelta(hours=12),
            source_name="AI Research Institute"
        )
    ]


def demonstrate_phase8_capabilities():
    """Demonstrate all Phase 8 content processing capabilities."""
    print("🚀 Phase 8 Content Processing & Classification Demo")
    print("=" * 60)
    
    # Create sample articles
    articles = create_sample_articles()
    print(f"📄 Created {len(articles)} sample articles for testing")
    
    # Configure processor with all Phase 8 features enabled
    config = ProcessingConfig(
        enable_sentiment=True,
        enable_topics=True,
        enable_trends=True,
        enable_keywords=True,
        enable_relevance=True,
        enable_clustering=True,
        enable_quality_filter=True,
        enable_summarization=True,
        filter_low_quality=True,
        quality_threshold=0.4,
        min_confidence_threshold=0.3
    )
    
    # Initialize processor
    processor = ContentProcessor(config)
    if not processor.initialize():
        print("❌ Failed to initialize content processor")
        return
    
    print("✅ Content processor initialized with Phase 8 capabilities")
    print()
    
    # 1. Individual Component Testing
    print("🔍 Testing Individual Phase 8 Components")
    print("-" * 40)
    
    # Test relevance scoring
    scorer = ContentRelevanceScorer()
    scorer.initialize()
    high_quality_article = articles[0]  # Stanford research article
    relevance_result = scorer.analyze(high_quality_article)
    print(f"📊 Relevance Score: {relevance_result.data.get('overall_score', 0):.3f} "
          f"(Category: {relevance_result.data.get('relevance_category', 'unknown')})")
    
    # Test quality filtering
    filter_analyzer = ContentQualityFilter()
    filter_analyzer.initialize()
    clickbait_article = articles[2]  # Clickbait article
    quality_result = filter_analyzer.analyze(clickbait_article)
    print(f"🛡️  Quality Assessment: {quality_result.data['quality_level']} "
          f"(Should filter: {quality_result.data['should_filter']})")
    
    # Test summarization
    summarizer = ContentSummarizer()
    summarizer.initialize()
    summary_result = summarizer.analyze(high_quality_article)
    print(f"📝 Summary Generated: {len(summary_result.data.get('primary_summary', ''))} characters")
    print(f"   Compression: {summary_result.data.get('compression_ratio', 0):.2f}x")
    print()
    
    # 2. Integrated Processing Pipeline
    print("⚙️  Testing Integrated Processing Pipeline")
    print("-" * 40)
    
    # Process all articles
    analyses = processor.process_articles(articles)
    print(f"✅ Processed {len(analyses)}/{len(articles)} articles (filtered {len(articles) - len(analyses)})")
    
    # Show which articles were filtered
    processed_urls = {analysis.article_url for analysis in analyses}
    for article in articles:
        status = "✅ Processed" if article.url in processed_urls else "❌ Filtered"
        print(f"   {status}: {article.title[:50]}...")
    print()
    
    # 3. Topic Clustering
    print("🔗 Testing Topic Clustering")
    print("-" * 40)
    
    clustering_result = processor.cluster_articles(articles)
    if clustering_result and len(clustering_result.clusters) > 0:
        print(f"📊 Found {len(clustering_result.clusters)} topic clusters:")
        for i, cluster in enumerate(clustering_result.clusters):
            print(f"   Cluster {i+1}: {len(cluster.articles)} articles")
            print(f"   Keywords: {', '.join(cluster.centroid_keywords[:5])}")
            print(f"   Coherence: {cluster.coherence_score:.3f}")
    else:
        print("📊 No clusters found (need more similar articles)")
    print()
    
    # 4. Analysis Summary
    print("📈 Analysis Summary")
    print("-" * 40)
    
    summary = processor.get_analysis_summary(analyses)
    
    # Overall stats
    stats = summary['summary_stats']
    print(f"📊 Total Articles: {stats['total_articles']}")
    print(f"📊 Average Confidence: {stats['average_confidence']:.3f}")
    print(f"📊 Analysis Types: {', '.join(stats['analysis_types_used'])}")
    print()
    
    # Phase 8 specific summaries
    if summary.get('relevance_summary'):
        rel_summary = summary['relevance_summary']
        print(f"🎯 Relevance Summary:")
        print(f"   Average Score: {rel_summary['average_relevance_score']:.3f}")
        print(f"   High Relevance: {rel_summary['high_relevance_articles']} articles")
        print(f"   Distribution: {rel_summary['relevance_distribution']}")
    
    if summary.get('quality_summary'):
        qual_summary = summary['quality_summary']
        print(f"🛡️  Quality Summary:")
        print(f"   Quality Distribution: {qual_summary['quality_level_distribution']}")
        print(f"   Filter Rate: {qual_summary['filter_rate']:.1%}")
        print(f"   Average Score: {qual_summary['average_quality_score']:.3f}")
    
    if summary.get('summary_summary'):
        summ_summary = summary['summary_summary']
        print(f"📝 Summarization Summary:")
        print(f"   Summaries Generated: {summ_summary['summaries_generated']}")
        print(f"   Average Compression: {summ_summary['average_compression_ratio']:.2f}x")
        print(f"   Average Length: {summ_summary['average_summary_length']:.0f} chars")
    
    print()
    print("🎉 Phase 8 demonstration completed successfully!")
    print("=" * 60)


if __name__ == "__main__":
    demonstrate_phase8_capabilities()
