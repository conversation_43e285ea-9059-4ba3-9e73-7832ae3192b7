#!/usr/bin/env python3
"""
Simple integration test for content processing with scraping.

This test demonstrates the basic integration between scraping and content processing
without the complex enhanced scraper architecture.
"""

import sys
import os
import logging
from datetime import datetime
from typing import List, Optional

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrapers.base.scraper import ScraperConfig
from scrapers.sources.news_scraper import NewsScraper
from scrapers.base.content_parser import ParsedArticle
from processing import ContentProcessor, ProcessingConfig, ContentAnalysis, AnalysisType
from models.database import Article, SourceType
from models.connection import db_manager


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_article() -> ParsedArticle:
    """Create a test article for processing."""
    return ParsedArticle(
        title="Revolutionary AI Breakthrough: New Language Model Achieves Human-Level Performance",
        content="""
        Researchers at a leading technology company have announced a groundbreaking advancement 
        in artificial intelligence that could reshape the future of human-computer interaction. 
        The new language model, dubbed "NextGen-AI," demonstrates unprecedented capabilities 
        in understanding context, reasoning, and generating human-like responses.
        
        The model has been trained on a massive dataset and incorporates novel architectural 
        improvements that allow it to process information more efficiently than previous systems. 
        Initial tests show that NextGen-AI can perform complex reasoning tasks, write creative 
        content, and even assist with scientific research.
        
        "This represents a significant leap forward in AI capabilities," said Dr. <PERSON>, 
        lead researcher on the project. "We're seeing performance levels that we didn't expect 
        to achieve for several more years."
        
        The implications for industries ranging from healthcare to education are enormous. 
        However, researchers emphasize the importance of responsible development and deployment 
        of such powerful AI systems.
        """,
        url="https://example.com/ai-breakthrough-2024",
        author="Tech Reporter",
        published_at=datetime.now(),
        source_name="AI News Today",
        tags=["artificial intelligence", "machine learning", "technology", "research"]
    )


def test_content_processing():
    """Test the content processing framework."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🧪 Testing Content Processing Integration")
    print("=" * 50)
    
    # Create content processor
    try:
        config = ProcessingConfig(
            enable_sentiment=True,
            enable_topics=True,
            enable_trends=True,
            enable_keywords=True,
            parallel_processing=False,  # Keep it simple for testing
            min_confidence_threshold=0.1
        )
        
        processor = ContentProcessor(config)
        logger.info("Content processor initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize content processor: {e}")
        return False
    
    # Create test article
    article = create_test_article()
    print(f"\n📄 Test Article: {article.title[:50]}...")
    
    # Process the article
    try:
        print("\n🔄 Processing article...")
        analysis = processor.process_article(article)
        
        print(f"✅ Processing completed successfully!")
        print(f"📊 Overall confidence: {analysis.overall_confidence:.3f}")
        
        # Display results
        sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
        if sentiment_result:
            print(f"🎭 Sentiment: {sentiment_result.data.get('label', 'unknown')} (score: {sentiment_result.data.get('score', 0):.3f})")

        topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
        if topic_results:
            topics = [r.data.get('category', 'unknown') for r in topic_results[:3]]
            print(f"🏷️  Topics: {', '.join(topics)}")

        trend_results = analysis.get_results_by_type(AnalysisType.TREND)
        if trend_results:
            trends = [r.data.get('trend_type', 'unknown') for r in trend_results[:2]]
            print(f"📈 Trends: {', '.join(trends)}")

        keyword_results = analysis.get_results_by_type(AnalysisType.KEYWORD)
        if keyword_results:
            keywords = [r.data.get('keyword', 'unknown') for r in keyword_results[:5]]
            print(f"🔑 Keywords: {', '.join(keywords)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Content processing failed: {e}")
        return False
    
    finally:
        processor.shutdown()


def test_database_integration():
    """Test saving processed articles to database."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("\n💾 Testing Database Integration")
    print("=" * 40)
    
    try:
        # Get database session
        session = db_manager.get_session_sync()
        logger.info("Database connection established")
        
        # Create test article and analysis
        article = create_test_article()
        
        # Process the article
        config = ProcessingConfig(
            enable_sentiment=True,
            enable_topics=True,
            enable_trends=True,
            enable_keywords=True,
            parallel_processing=False
        )
        
        processor = ContentProcessor(config)
        analysis = processor.process_article(article)
        
        # Create database article
        db_article = Article(
            title=article.title,
            content=article.content,
            url=article.url,
            author=article.author,
            published_at=article.published_at,
            source_type=SourceType.RSS  # Use appropriate source type
        )
        
        # Add analysis results
        sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
        if sentiment_result:
            db_article.sentiment_score = sentiment_result.data.get('score', 0)
            db_article.sentiment_label = sentiment_result.data.get('label', 'neutral')
            db_article.sentiment_confidence = sentiment_result.confidence

        topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
        if topic_results:
            db_article.topics_analysis = [
                {
                    'category': result.data.get('category', 'unknown'),
                    'confidence': result.confidence,
                    'subcategory': result.data.get('subcategory', '')
                }
                for result in topic_results
            ]

        trend_results = analysis.get_results_by_type(AnalysisType.TREND)
        if trend_results:
            db_article.trends_analysis = [
                {
                    'trend_type': result.data.get('trend_type', 'unknown'),
                    'description': result.data.get('description', ''),
                    'confidence': result.confidence,
                    'timeframe': result.data.get('timeframe', '')
                }
                for result in trend_results
            ]

        keyword_results = analysis.get_results_by_type(AnalysisType.KEYWORD)
        if keyword_results:
            db_article.keywords_analysis = [
                {
                    'keyword': result.data.get('keyword', 'unknown'),
                    'relevance': result.data.get('relevance', 0),
                    'category': result.data.get('category', '')
                }
                for result in keyword_results
            ]
        
        db_article.analysis_confidence = analysis.overall_confidence
        db_article.analysis_timestamp = datetime.utcnow()
        
        # Save to database
        session.add(db_article)
        session.commit()
        
        print(f"✅ Article saved to database with ID: {db_article.id}")
        print(f"📊 Analysis data stored successfully")
        
        # Verify the save
        saved_article = session.query(Article).filter_by(url=article.url).first()
        if saved_article:
            print(f"🔍 Verification: Article found in database")
            print(f"   Sentiment: {saved_article.sentiment_label} ({saved_article.sentiment_score:.3f})")
            print(f"   Topics: {len(saved_article.topics_analysis or [])} categories")
            print(f"   Keywords: {len(saved_article.keywords_analysis or [])} keywords")
        
        session.close()
        processor.shutdown()
        
        return True
        
    except Exception as e:
        logger.error(f"Database integration test failed: {e}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False


def run_integration_tests():
    """Run all integration tests."""
    print("🚀 Content Processing Integration Tests")
    print("=" * 60)
    
    # Test 1: Content Processing
    test1_passed = test_content_processing()
    
    # Test 2: Database Integration
    test2_passed = test_database_integration()
    
    # Summary
    print(f"\n🎯 Test Results Summary")
    print("=" * 30)
    print(f"📊 Content Processing: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"💾 Database Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 All tests passed! Integration is working correctly.")
        return True
    else:
        print(f"\n⚠️  Some tests failed. Check the logs for details.")
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
