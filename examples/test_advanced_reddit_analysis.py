#!/usr/bin/env python3
"""
Test script for advanced Reddit post and comment analysis.

This script tests:
- Advanced comment analysis functionality
- Thread analysis and metrics
- Expert contributor identification
- Technical term extraction
- Quality scoring
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from scrapers.sources.reddit_scraper import RedditScraper, RedditScraperConfig, create_reddit_scraper
from scrapers.analysis.reddit_comment_analyzer import RedditCommentAnalyzer
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_comment_analyzer():
    """Test the standalone comment analyzer."""
    print("🧠 Testing Comment Analyzer")
    print("=" * 50)
    
    try:
        analyzer = RedditCommentAnalyzer()
        print("✅ Comment analyzer created successfully")
        
        # Test technical term detection
        test_text = "I'm using tensorflow and pytorch for deep learning with neural networks"
        technical_terms = analyzer._extract_technical_terms(test_text)
        print(f"🔬 Technical terms found: {technical_terms}")
        
        # Test sentiment analysis
        positive_text = "This is amazing and excellent work!"
        negative_text = "This is terrible and awful implementation"
        
        positive_sentiment = analyzer._analyze_sentiment(positive_text)
        negative_sentiment = analyzer._analyze_sentiment(negative_text)
        
        print(f"😊 Positive sentiment: {positive_sentiment}")
        print(f"😞 Negative sentiment: {negative_sentiment}")
        
        # Test code detection
        code_text = "Here's the code: ```python\ndef hello():\n    print('hello')\n```"
        has_code = analyzer._detect_code(code_text)
        print(f"💻 Code detected: {has_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comment analyzer test failed: {e}")
        logger.exception("Comment analyzer error")
        return False


def test_enhanced_reddit_scraper():
    """Test Reddit scraper with advanced comment analysis."""
    print("\n🚀 Testing Enhanced Reddit Scraper")
    print("=" * 50)
    
    try:
        # Create scraper with comments enabled
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=2,
            include_comments=True
        )
        
        # Check if comment analysis is available
        stats = scraper.get_stats()
        analysis_available = stats.get('comment_analysis_available', False)
        print(f"🧠 Comment analysis available: {analysis_available}")
        
        if not analysis_available:
            print("⚠️  Advanced comment analysis not available - testing basic functionality")
        
        # Get a post URL for testing
        post_url = None
        for url in scraper.get_article_urls():
            post_url = url
            break
        
        if not post_url:
            print("⚠️  No posts found for testing")
            return False
        
        print(f"📄 Testing enhanced scraping for: {post_url}")
        
        # Scrape with enhanced analysis
        article = scraper.scrape_article(post_url)
        
        if not article:
            print("❌ Failed to scrape article")
            return False
        
        print("✅ Article scraped successfully!")
        print(f"📰 Title: {article.title[:80]}...")
        print(f"📊 Content length: {len(article.content)} chars")
        
        # Check for enhanced comment features
        if "HIGH QUALITY" in article.content:
            print("🌟 High quality comments detected!")
        
        if "TECH:" in article.content:
            print("🔬 Technical terms identified in comments!")
        
        if "Expert Contributors" in article.content:
            print("👨‍🎓 Expert contributors identified!")
        
        # Show enhanced statistics
        final_stats = scraper.get_stats()
        print(f"\n📊 Enhanced Statistics:")
        print(f"   Comments scraped: {final_stats.get('comments_scraped', 0)}")
        print(f"   High quality comments: {final_stats.get('high_quality_comments', 0)}")
        print(f"   Expert contributors: {final_stats.get('expert_contributors', 0)}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Enhanced scraper test failed: {e}")
        logger.exception("Enhanced scraper error")
        return False


def test_thread_analysis():
    """Test detailed thread analysis functionality."""
    print("\n📊 Testing Thread Analysis")
    print("=" * 50)
    
    try:
        # Create scraper
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=1,
            include_comments=True
        )
        
        # Get a post URL
        post_url = None
        for url in scraper.get_article_urls():
            post_url = url
            break
        
        if not post_url:
            print("⚠️  No posts found for thread analysis")
            return False
        
        print(f"🔍 Analyzing thread: {post_url}")
        
        # Perform thread analysis
        analysis = scraper.analyze_thread(post_url)
        
        if not analysis:
            print("⚠️  Thread analysis not available (may require comment analyzer)")
            return True  # Not a failure, just a limitation
        
        print("✅ Thread analysis completed!")
        print(f"📊 Thread Analysis Results:")
        print(f"   Title: {analysis['title'][:60]}...")
        print(f"   Total comments: {analysis['total_comments']}")
        print(f"   Max depth: {analysis['max_depth']}")
        print(f"   Average score: {analysis['avg_score']:.1f}")
        print(f"   Technical depth: {analysis['technical_depth']:.2f}")
        print(f"   Controversy score: {analysis['controversy_score']:.2f}")
        
        # Show top contributors
        if analysis['top_contributors']:
            print(f"   Top contributors: {analysis['top_contributors'][:3]}")
        
        # Show discussion topics
        if analysis['discussion_topics']:
            print(f"   Discussion topics: {analysis['discussion_topics'][:5]}")
        
        # Show sentiment distribution
        sentiment = analysis['sentiment_distribution']
        print(f"   Sentiment - Positive: {sentiment.get('positive', 0)}, "
              f"Negative: {sentiment.get('negative', 0)}, "
              f"Neutral: {sentiment.get('neutral', 0)}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Thread analysis test failed: {e}")
        logger.exception("Thread analysis error")
        return False


def test_quality_filtering():
    """Test comment quality filtering and expert identification."""
    print("\n⭐ Testing Quality Filtering")
    print("=" * 50)
    
    try:
        # Create scraper with enhanced analysis
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning', 'artificial'],
            max_posts_per_subreddit=3,
            include_comments=True
        )
        
        # Check if analysis is available
        if not scraper.comment_analyzer:
            print("⚠️  Comment analyzer not available - skipping quality filtering test")
            return True
        
        print("🔍 Testing quality filtering across multiple posts...")
        
        total_posts = 0
        total_high_quality = 0
        total_experts = 0
        
        # Test multiple posts
        for url in scraper.get_article_urls():
            if total_posts >= 3:
                break
            
            print(f"\n📄 Analyzing post {total_posts + 1}: {url}")
            
            # Scrape with analysis
            article = scraper.scrape_article(url)
            if article:
                # Count quality indicators
                high_quality_count = article.content.count("HIGH QUALITY")
                expert_mentions = article.content.count("Expert Contributors")
                
                print(f"   🌟 High quality comments: {high_quality_count}")
                print(f"   👨‍🎓 Expert contributor sections: {expert_mentions}")
                
                total_high_quality += high_quality_count
                total_experts += expert_mentions
                total_posts += 1
        
        print(f"\n📊 Quality Filtering Summary:")
        print(f"   Posts analyzed: {total_posts}")
        print(f"   Total high quality comments: {total_high_quality}")
        print(f"   Total expert sections: {total_experts}")
        
        # Get final stats
        final_stats = scraper.get_stats()
        print(f"   Scraper stats - High quality: {final_stats.get('high_quality_comments', 0)}")
        print(f"   Scraper stats - Experts: {final_stats.get('expert_contributors', 0)}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Quality filtering test failed: {e}")
        logger.exception("Quality filtering error")
        return False


def main():
    """Run all advanced Reddit analysis tests."""
    print("🚀 Advanced Reddit Analysis Tests")
    print("=" * 60)
    
    # Check credentials first
    if not settings.reddit.client_id or not settings.reddit.client_secret:
        print("⚠️  Reddit API credentials not configured")
        print("Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET environment variables")
        print("Some tests will be limited or may fail")
        print()
    
    tests = [
        ("Comment Analyzer", test_comment_analyzer),
        ("Enhanced Reddit Scraper", test_enhanced_reddit_scraper),
        ("Thread Analysis", test_thread_analysis),
        ("Quality Filtering", test_quality_filtering)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 Test Results Summary")
    print("=" * 40)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All advanced Reddit analysis tests passed!")
    else:
        print("\n⚠️  Some tests failed.")
        print("This may be due to missing Reddit API credentials or dependencies.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
