#!/usr/bin/env python3
"""
Test the API endpoints for articles and content analysis.

This example demonstrates:
1. Starting the FastAPI server
2. Testing article endpoints (CRUD operations)
3. Testing content analysis endpoints
4. Testing search and filtering functionality
5. Testing batch processing endpoints
"""

import logging
import time
import requests
import json
from datetime import datetime, timezone
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from models.connection import db_manager
from models.database import Article, SourceType
from config.settings import settings


class APITester:
    """Test client for API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.logger = logging.getLogger("api.tester")
    
    def test_health_check(self) -> bool:
        """Test the health check endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            
            data = response.json()
            self.logger.info(f"Health check: {data}")
            
            return data.get("status") == "healthy"
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    def test_articles_endpoints(self) -> bool:
        """Test articles CRUD endpoints."""
        try:
            # Test GET /api/v1/articles/
            response = self.session.get(f"{self.base_url}/api/v1/articles/", params={
                "limit": 10,
                "skip": 0
            })
            response.raise_for_status()
            
            articles = response.json()
            self.logger.info(f"Retrieved {len(articles)} articles")
            
            if not articles:
                self.logger.warning("No articles found in database")
                return True
            
            # Test GET /api/v1/articles/{id}
            article_id = articles[0]["id"]
            response = self.session.get(f"{self.base_url}/api/v1/articles/{article_id}")
            response.raise_for_status()
            
            article = response.json()
            self.logger.info(f"Retrieved article: {article['title'][:50]}...")
            
            # Test article search
            response = self.session.get(f"{self.base_url}/api/v1/articles/search/", params={
                "q": "AI",
                "limit": 5
            })
            response.raise_for_status()
            
            search_results = response.json()
            self.logger.info(f"Search returned {len(search_results)} results")
            
            # Test article statistics
            response = self.session.get(f"{self.base_url}/api/v1/articles/stats/")
            response.raise_for_status()
            
            stats = response.json()
            self.logger.info(f"Article stats: {stats}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Articles endpoints test failed: {e}")
            return False
    
    def test_analysis_endpoints(self) -> bool:
        """Test content analysis endpoints."""
        try:
            # Get articles with analysis
            response = self.session.get(f"{self.base_url}/api/v1/articles/", params={
                "has_analysis": True,
                "limit": 5
            })
            response.raise_for_status()
            
            analyzed_articles = response.json()
            self.logger.info(f"Found {len(analyzed_articles)} analyzed articles")
            
            if analyzed_articles:
                # Test GET analysis for specific article
                article_id = analyzed_articles[0]["id"]
                response = self.session.get(f"{self.base_url}/api/v1/analysis/{article_id}")
                
                if response.status_code == 200:
                    analysis = response.json()
                    self.logger.info(f"Retrieved analysis for article {article_id}")
                    self.logger.info(f"  Sentiment: {analysis.get('sentiment', {})}")
                    self.logger.info(f"  Topics: {len(analysis.get('topics', []))}")
                    self.logger.info(f"  Trends: {len(analysis.get('trends', []))}")
                else:
                    self.logger.warning(f"No analysis found for article {article_id}")
            
            # Test sentiment distribution
            response = self.session.get(f"{self.base_url}/api/v1/analysis/sentiment/distribution", params={
                "days_back": 30
            })
            response.raise_for_status()
            
            sentiment_dist = response.json()
            self.logger.info(f"Sentiment distribution: {sentiment_dist}")
            
            # Test trending topics
            response = self.session.get(f"{self.base_url}/api/v1/analysis/topics/trending", params={
                "limit": 10,
                "days_back": 7
            })
            response.raise_for_status()
            
            trending_topics = response.json()
            self.logger.info(f"Found {len(trending_topics)} trending topics")
            
            # Test analysis statistics
            response = self.session.get(f"{self.base_url}/api/v1/analysis/stats/analysis", params={
                "days_back": 30
            })
            response.raise_for_status()
            
            analysis_stats = response.json()
            self.logger.info(f"Analysis stats: {analysis_stats}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Analysis endpoints test failed: {e}")
            return False
    
    def test_article_analysis_trigger(self) -> bool:
        """Test triggering analysis for a specific article."""
        try:
            # Get an unanalyzed article
            response = self.session.get(f"{self.base_url}/api/v1/articles/", params={
                "has_analysis": False,
                "limit": 1
            })
            response.raise_for_status()
            
            unanalyzed_articles = response.json()
            
            if not unanalyzed_articles:
                self.logger.warning("No unanalyzed articles found")
                return True
            
            article_id = unanalyzed_articles[0]["id"]
            article_title = unanalyzed_articles[0]["title"]
            
            self.logger.info(f"Triggering analysis for: {article_title[:50]}...")
            
            # Trigger analysis
            response = self.session.post(f"{self.base_url}/api/v1/analysis/{article_id}/analyze")
            
            if response.status_code == 200:
                analysis = response.json()
                self.logger.info(f"Analysis completed successfully")
                self.logger.info(f"  Sentiment: {analysis.get('sentiment', {})}")
                self.logger.info(f"  Topics: {len(analysis.get('topics', []))}")
                self.logger.info(f"  Trends: {len(analysis.get('trends', []))}")
                return True
            elif response.status_code == 400:
                self.logger.warning("Article has no content to analyze")
                return True
            else:
                response.raise_for_status()
            
        except Exception as e:
            self.logger.error(f"Article analysis trigger test failed: {e}")
            return False
    
    def test_batch_processing_trigger(self) -> bool:
        """Test triggering batch processing."""
        try:
            # Trigger batch processing
            response = self.session.post(f"{self.base_url}/api/v1/analysis/batch/process", params={
                "max_articles": 5,
                "unanalyzed_only": True
            })
            response.raise_for_status()
            
            result = response.json()
            self.logger.info(f"Batch processing triggered: {result}")
            
            # Wait a moment for processing to start
            time.sleep(2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Batch processing trigger test failed: {e}")
            return False
    
    def test_filtering_and_pagination(self) -> bool:
        """Test various filtering and pagination options."""
        try:
            # Test source type filtering
            for source_type in ["reddit", "twitter"]:
                response = self.session.get(f"{self.base_url}/api/v1/articles/", params={
                    "source_type": source_type,
                    "limit": 5
                })
                response.raise_for_status()
                
                articles = response.json()
                self.logger.info(f"Found {len(articles)} {source_type} articles")
            
            # Test sentiment filtering
            response = self.session.get(f"{self.base_url}/api/v1/articles/", params={
                "sentiment_label": "positive",
                "limit": 5
            })
            response.raise_for_status()
            
            positive_articles = response.json()
            self.logger.info(f"Found {len(positive_articles)} positive articles")
            
            # Test pagination
            response = self.session.get(f"{self.base_url}/api/v1/articles/", params={
                "skip": 10,
                "limit": 5,
                "sort_by": "scraped_at",
                "sort_order": "desc"
            })
            response.raise_for_status()
            
            paginated_articles = response.json()
            self.logger.info(f"Pagination test: {len(paginated_articles)} articles")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Filtering and pagination test failed: {e}")
            return False


def test_api_server_startup():
    """Test if API server can be started."""
    print("\n" + "="*60)
    print("TESTING API SERVER STARTUP")
    print("="*60)
    
    try:
        import subprocess
        import signal
        import os
        
        # Start the API server in background
        print("Starting API server...")
        
        # Use the current working directory
        cwd = os.getcwd()
        
        process = subprocess.Popen([
            "python", "-m", "api.main"
        ], cwd=cwd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ API server started successfully")
            
            # Test basic connectivity
            tester = APITester()
            if tester.test_health_check():
                print("✅ Health check passed")
                success = True
            else:
                print("❌ Health check failed")
                success = False
            
            # Terminate the server
            process.terminate()
            process.wait(timeout=5)
            
            return success
        else:
            stdout, stderr = process.communicate()
            print(f"❌ API server failed to start")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ API server startup test failed: {e}")
        return False


def test_api_endpoints_comprehensive():
    """Test API endpoints comprehensively (assumes server is running)."""
    print("\n" + "="*60)
    print("TESTING API ENDPOINTS (COMPREHENSIVE)")
    print("="*60)
    
    try:
        tester = APITester()
        
        # Test health check first
        if not tester.test_health_check():
            print("❌ Health check failed - server may not be running")
            return False
        
        print("✅ Health check passed")
        
        # Test articles endpoints
        if tester.test_articles_endpoints():
            print("✅ Articles endpoints test passed")
        else:
            print("❌ Articles endpoints test failed")
            return False
        
        # Test analysis endpoints
        if tester.test_analysis_endpoints():
            print("✅ Analysis endpoints test passed")
        else:
            print("❌ Analysis endpoints test failed")
            return False
        
        # Test filtering and pagination
        if tester.test_filtering_and_pagination():
            print("✅ Filtering and pagination test passed")
        else:
            print("❌ Filtering and pagination test failed")
            return False
        
        # Test analysis trigger (if unanalyzed articles exist)
        if tester.test_article_analysis_trigger():
            print("✅ Article analysis trigger test passed")
        else:
            print("❌ Article analysis trigger test failed")
            return False
        
        # Test batch processing trigger
        if tester.test_batch_processing_trigger():
            print("✅ Batch processing trigger test passed")
        else:
            print("❌ Batch processing trigger test failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive API test failed: {e}")
        return False


def main():
    """Run all API endpoint tests."""
    print("🔄 API ENDPOINTS TEST SUITE")
    print("=" * 80)
    
    # Check if we have articles in database
    try:
        with db_manager.get_session() as session:
            article_count = session.query(Article).count()
            print(f"📊 Database contains {article_count} articles")
            
            if article_count == 0:
                print("⚠️  WARNING: No articles in database. Some tests may not be meaningful.")
    except Exception as e:
        print(f"⚠️  WARNING: Could not check database: {e}")
    
    tests = [
        ("API Server Startup", test_api_server_startup),
        ("API Endpoints (Comprehensive)", test_api_endpoints_comprehensive)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! API endpoints are working correctly.")
        print("\n📖 API Documentation available at: http://localhost:8000/docs")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
