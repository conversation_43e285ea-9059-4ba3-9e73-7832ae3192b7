#!/usr/bin/env python3
"""
Test the batch processing system for existing articles.

This example demonstrates:
1. Processing existing articles without analysis
2. Updating articles with sentiment analysis, topic categorization, and trend detection
3. Batch processing with progress tracking
4. Error handling and statistics
"""

import logging
import time
from datetime import datetime, timezone
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from processing.batch.article_processor import (
    create_batch_processor, 
    BatchProcessingConfig,
    process_unanalyzed_articles,
    reprocess_all_articles
)
from models.connection import db_manager
from models.database import Article, SourceType
from config.settings import settings


def test_batch_processor_initialization():
    """Test batch processor initialization."""
    print("\n" + "="*60)
    print("TESTING BATCH PROCESSOR INITIALIZATION")
    print("="*60)
    
    try:
        # Test default configuration
        processor = create_batch_processor()
        print("✅ Default batch processor created successfully")
        
        # Test custom configuration
        custom_processor = create_batch_processor(
            process_unanalyzed_only=False,
            max_articles=50,
            source_types=[SourceType.REDDIT],
            enable_all_analysis=True
        )
        print("✅ Custom batch processor created successfully")
        
        # Test configuration object
        config = BatchProcessingConfig(
            max_articles_per_batch=25,
            max_total_articles=100,
            min_content_length=100,
            max_workers=2
        )
        
        from processing.batch.article_processor import BatchArticleProcessor
        config_processor = BatchArticleProcessor(config)
        print("✅ Configuration-based processor created successfully")
        
        # Cleanup
        processor.shutdown()
        custom_processor.shutdown()
        config_processor.shutdown()
        
        return True
        
    except Exception as e:
        print(f"❌ Batch processor initialization failed: {e}")
        return False


def test_database_article_query():
    """Test querying articles from database."""
    print("\n" + "="*60)
    print("TESTING DATABASE ARTICLE QUERY")
    print("="*60)
    
    try:
        with db_manager.get_session() as session:
            # Count total articles
            total_articles = session.query(Article).count()
            print(f"📊 Total articles in database: {total_articles}")
            
            # Count articles without analysis
            unanalyzed = session.query(Article).filter(
                Article.analysis_timestamp.is_(None)
            ).count()
            print(f"📊 Articles without analysis: {unanalyzed}")
            
            # Count articles by source type
            reddit_count = session.query(Article).filter(
                Article.source_type == SourceType.REDDIT
            ).count()
            twitter_count = session.query(Article).filter(
                Article.source_type == SourceType.TWITTER
            ).count()
            
            print(f"📊 Reddit articles: {reddit_count}")
            print(f"📊 Twitter articles: {twitter_count}")
            
            # Show sample articles
            sample_articles = session.query(Article).limit(3).all()
            if sample_articles:
                print(f"\n📄 Sample articles:")
                for article in sample_articles:
                    print(f"   - {article.title[:50]}...")
                    print(f"     Source: {article.source_type}")
                    print(f"     Has analysis: {article.analysis_timestamp is not None}")
                    print(f"     Content length: {len(article.content or '')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Database query test failed: {e}")
        return False


def test_small_batch_processing():
    """Test processing a small batch of articles."""
    print("\n" + "="*60)
    print("TESTING SMALL BATCH PROCESSING")
    print("="*60)
    
    try:
        # Create processor for small batch
        config = BatchProcessingConfig(
            process_unanalyzed_only=True,
            max_articles_per_batch=5,
            max_total_articles=10,
            max_workers=2,
            enable_progress_logging=True,
            progress_log_interval=1
        )
        
        from processing.batch.article_processor import BatchArticleProcessor
        processor = BatchArticleProcessor(config)
        
        # Set up callbacks
        processed_articles = []
        progress_updates = []
        
        def progress_callback(progress):
            progress_updates.append(progress)
            print(f"📈 Progress: {progress['articles_processed']}/{progress['total_articles']} "
                  f"({progress['progress_percentage']:.1f}%)")
        
        def article_callback(article, analysis):
            processed_articles.append((article, analysis))
            print(f"✅ Processed: {article.title[:40]}...")
        
        processor.add_progress_callback(progress_callback)
        processor.add_article_callback(article_callback)
        
        # Run processing
        print("Starting small batch processing...")
        start_time = time.time()
        
        stats = processor.process_articles()
        
        elapsed_time = time.time() - start_time
        
        print(f"\n✅ Small batch processing completed in {elapsed_time:.2f} seconds")
        print(f"   Articles found: {stats['articles_found']}")
        print(f"   Articles processed: {stats['articles_processed']}")
        print(f"   Articles updated: {stats['articles_updated']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        print(f"   Database errors: {stats['database_errors']}")
        print(f"   Average processing time: {stats['average_processing_time_ms']:.2f}ms")
        
        processor.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Small batch processing test failed: {e}")
        return False


def test_convenience_functions():
    """Test convenience functions for batch processing."""
    print("\n" + "="*60)
    print("TESTING CONVENIENCE FUNCTIONS")
    print("="*60)
    
    try:
        # Test process_unanalyzed_articles
        print("Testing process_unanalyzed_articles...")
        stats1 = process_unanalyzed_articles(max_articles=5)
        
        print(f"✅ Processed {stats1['articles_processed']} unanalyzed articles")
        print(f"   Found: {stats1['articles_found']}")
        print(f"   Updated: {stats1['articles_updated']}")
        
        # Test reprocess_all_articles (with very small limit)
        print("\nTesting reprocess_all_articles...")
        stats2 = reprocess_all_articles(max_articles=3)
        
        print(f"✅ Reprocessed {stats2['articles_processed']} articles")
        print(f"   Found: {stats2['articles_found']}")
        print(f"   Updated: {stats2['articles_updated']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Convenience functions test failed: {e}")
        return False


def test_analysis_results():
    """Test that analysis results are properly stored."""
    print("\n" + "="*60)
    print("TESTING ANALYSIS RESULTS STORAGE")
    print("="*60)
    
    try:
        # Process a few articles
        stats = process_unanalyzed_articles(max_articles=3)
        
        if stats['articles_updated'] == 0:
            print("⚠️  No articles were updated (possibly all already analyzed)")
            return True
        
        # Check analysis results in database
        with db_manager.get_session() as session:
            analyzed_articles = session.query(Article).filter(
                Article.analysis_timestamp.isnot(None)
            ).order_by(Article.analysis_timestamp.desc()).limit(3).all()
            
            if not analyzed_articles:
                print("⚠️  No analyzed articles found in database")
                return True
            
            print(f"📊 Found {len(analyzed_articles)} recently analyzed articles:")
            
            for article in analyzed_articles:
                print(f"\n📄 {article.title[:50]}...")
                
                # Check sentiment analysis
                if article.sentiment_score is not None:
                    print(f"   Sentiment: {article.sentiment_label} "
                          f"(score: {article.sentiment_score:.2f}, "
                          f"confidence: {article.sentiment_confidence:.2f})")
                else:
                    print("   Sentiment: Not analyzed")
                
                # Check topic analysis
                if article.topics_analysis:
                    topics = [t.get('category', 'unknown') for t in article.topics_analysis[:3]]
                    print(f"   Topics: {', '.join(topics)}")
                else:
                    print("   Topics: Not analyzed")
                
                # Check trend analysis
                if article.trends_analysis:
                    trends = [t.get('trend_type', 'unknown') for t in article.trends_analysis[:2]]
                    print(f"   Trends: {', '.join(trends)}")
                else:
                    print("   Trends: Not analyzed")
                
                print(f"   Analysis confidence: {article.analysis_confidence:.2f}")
                print(f"   Analysis timestamp: {article.analysis_timestamp}")
        
        print("\n✅ Analysis results verification completed")
        return True
        
    except Exception as e:
        print(f"❌ Analysis results test failed: {e}")
        return False


def test_error_handling():
    """Test error handling in batch processing."""
    print("\n" + "="*60)
    print("TESTING ERROR HANDLING")
    print("="*60)
    
    try:
        # Create processor with error handling settings
        config = BatchProcessingConfig(
            max_total_articles=5,
            max_workers=1,
            skip_on_error=True,
            max_retries=2,
            processing_timeout_seconds=10
        )
        
        from processing.batch.article_processor import BatchArticleProcessor
        processor = BatchArticleProcessor(config)
        
        # Run processing
        stats = processor.process_articles()
        
        print(f"✅ Error handling test completed")
        print(f"   Articles processed: {stats['articles_processed']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        print(f"   Database errors: {stats['database_errors']}")
        
        processor.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


def main():
    """Run all batch processing tests."""
    print("🔄 BATCH PROCESSING TEST SUITE")
    print("=" * 80)
    
    tests = [
        ("Batch Processor Initialization", test_batch_processor_initialization),
        ("Database Article Query", test_database_article_query),
        ("Small Batch Processing", test_small_batch_processing),
        ("Convenience Functions", test_convenience_functions),
        ("Analysis Results Storage", test_analysis_results),
        ("Error Handling", test_error_handling)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Batch processing system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
