#!/usr/bin/env python3
"""
Test environment variable loading and Reddit credentials.
"""

import os
from pathlib import Path

def test_env_file():
    """Test if .env file exists and can be read."""
    print("🔍 TESTING ENVIRONMENT FILE")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print(f"✅ .env file found at: {env_file.absolute()}")
        
        # Read and check Reddit credentials
        with open(env_file, 'r') as f:
            content = f.read()
            
        if "REDDIT_CLIENT_ID=" in content:
            lines = content.split('\n')
            for line in lines:
                if line.startswith("REDDIT_CLIENT_ID="):
                    client_id = line.split('=', 1)[1]
                    if client_id and client_id != "your_reddit_client_id_here":
                        print(f"✅ Reddit Client ID found: {client_id[:8]}...")
                    else:
                        print("❌ Reddit Client ID not configured")
                        
                elif line.startswith("REDDIT_CLIENT_SECRET="):
                    client_secret = line.split('=', 1)[1]
                    if client_secret and client_secret != "your_reddit_client_secret_here":
                        print(f"✅ Reddit Client Secret found: {client_secret[:8]}...")
                    else:
                        print("❌ Reddit Client Secret not configured")
        else:
            print("❌ Reddit credentials not found in .env file")
    else:
        print("❌ .env file not found")
    
    print()

def test_os_environ():
    """Test if environment variables are loaded."""
    print("🔍 TESTING OS ENVIRONMENT VARIABLES")
    print("=" * 50)
    
    reddit_client_id = os.getenv("REDDIT_CLIENT_ID")
    reddit_client_secret = os.getenv("REDDIT_CLIENT_SECRET")
    reddit_user_agent = os.getenv("REDDIT_USER_AGENT")
    
    if reddit_client_id:
        print(f"✅ REDDIT_CLIENT_ID in environment: {reddit_client_id[:8]}...")
    else:
        print("❌ REDDIT_CLIENT_ID not in environment")
    
    if reddit_client_secret:
        print(f"✅ REDDIT_CLIENT_SECRET in environment: {reddit_client_secret[:8]}...")
    else:
        print("❌ REDDIT_CLIENT_SECRET not in environment")
    
    if reddit_user_agent:
        print(f"✅ REDDIT_USER_AGENT in environment: {reddit_user_agent}")
    else:
        print("❌ REDDIT_USER_AGENT not in environment")
    
    print()

def test_settings_import():
    """Test importing settings and checking Reddit configuration."""
    print("🔍 TESTING SETTINGS IMPORT")
    print("=" * 50)
    
    try:
        from config.settings import settings
        
        print("✅ Settings imported successfully")
        
        # Check Reddit settings
        if hasattr(settings, 'reddit'):
            print("✅ Reddit settings found")
            
            if hasattr(settings.reddit, 'client_id'):
                client_id = settings.reddit.client_id
                if client_id and client_id != "your_reddit_client_id_here":
                    print(f"✅ Reddit Client ID in settings: {client_id[:8]}...")
                else:
                    print("❌ Reddit Client ID not configured in settings")
            
            if hasattr(settings.reddit, 'client_secret'):
                client_secret = settings.reddit.client_secret
                if client_secret and client_secret != "your_reddit_client_secret_here":
                    print(f"✅ Reddit Client Secret in settings: {client_secret[:8]}...")
                else:
                    print("❌ Reddit Client Secret not configured in settings")
            
            if hasattr(settings.reddit, 'user_agent'):
                user_agent = settings.reddit.user_agent
                print(f"✅ Reddit User Agent in settings: {user_agent}")
        else:
            print("❌ Reddit settings not found")
            
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
    
    print()

def test_praw_connection():
    """Test direct PRAW connection with credentials."""
    print("🔍 TESTING DIRECT PRAW CONNECTION")
    print("=" * 50)
    
    try:
        import praw
        
        # Try to load from environment first
        client_id = os.getenv("REDDIT_CLIENT_ID")
        client_secret = os.getenv("REDDIT_CLIENT_SECRET")
        user_agent = os.getenv("REDDIT_USER_AGENT", "AI_News_Scraper/1.0")
        
        if not client_id or not client_secret:
            print("❌ Reddit credentials not available in environment")
            return False
        
        # Create Reddit instance
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # Test connection by accessing a subreddit
        subreddit = reddit.subreddit("test")
        print(f"✅ Connected to Reddit API")
        print(f"   Test subreddit: {subreddit.display_name}")
        print(f"   Subscribers: {subreddit.subscribers}")
        
        return True
        
    except Exception as e:
        print(f"❌ PRAW connection failed: {e}")
        return False

def main():
    """Run all environment tests."""
    print("🔄 ENVIRONMENT AND CREDENTIALS TEST SUITE")
    print("=" * 80)
    
    test_env_file()
    test_os_environ()
    test_settings_import()
    test_praw_connection()
    
    print("=" * 80)
    print("TEST COMPLETE")

if __name__ == "__main__":
    main()
