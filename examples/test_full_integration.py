#!/usr/bin/env python3
"""
Comprehensive test of the integrated pipeline with both Reddit and Twitter sources.

This example demonstrates:
1. Complete pipeline from scraping to analysis to database storage
2. Integration with both Reddit and Twitter APIs
3. Content processing with sentiment, topic, and trend analysis
4. Database storage of analysis results
5. Performance monitoring and statistics
"""

import os
import logging
import time
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List
from pathlib import Path

# Load .env file explicitly before importing other modules
def load_env_file():
    """Load .env file manually."""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

# Load environment variables
load_env_file()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from scrapers.enhanced.integrated_pipeline import (
    IntegratedScraping<PERSON><PERSON><PERSON>e,
    PipelineConfig,
    create_integrated_pipeline
)
from models.connection import db_manager
from models.database import Article, SourceType
from config.settings import settings


def test_pipeline_initialization():
    """Test integrated pipeline initialization."""
    print("\n" + "="*60)
    print("TESTING INTEGRATED PIPELINE INITIALIZATION")
    print("="*60)
    
    try:
        # Test default configuration
        pipeline = create_integrated_pipeline()
        print("✅ Default integrated pipeline created successfully")
        
        # Test custom configuration
        custom_config = PipelineConfig(
            enable_reddit=True,
            enable_twitter=True,
            reddit_max_posts_per_subreddit=5,
            enable_content_processing=True,
            save_to_database=True,
            max_workers=2,
            enable_progress_logging=True
        )
        
        custom_pipeline = IntegratedScrapingPipeline(custom_config)
        print("✅ Custom integrated pipeline created successfully")
        
        # Test configuration validation
        print(f"   Reddit enabled: {custom_config.enable_reddit}")
        print(f"   Twitter enabled: {custom_config.enable_twitter}")
        print(f"   Content processing: {custom_config.enable_content_processing}")
        print(f"   Database storage: {custom_config.save_to_database}")
        
        # Cleanup
        pipeline.shutdown()
        custom_pipeline.shutdown()
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline initialization failed: {e}")
        return False


def test_database_connection():
    """Test database connection and initial state."""
    print("\n" + "="*60)
    print("TESTING DATABASE CONNECTION")
    print("="*60)
    
    try:
        with db_manager.get_session() as session:
            # Count existing articles
            total_articles = session.query(Article).count()
            reddit_articles = session.query(Article).filter(
                Article.source_type == SourceType.REDDIT
            ).count()
            twitter_articles = session.query(Article).filter(
                Article.source_type == SourceType.TWITTER
            ).count()
            analyzed_articles = session.query(Article).filter(
                Article.analysis_timestamp.isnot(None)
            ).count()
            
            print(f"📊 Database state before test:")
            print(f"   Total articles: {total_articles}")
            print(f"   Reddit articles: {reddit_articles}")
            print(f"   Twitter articles: {twitter_articles}")
            print(f"   Analyzed articles: {analyzed_articles}")
            
            return True
            
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False


def test_reddit_integration():
    """Test Reddit integration specifically."""
    print("\n" + "="*60)
    print("TESTING REDDIT INTEGRATION")
    print("="*60)
    
    try:
        # Create pipeline with only Reddit enabled
        config = PipelineConfig(
            enable_reddit=True,
            enable_twitter=False,
            reddit_max_posts_per_subreddit=3,
            enable_content_processing=True,
            save_to_database=True,
            max_workers=1,
            enable_progress_logging=True
        )
        
        pipeline = IntegratedScrapingPipeline(config)
        
        # Set up callbacks
        scraped_articles = []
        processed_articles = []
        
        def article_callback(article, analysis):
            if article.source_type == SourceType.REDDIT:
                scraped_articles.append(article)
                if analysis:
                    processed_articles.append((article, analysis))
        
        pipeline.add_article_callback(article_callback)
        
        # Run pipeline
        print("Running Reddit-only pipeline...")
        start_time = time.time()
        
        stats = pipeline.run_pipeline()
        
        elapsed_time = time.time() - start_time
        
        print(f"✅ Reddit integration test completed in {elapsed_time:.2f} seconds")
        print(f"   Articles scraped: {stats['total_articles_scraped']}")
        print(f"   Reddit articles: {stats['source_breakdown'].get('reddit', 0)}")
        print(f"   Articles processed: {stats['articles_processed']}")
        print(f"   Articles saved: {stats['articles_saved']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        
        pipeline.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Reddit integration test failed: {e}")
        return False


def test_twitter_integration():
    """Test Twitter integration specifically."""
    print("\n" + "="*60)
    print("TESTING TWITTER INTEGRATION")
    print("="*60)
    
    try:
        # Create pipeline with only Twitter enabled
        config = PipelineConfig(
            enable_reddit=False,
            enable_twitter=True,
            twitter_max_tweets_per_hashtag=3,
            enable_content_processing=True,
            save_to_database=True,
            max_workers=1,
            enable_progress_logging=True
        )
        
        pipeline = IntegratedScrapingPipeline(config)
        
        # Set up callbacks
        scraped_articles = []
        processed_articles = []
        
        def article_callback(article, analysis):
            if article.source_type == SourceType.TWITTER:
                scraped_articles.append(article)
                if analysis:
                    processed_articles.append((article, analysis))
        
        pipeline.add_article_callback(article_callback)
        
        # Run pipeline
        print("Running Twitter-only pipeline...")
        start_time = time.time()
        
        stats = pipeline.run_pipeline()
        
        elapsed_time = time.time() - start_time
        
        print(f"✅ Twitter integration test completed in {elapsed_time:.2f} seconds")
        print(f"   Articles scraped: {stats['total_articles_scraped']}")
        print(f"   Twitter articles: {stats['source_breakdown'].get('twitter', 0)}")
        print(f"   Articles processed: {stats['articles_processed']}")
        print(f"   Articles saved: {stats['articles_saved']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        
        pipeline.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Twitter integration test failed: {e}")
        return False


def test_full_integration():
    """Test full integration with both Reddit and Twitter."""
    print("\n" + "="*60)
    print("TESTING FULL INTEGRATION (REDDIT + TWITTER)")
    print("="*60)
    
    try:
        # Create pipeline with both sources enabled
        config = PipelineConfig(
            enable_reddit=True,
            enable_twitter=True,
            reddit_max_posts_per_subreddit=5,
            twitter_max_tweets_per_hashtag=5,
            enable_content_processing=True,
            save_to_database=True,
            max_workers=3,
            enable_progress_logging=True,
            progress_log_interval=2
        )
        
        pipeline = IntegratedScrapingPipeline(config)
        
        # Set up comprehensive callbacks
        all_articles = []
        reddit_articles = []
        twitter_articles = []
        processed_articles = []
        progress_updates = []
        
        def progress_callback(progress):
            progress_updates.append(progress)
            print(f"📈 Progress: {progress['articles_processed']}/{progress['total_articles']} "
                  f"({progress['progress_percentage']:.1f}%)")
        
        def article_callback(article, analysis):
            all_articles.append(article)
            
            if article.source_type == SourceType.REDDIT:
                reddit_articles.append(article)
            elif article.source_type == SourceType.TWITTER:
                twitter_articles.append(article)
            
            if analysis:
                processed_articles.append((article, analysis))
                print(f"✅ Processed: {article.title[:40]}... "
                      f"(Source: {article.source_type.value})")
        
        pipeline.add_progress_callback(progress_callback)
        pipeline.add_article_callback(article_callback)
        
        # Run full pipeline
        print("Running full integrated pipeline...")
        start_time = time.time()
        
        stats = pipeline.run_pipeline()
        
        elapsed_time = time.time() - start_time
        
        print(f"\n✅ Full integration test completed in {elapsed_time:.2f} seconds")
        print(f"   Total articles scraped: {stats['total_articles_scraped']}")
        print(f"   Reddit articles: {stats['source_breakdown'].get('reddit', 0)}")
        print(f"   Twitter articles: {stats['source_breakdown'].get('twitter', 0)}")
        print(f"   Unique articles: {stats['unique_articles']}")
        print(f"   Duplicates removed: {stats['duplicates_removed']}")
        print(f"   Articles processed: {stats['articles_processed']}")
        print(f"   Articles saved: {stats['articles_saved']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        print(f"   Database errors: {stats['database_errors']}")
        print(f"   Average processing time: {stats['average_processing_time_ms']:.2f}ms")
        
        # Verify results
        print(f"\n📊 Callback verification:")
        print(f"   Articles received via callback: {len(all_articles)}")
        print(f"   Reddit articles via callback: {len(reddit_articles)}")
        print(f"   Twitter articles via callback: {len(twitter_articles)}")
        print(f"   Processed articles via callback: {len(processed_articles)}")
        print(f"   Progress updates received: {len(progress_updates)}")
        
        pipeline.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Full integration test failed: {e}")
        return False


def test_database_verification():
    """Verify that articles were properly saved to database with analysis."""
    print("\n" + "="*60)
    print("TESTING DATABASE VERIFICATION")
    print("="*60)
    
    try:
        with db_manager.get_session() as session:
            # Count articles after integration test
            total_articles = session.query(Article).count()
            reddit_articles = session.query(Article).filter(
                Article.source_type == SourceType.REDDIT
            ).count()
            twitter_articles = session.query(Article).filter(
                Article.source_type == SourceType.TWITTER
            ).count()
            analyzed_articles = session.query(Article).filter(
                Article.analysis_timestamp.isnot(None)
            ).count()
            
            print(f"📊 Database state after integration test:")
            print(f"   Total articles: {total_articles}")
            print(f"   Reddit articles: {reddit_articles}")
            print(f"   Twitter articles: {twitter_articles}")
            print(f"   Analyzed articles: {analyzed_articles}")
            
            # Check recent articles with analysis
            recent_analyzed = session.query(Article).filter(
                Article.analysis_timestamp.isnot(None)
            ).order_by(Article.analysis_timestamp.desc()).limit(5).all()
            
            if recent_analyzed:
                print(f"\n📄 Recent analyzed articles:")
                for article in recent_analyzed:
                    print(f"   - {article.title[:50]}...")
                    print(f"     Source: {article.source_type.value}")
                    print(f"     Sentiment: {article.sentiment_label} "
                          f"(score: {article.sentiment_score:.2f})")
                    
                    if article.topics_analysis:
                        topics = [t.get('category', 'unknown') for t in article.topics_analysis[:2]]
                        print(f"     Topics: {', '.join(topics)}")
                    
                    print(f"     Analysis confidence: {article.analysis_confidence:.2f}")
                    print(f"     Analyzed at: {article.analysis_timestamp}")
                    print()
            
            return True
            
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False


def test_performance_monitoring():
    """Test performance monitoring and statistics."""
    print("\n" + "="*60)
    print("TESTING PERFORMANCE MONITORING")
    print("="*60)
    
    try:
        # Run a small performance test
        config = PipelineConfig(
            enable_reddit=True,
            enable_twitter=True,
            reddit_max_posts_per_subreddit=2,
            twitter_max_tweets_per_hashtag=2,
            enable_content_processing=True,
            save_to_database=True,
            max_workers=2,
            enable_progress_logging=True,
            processing_timeout_seconds=30
        )
        
        pipeline = IntegratedScrapingPipeline(config)
        
        # Monitor performance
        performance_data = []
        
        def performance_callback(progress):
            performance_data.append({
                'timestamp': datetime.now(timezone.utc),
                'articles_processed': progress['articles_processed'],
                'total_articles': progress['total_articles'],
                'progress_percentage': progress['progress_percentage']
            })
        
        pipeline.add_progress_callback(performance_callback)
        
        # Run pipeline with timing
        start_time = time.time()
        stats = pipeline.run_pipeline()
        elapsed_time = time.time() - start_time
        
        # Calculate performance metrics
        articles_per_second = stats['articles_processed'] / max(elapsed_time, 1)
        
        print(f"✅ Performance monitoring completed")
        print(f"   Total processing time: {elapsed_time:.2f} seconds")
        print(f"   Articles processed: {stats['articles_processed']}")
        print(f"   Processing rate: {articles_per_second:.2f} articles/second")
        print(f"   Average processing time: {stats['average_processing_time_ms']:.2f}ms")
        print(f"   Performance samples: {len(performance_data)}")
        
        if performance_data:
            print(f"   Progress tracking worked: {len(performance_data)} updates")
        
        pipeline.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {e}")
        return False


def main():
    """Run all integration tests."""
    print("🔄 FULL INTEGRATION TEST SUITE")
    print("=" * 80)
    
    # Check API credentials
    print("🔑 Checking API credentials...")
    reddit_configured = bool(settings.reddit.client_id and settings.reddit.client_secret)
    twitter_configured = bool(settings.twitter.bearer_token)
    
    print(f"   Reddit API: {'✅ Configured' if reddit_configured else '❌ Not configured'}")
    print(f"   Twitter API: {'✅ Configured' if twitter_configured else '❌ Not configured'}")
    
    if not (reddit_configured or twitter_configured):
        print("⚠️  WARNING: No API credentials configured. Tests may fail.")
    
    tests = [
        ("Pipeline Initialization", test_pipeline_initialization),
        ("Database Connection", test_database_connection),
        ("Reddit Integration", test_reddit_integration),
        ("Twitter Integration", test_twitter_integration),
        ("Full Integration (Reddit + Twitter)", test_full_integration),
        ("Database Verification", test_database_verification),
        ("Performance Monitoring", test_performance_monitoring)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! The complete pipeline is working correctly.")
        print("\n📋 Integration Summary:")
        print("   ✅ Reddit scraping and processing")
        print("   ✅ Twitter scraping and processing")
        print("   ✅ Content analysis (sentiment, topics, trends)")
        print("   ✅ Database storage with analysis results")
        print("   ✅ Performance monitoring and statistics")
    else:
        print("⚠️  Some integration tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
