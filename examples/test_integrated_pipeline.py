#!/usr/bin/env python3
"""
Test the integrated scraping pipeline with content processing and database storage.

This example demonstrates the complete pipeline:
1. Scraping from Reddit and Twitter
2. Content processing with sentiment analysis, topic categorization, and trend detection
3. Database storage with PostgreSQL
4. Duplicate detection and deduplication
"""

import logging
import time
from datetime import datetime
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from scrapers.enhanced.integrated_pipeline import create_integrated_pipeline, PipelineConfig
from models.connection import db_manager
from models.database import Article
from config.settings import settings


def test_pipeline_initialization():
    """Test pipeline initialization with different configurations."""
    print("\n" + "="*60)
    print("TESTING PIPELINE INITIALIZATION")
    print("="*60)
    
    try:
        # Test with all features enabled
        pipeline = create_integrated_pipeline(
            enable_reddit=True,
            enable_twitter=True,
            enable_content_processing=True,
            save_to_database=True
        )
        
        print("✅ Full pipeline initialized successfully")
        
        # Test with limited features
        limited_pipeline = create_integrated_pipeline(
            enable_reddit=True,
            enable_twitter=False,
            enable_content_processing=True,
            save_to_database=False
        )
        
        print("✅ Limited pipeline initialized successfully")
        
        # Test configuration
        config = PipelineConfig(
            reddit_subreddits=['MachineLearning', 'artificial'],
            reddit_max_posts_per_subreddit=10,
            twitter_hashtags=['#AI', '#ML'],
            max_workers=2,
            batch_size=5
        )
        
        custom_pipeline = create_integrated_pipeline()
        print("✅ Custom configuration pipeline initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline initialization failed: {e}")
        return False


def test_database_connection():
    """Test database connection and article model."""
    print("\n" + "="*60)
    print("TESTING DATABASE CONNECTION")
    print("="*60)
    
    try:
        # Test database connection
        with db_manager.get_session() as session:
            # Count existing articles
            article_count = session.query(Article).count()
            print(f"✅ Database connected. Found {article_count} existing articles")
            
            # Test article creation (without committing)
            test_article = Article(
                title="Test Article",
                content="This is a test article for pipeline testing",
                url="https://test.example.com/article/1",
                source_type="reddit",
                author="test_user"
            )
            
            # Add analysis fields
            test_article.sentiment_score = 0.5
            test_article.sentiment_label = "positive"
            test_article.sentiment_confidence = 0.8
            test_article.topics_analysis = [
                {"category": "AI", "confidence": 0.9, "subcategory": "machine_learning"}
            ]
            test_article.trends_analysis = [
                {"trend_type": "emerging", "description": "AI advancement", "confidence": 0.7}
            ]
            test_article.analysis_confidence = 0.8
            test_article.analysis_timestamp = datetime.now()
            
            print("✅ Test article with analysis fields created successfully")
            
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False


def test_content_processing_only():
    """Test content processing without scraping."""
    print("\n" + "="*60)
    print("TESTING CONTENT PROCESSING")
    print("="*60)
    
    try:
        # Create pipeline with processing enabled but no scraping
        pipeline = create_integrated_pipeline(
            enable_reddit=False,
            enable_twitter=False,
            enable_content_processing=True,
            save_to_database=False
        )
        
        # Create test articles
        from scrapers.base.content_parser import ParsedArticle
        
        test_articles = [
            ParsedArticle(
                title="Breakthrough in Large Language Models",
                content="Researchers have announced a significant breakthrough in large language model training, achieving unprecedented performance on various NLP tasks.",
                url="https://test.example.com/llm-breakthrough",
                author="ai_researcher",
                published_at=datetime.now()
            ),
            ParsedArticle(
                title="Concerns About AI Safety",
                content="Experts are raising concerns about the rapid development of AI systems and the need for better safety measures and regulations.",
                url="https://test.example.com/ai-safety-concerns",
                author="safety_expert",
                published_at=datetime.now()
            )
        ]
        
        print(f"Created {len(test_articles)} test articles")
        
        # Process articles manually
        processed_count = 0
        for article in test_articles:
            try:
                analysis = pipeline.content_processor.analyze(article)
                processed_count += 1
                
                print(f"\n📄 Processed: {article.title}")
                
                # Show sentiment analysis
                from processing.base.analyzer import AnalysisType
                sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
                if sentiment_result:
                    print(f"   Sentiment: {sentiment_result.data.get('label')} "
                          f"(score: {sentiment_result.data.get('score', 0):.2f}, "
                          f"confidence: {sentiment_result.confidence:.2f})")
                
                # Show topic categorization
                topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
                if topic_results:
                    print(f"   Topics: {len(topic_results)} categories detected")
                    for topic in topic_results[:3]:  # Show top 3
                        print(f"     - {topic.data.get('category')} "
                              f"(confidence: {topic.confidence:.2f})")
                
                # Show trend detection
                trend_results = analysis.get_results_by_type(AnalysisType.TREND)
                if trend_results:
                    print(f"   Trends: {len(trend_results)} trends detected")
                    for trend in trend_results[:2]:  # Show top 2
                        print(f"     - {trend.data.get('trend_type')}: "
                              f"{trend.data.get('description', '')[:50]}...")
                
            except Exception as e:
                print(f"❌ Error processing article: {e}")
        
        print(f"\n✅ Successfully processed {processed_count}/{len(test_articles)} articles")
        return True
        
    except Exception as e:
        print(f"❌ Content processing test failed: {e}")
        return False


def test_limited_scraping_pipeline():
    """Test pipeline with limited scraping (small scale)."""
    print("\n" + "="*60)
    print("TESTING LIMITED SCRAPING PIPELINE")
    print("="*60)
    
    try:
        # Create pipeline with limited scope
        config = PipelineConfig(
            enable_reddit=True,
            enable_twitter=True,
            reddit_subreddits=['MachineLearning'],  # Only one subreddit
            reddit_max_posts_per_subreddit=5,      # Only 5 posts
            twitter_hashtags=['#AI'],               # Only one hashtag
            twitter_max_tweets_per_hashtag=5,       # Only 5 tweets
            enable_content_processing=True,
            save_to_database=False,  # Don't save to avoid cluttering database
            max_workers=2,
            batch_size=3
        )
        
        from scrapers.enhanced.integrated_pipeline import IntegratedScrapingPipeline
        pipeline = IntegratedScrapingPipeline(config)
        
        print("Pipeline created with limited scope")
        
        # Set up callbacks to monitor progress
        articles_scraped = []
        analyses_completed = []
        
        def article_callback(article):
            articles_scraped.append(article)
            print(f"📥 Scraped: {article.title[:50]}...")
        
        def analysis_callback(article, analysis):
            analyses_completed.append((article, analysis))
            print(f"🔍 Analyzed: {article.title[:50]}...")
        
        pipeline.add_article_callback(article_callback)
        pipeline.add_analysis_callback(analysis_callback)
        
        # Run the pipeline
        print("Running limited pipeline...")
        start_time = time.time()
        
        stats = pipeline.run_pipeline()
        
        elapsed_time = time.time() - start_time
        
        print(f"\n✅ Pipeline completed in {elapsed_time:.2f} seconds")
        print(f"   Articles scraped: {len(articles_scraped)}")
        print(f"   Articles analyzed: {len(analyses_completed)}")
        
        # Show pipeline statistics
        print(f"\nPipeline Statistics:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        pipeline.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Limited scraping pipeline test failed: {e}")
        return False


def test_database_integration():
    """Test full pipeline with database integration."""
    print("\n" + "="*60)
    print("TESTING DATABASE INTEGRATION")
    print("="*60)
    
    try:
        # Count articles before
        with db_manager.get_session() as session:
            initial_count = session.query(Article).count()
        
        print(f"Initial article count: {initial_count}")
        
        # Create pipeline with database saving enabled
        config = PipelineConfig(
            enable_reddit=True,
            enable_twitter=False,  # Disable Twitter to reduce scope
            reddit_subreddits=['artificial'],  # Small subreddit
            reddit_max_posts_per_subreddit=3,  # Very limited
            enable_content_processing=True,
            save_to_database=True,
            max_workers=1,
            batch_size=2
        )
        
        from scrapers.enhanced.integrated_pipeline import IntegratedScrapingPipeline
        pipeline = IntegratedScrapingPipeline(config)
        
        # Run pipeline
        print("Running pipeline with database integration...")
        stats = pipeline.run_pipeline()
        
        # Count articles after
        with db_manager.get_session() as session:
            final_count = session.query(Article).count()
            
            # Show some recent articles with analysis
            recent_articles = session.query(Article).filter(
                Article.analysis_timestamp.isnot(None)
            ).order_by(Article.scraped_at.desc()).limit(3).all()
            
            print(f"\nFinal article count: {final_count}")
            print(f"Articles added: {final_count - initial_count}")
            
            if recent_articles:
                print(f"\nRecent articles with analysis:")
                for article in recent_articles:
                    print(f"  📄 {article.title[:50]}...")
                    print(f"     Sentiment: {article.sentiment_label} "
                          f"(score: {article.sentiment_score or 0:.2f})")
                    if article.topics_analysis:
                        topics = [t.get('category', 'unknown') for t in article.topics_analysis[:2]]
                        print(f"     Topics: {', '.join(topics)}")
        
        pipeline.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        return False


def main():
    """Run all integrated pipeline tests."""
    print("🔄 INTEGRATED SCRAPING PIPELINE TEST SUITE")
    print("=" * 80)
    
    # Check prerequisites
    print("Checking prerequisites...")
    
    if not settings.reddit.client_id or not settings.reddit.client_secret:
        print("⚠️  WARNING: Reddit credentials not found!")
        print("   Set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET to run Reddit tests")
    
    if not settings.twitter.bearer_token:
        print("⚠️  WARNING: Twitter credentials not found!")
        print("   Set TWITTER_BEARER_TOKEN to run Twitter tests")
    
    tests = [
        ("Pipeline Initialization", test_pipeline_initialization),
        ("Database Connection", test_database_connection),
        ("Content Processing Only", test_content_processing_only),
        ("Limited Scraping Pipeline", test_limited_scraping_pipeline),
        ("Database Integration", test_database_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Integrated pipeline is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
