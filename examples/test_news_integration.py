"""
Test script for Phase 7: News API Integration
Tests NewsAPI and Guardian API scrapers with prioritization and content extraction.
"""

import sys
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrapers.sources.newsapi_scraper import NewsAPIScraper, NewsAPIScraperConfig
from scrapers.sources.guardian_scraper import GuardianScraper, GuardianScraperConfig
from scrapers.news_prioritizer import NewsSourcePrioritizer, SourceCredibilityConfig
from scrapers.content_extractor import ContentExtractor
from models.connection import db_manager
from models.database import Article
from processing.sentiment.analyzer import SentimentAnalyzer
from processing.topics.categorizer import TopicCategorizer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_newsapi_integration():
    """Test NewsAPI integration."""
    logger.info("=== Testing NewsAPI Integration ===")
    
    try:
        # Create NewsAPI scraper configuration
        config = NewsAPIScraperConfig(
            name="NewsAPI Test",
            keywords=["artificial intelligence", "machine learning", "ChatGPT"],
            max_pages=2,  # Limit for testing
            days_back=3
        )
        
        # Create scraper
        scraper = NewsAPIScraper(config)
        
        # Get article URLs
        logger.info("Fetching article URLs from NewsAPI...")
        urls = scraper.get_article_urls()
        logger.info(f"Found {len(urls)} URLs from NewsAPI")
        
        if urls:
            # Test scraping a few articles
            articles = []
            for i, url in enumerate(urls[:5]):  # Test first 5 articles
                logger.info(f"Scraping article {i+1}: {url}")
                article = scraper.scrape_article(url)
                if article:
                    articles.append(article)
                    logger.info(f"Successfully scraped: {article.title[:100]}...")
                else:
                    logger.warning(f"Failed to scrape: {url}")
            
            logger.info(f"Successfully scraped {len(articles)} articles from NewsAPI")
            return articles
        else:
            logger.warning("No URLs found from NewsAPI")
            return []
            
    except Exception as e:
        logger.error(f"Error testing NewsAPI integration: {e}")
        return []


def test_guardian_integration():
    """Test Guardian API integration."""
    logger.info("=== Testing Guardian API Integration ===")
    
    try:
        # Create Guardian scraper configuration
        config = GuardianScraperConfig(
            name="Guardian Test",
            keywords=["artificial intelligence", "technology", "AI"],
            max_pages=2,  # Limit for testing
            days_back=3
        )
        
        # Create scraper
        scraper = GuardianScraper(config)
        
        # Get article URLs
        logger.info("Fetching article URLs from Guardian API...")
        urls = scraper.get_article_urls()
        logger.info(f"Found {len(urls)} URLs from Guardian API")
        
        if urls:
            # Test scraping a few articles
            articles = []
            for i, url in enumerate(urls[:5]):  # Test first 5 articles
                logger.info(f"Scraping article {i+1}: {url}")
                article = scraper.scrape_article(url)
                if article:
                    articles.append(article)
                    logger.info(f"Successfully scraped: {article.title[:100]}...")
                else:
                    logger.warning(f"Failed to scrape: {url}")
            
            logger.info(f"Successfully scraped {len(articles)} articles from Guardian API")
            return articles
        else:
            logger.warning("No URLs found from Guardian API")
            return []
            
    except Exception as e:
        logger.error(f"Error testing Guardian API integration: {e}")
        return []


def test_news_prioritization(articles):
    """Test news source prioritization."""
    logger.info("=== Testing News Source Prioritization ===")
    
    try:
        # Create prioritizer
        prioritizer = NewsSourcePrioritizer()
        
        # Calculate scores for all articles
        scored_articles = prioritizer.prioritize_articles(articles)
        
        logger.info("Article Priority Scores:")
        for i, (article, score) in enumerate(scored_articles[:10]):  # Show top 10
            logger.info(f"{i+1}. Score: {score:.3f} - {article.source_name} - {article.title[:80]}...")
        
        # Filter by quality threshold
        high_quality_articles = prioritizer.filter_by_quality_threshold(articles, threshold=0.6)
        logger.info(f"High quality articles (>0.6): {len(high_quality_articles)}/{len(articles)}")
        
        # Analyze source distribution
        source_analysis = prioritizer.analyze_source_distribution(articles)
        logger.info(f"Source Distribution Analysis:")
        logger.info(f"Total articles: {source_analysis['total_articles']}")
        logger.info(f"Unique sources: {source_analysis['unique_sources']}")
        
        for source, stats in list(source_analysis['source_breakdown'].items())[:5]:
            logger.info(f"  {source}: {stats['count']} articles ({stats['percentage']:.1f}%), "
                       f"credibility: {stats['avg_credibility']:.2f}, quality: {stats['avg_quality']:.2f}")
        
        return high_quality_articles
        
    except Exception as e:
        logger.error(f"Error testing news prioritization: {e}")
        return articles


def test_content_extraction(articles):
    """Test enhanced content extraction."""
    logger.info("=== Testing Content Extraction ===")
    
    try:
        # Create content extractor
        extractor = ContentExtractor()
        
        # Test extraction on a few articles
        enhanced_articles = []
        for i, article in enumerate(articles[:3]):  # Test first 3 articles
            logger.info(f"Enhancing article {i+1}: {article.url}")
            
            original_length = len(article.content or "")
            enhanced_article = extractor.enhance_article(article)
            new_length = len(enhanced_article.content or "")
            
            if new_length > original_length:
                logger.info(f"Content enhanced: {original_length} -> {new_length} characters")
            else:
                logger.info(f"Content unchanged: {original_length} characters")
            
            enhanced_articles.append(enhanced_article)
        
        return enhanced_articles
        
    except Exception as e:
        logger.error(f"Error testing content extraction: {e}")
        return articles


def test_database_integration(articles):
    """Test database integration with news articles."""
    logger.info("=== Testing Database Integration ===")
    
    try:
        # Initialize analysis components
        sentiment_analyzer = SentimentAnalyzer()
        topic_categorizer = TopicCategorizer()

        # Initialize analyzers
        if not sentiment_analyzer.initialize():
            logger.error("Failed to initialize sentiment analyzer")
            return 0
        if not topic_categorizer.initialize():
            logger.error("Failed to initialize topic categorizer")
            return 0

        # Process and store articles
        stored_count = 0
        with db_manager.get_session() as session:
            for article in articles:
                try:
                    # Analyze article
                    sentiment_result = sentiment_analyzer.analyze(article)
                    topic_result = topic_categorizer.analyze(article)

                    # Create database article
                    from models.database import SourceType
                    db_article = Article(
                        title=article.title,
                        content=article.content,
                        url=article.url,
                        source_type=SourceType.NEWS,
                        source_id=getattr(article, 'source_id', 'unknown'),
                        published_at=article.published_at,
                        sentiment_score=sentiment_result.data.get('score', 0.0),
                        sentiment_label=sentiment_result.data.get('polarity', 'neutral'),
                        sentiment_confidence=sentiment_result.confidence,
                        topics_analysis=topic_result.data,
                        analysis_confidence=(sentiment_result.confidence + topic_result.confidence) / 2,
                        analysis_timestamp=datetime.now(),
                        engagement_metrics={
                            'credibility_score': 0.9,
                            'api_source': getattr(article, 'source_id', 'unknown')
                        }
                    )

                    # Check if article already exists
                    existing = session.query(Article).filter_by(url=db_article.url).first()
                    if not existing:
                        session.add(db_article)
                        stored_count += 1

                        logger.info(f"Stored article: {article.title[:80]}... "
                                   f"(sentiment: {sentiment_result.data.get('polarity', 'neutral')}, "
                                   f"confidence: {sentiment_result.confidence:.2f})")
                    else:
                        logger.debug(f"Article already exists: {article.url}")

                except Exception as e:
                    logger.warning(f"Error storing article {article.url}: {e}")

        logger.info(f"Successfully stored {stored_count}/{len(articles)} articles in database")

        # Query recent articles
        with db_manager.get_session() as session:
            recent_articles = session.query(Article).order_by(Article.scraped_at.desc()).limit(10).all()
            logger.info(f"Retrieved {len(recent_articles)} recent articles from database")

        return stored_count
        
    except Exception as e:
        logger.error(f"Error testing database integration: {e}")
        return 0


def main():
    """Main test function."""
    logger.info("Starting Phase 7: News API Integration Test")
    start_time = datetime.now()
    
    all_articles = []
    
    # Test NewsAPI
    newsapi_articles = test_newsapi_integration()
    all_articles.extend(newsapi_articles)
    
    # Test Guardian API
    guardian_articles = test_guardian_integration()
    all_articles.extend(guardian_articles)
    
    if not all_articles:
        logger.error("No articles collected from any news API. Check API keys and configuration.")
        return
    
    logger.info(f"Total articles collected: {len(all_articles)}")
    
    # Test prioritization
    prioritized_articles = test_news_prioritization(all_articles)
    
    # Test content extraction (on a subset)
    enhanced_articles = test_content_extraction(prioritized_articles[:5])
    
    # Test database integration
    stored_count = test_database_integration(prioritized_articles)
    
    # Summary
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    logger.info("=== Phase 7 Integration Test Summary ===")
    logger.info(f"Test duration: {duration:.1f} seconds")
    logger.info(f"NewsAPI articles: {len(newsapi_articles)}")
    logger.info(f"Guardian articles: {len(guardian_articles)}")
    logger.info(f"Total articles: {len(all_articles)}")
    logger.info(f"High-quality articles: {len(prioritized_articles)}")
    logger.info(f"Enhanced articles: {len(enhanced_articles)}")
    logger.info(f"Stored in database: {stored_count}")
    logger.info("Phase 7: News API Integration test completed successfully!")


if __name__ == "__main__":
    main()
