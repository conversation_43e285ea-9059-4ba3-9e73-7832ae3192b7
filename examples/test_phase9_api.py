#!/usr/bin/env python3
"""
Test script for Phase 9: API & Search Capabilities

Tests:
- Enhanced search endpoints
- API authentication and rate limiting
- Caching functionality
- API key management
- Advanced filtering and pagination
"""

import asyncio
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

import httpx
from pydantic import BaseModel

# Test configuration
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_TIMEOUT = 30


class TestResults:
    """Track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_result(self, test_name: str, success: bool, error: str = None):
        if success:
            self.passed += 1
            print(f"✅ {test_name}")
        else:
            self.failed += 1
            self.errors.append(f"{test_name}: {error}")
            print(f"❌ {test_name}: {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n📊 Test Summary: {self.passed}/{total} passed")
        if self.errors:
            print("\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")


async def test_api_health():
    """Test API health endpoints."""
    results = TestResults()
    
    async with httpx.AsyncClient(timeout=TEST_TIMEOUT) as client:
        try:
            # Test basic health check
            response = await client.get("http://localhost:8000/health")
            results.add_result(
                "Basic health check",
                response.status_code == 200,
                f"Status: {response.status_code}"
            )
            
            # Test detailed health check
            response = await client.get(f"{API_BASE_URL}/admin/health/detailed")
            results.add_result(
                "Detailed health check",
                response.status_code in [200, 401],  # 401 is OK (auth required)
                f"Status: {response.status_code}"
            )
            
        except Exception as e:
            results.add_result("API health tests", False, str(e))
    
    return results


async def test_search_endpoints():
    """Test enhanced search endpoints."""
    results = TestResults()
    
    async with httpx.AsyncClient(timeout=TEST_TIMEOUT) as client:
        try:
            # Test basic article listing
            response = await client.get(f"{API_BASE_URL}/search/")
            results.add_result(
                "Basic article listing",
                response.status_code in [200, 429],  # 429 = rate limited
                f"Status: {response.status_code}"
            )
            
            # Test search with query
            response = await client.get(f"{API_BASE_URL}/search/?q=AI&limit=10")
            results.add_result(
                "Search with query",
                response.status_code in [200, 429],
                f"Status: {response.status_code}"
            )
            
            # Test facets endpoint
            response = await client.get(f"{API_BASE_URL}/search/facets")
            results.add_result(
                "Search facets",
                response.status_code in [200, 429],
                f"Status: {response.status_code}"
            )
            
            # Test suggestions endpoint
            response = await client.get(f"{API_BASE_URL}/search/suggestions?q=AI")
            results.add_result(
                "Search suggestions",
                response.status_code in [200, 429],
                f"Status: {response.status_code}"
            )
            
            # Test trending endpoint
            response = await client.get(f"{API_BASE_URL}/search/trending")
            results.add_result(
                "Trending searches",
                response.status_code in [200, 429],
                f"Status: {response.status_code}"
            )
            
            # Test advanced search with POST
            search_request = {
                "filters": {
                    "query": "machine learning",
                    "min_relevance_score": 0.5,
                    "has_analysis": True
                },
                "sort": [{"field": "created_at", "direction": "desc"}],
                "pagination": {"skip": 0, "limit": 20},
                "include_facets": True
            }
            
            response = await client.post(
                f"{API_BASE_URL}/search/search",
                json=search_request
            )
            results.add_result(
                "Advanced search POST",
                response.status_code in [200, 429],
                f"Status: {response.status_code}"
            )
            
        except Exception as e:
            results.add_result("Search endpoints tests", False, str(e))
    
    return results


async def test_rate_limiting():
    """Test rate limiting functionality."""
    results = TestResults()
    
    async with httpx.AsyncClient(timeout=TEST_TIMEOUT) as client:
        try:
            # Make multiple rapid requests to test rate limiting
            responses = []
            for i in range(5):
                response = await client.get(f"{API_BASE_URL}/search/?limit=1")
                responses.append(response.status_code)
                
                # Check for rate limit headers
                if "X-RateLimit-Limit" in response.headers:
                    results.add_result(
                        "Rate limit headers present",
                        True
                    )
                    break
            
            # Check if we got rate limited or normal responses
            has_rate_limit = any(status == 429 for status in responses)
            has_success = any(status == 200 for status in responses)
            
            results.add_result(
                "Rate limiting functional",
                has_success or has_rate_limit,
                f"Response codes: {responses}"
            )
            
        except Exception as e:
            results.add_result("Rate limiting tests", False, str(e))
    
    return results


async def test_caching():
    """Test caching functionality."""
    results = TestResults()
    
    async with httpx.AsyncClient(timeout=TEST_TIMEOUT) as client:
        try:
            # Make the same request twice to test caching
            start_time = time.time()
            response1 = await client.get(f"{API_BASE_URL}/search/facets")
            first_time = time.time() - start_time
            
            start_time = time.time()
            response2 = await client.get(f"{API_BASE_URL}/search/facets")
            second_time = time.time() - start_time
            
            # Second request should be faster (cached)
            if response1.status_code == 200 and response2.status_code == 200:
                results.add_result(
                    "Caching improves performance",
                    second_time < first_time * 0.8,  # At least 20% faster
                    f"First: {first_time:.3f}s, Second: {second_time:.3f}s"
                )
            else:
                results.add_result(
                    "Caching test",
                    response1.status_code in [200, 429],
                    f"Status: {response1.status_code}"
                )
            
        except Exception as e:
            results.add_result("Caching tests", False, str(e))
    
    return results


async def test_authentication():
    """Test authentication endpoints."""
    results = TestResults()
    
    async with httpx.AsyncClient(timeout=TEST_TIMEOUT) as client:
        try:
            # Test admin endpoints (should require auth)
            response = await client.get(f"{API_BASE_URL}/admin/api-keys")
            results.add_result(
                "Admin endpoints require auth",
                response.status_code == 401,
                f"Status: {response.status_code}"
            )
            
            # Test rate limit status endpoint
            response = await client.get(f"{API_BASE_URL}/admin/rate-limits")
            results.add_result(
                "Rate limit status requires auth",
                response.status_code == 401,
                f"Status: {response.status_code}"
            )
            
        except Exception as e:
            results.add_result("Authentication tests", False, str(e))
    
    return results


async def main():
    """Run all Phase 9 tests."""
    
    print("🚀 Starting Phase 9: API & Search Capabilities Tests")
    print("=" * 60)
    
    # Run all test suites
    test_suites = [
        ("API Health", test_api_health),
        ("Search Endpoints", test_search_endpoints),
        ("Rate Limiting", test_rate_limiting),
        ("Caching", test_caching),
        ("Authentication", test_authentication),
    ]
    
    overall_results = TestResults()
    
    for suite_name, test_func in test_suites:
        print(f"\n📋 Testing {suite_name}...")
        suite_results = await test_func()
        
        # Aggregate results
        overall_results.passed += suite_results.passed
        overall_results.failed += suite_results.failed
        overall_results.errors.extend(suite_results.errors)
    
    print("\n" + "=" * 60)
    overall_results.summary()
    
    if overall_results.failed == 0:
        print("\n🎉 All Phase 9 tests passed! API & Search Capabilities are working correctly.")
    else:
        print(f"\n⚠️  {overall_results.failed} tests failed. Please check the implementation.")
    
    return overall_results.failed == 0


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
