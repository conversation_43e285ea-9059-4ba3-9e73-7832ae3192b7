#!/usr/bin/env python3
"""
Test Reddit API credentials and basic functionality.

This script verifies that your Reddit API credentials are working
and tests basic Reddit scraping functionality.
"""

import logging
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from config.settings import settings
from scrapers.sources.reddit_scraper import RedditScraper
from scrapers.monitoring.reddit_monitor import RedditMonitor


def test_reddit_credentials():
    """Test Reddit API credentials."""
    print("🔑 TESTING REDDIT API CREDENTIALS")
    print("=" * 50)
    
    try:
        # Check if credentials are configured
        if not settings.reddit.client_id or settings.reddit.client_id == "your_reddit_client_id_here":
            print("❌ Reddit Client ID not configured in .env file")
            return False
        
        if not settings.reddit.client_secret or settings.reddit.client_secret == "your_reddit_client_secret_here":
            print("❌ Reddit Client Secret not configured in .env file")
            return False
        
        print(f"✅ Reddit Client ID: {settings.reddit.client_id[:8]}...")
        print(f"✅ Reddit Client Secret: {settings.reddit.client_secret[:8]}...")
        print(f"✅ Reddit User Agent: {settings.reddit.user_agent}")
        
        # Test Reddit connection
        scraper = RedditScraper()
        
        # Test basic Reddit API access
        print("\n🔍 Testing Reddit API connection...")
        
        # This will test the PRAW connection
        reddit_instance = scraper._get_reddit_instance()
        
        # Test by getting user info (this requires valid credentials)
        user = reddit_instance.user.me()
        if user:
            print(f"✅ Connected as Reddit user: {user.name}")
        else:
            print("✅ Connected to Reddit API (read-only access)")
        
        print("✅ Reddit API credentials are working!")
        return True
        
    except Exception as e:
        print(f"❌ Reddit API test failed: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure you've created a Reddit app at https://www.reddit.com/prefs/apps")
        print("2. Use 'script' type for the app")
        print("3. Copy the client ID (under the app name) and secret correctly")
        print("4. Make sure your .env file is in the project root directory")
        return False


def test_reddit_scraping():
    """Test basic Reddit scraping functionality."""
    print("\n🕷️ TESTING REDDIT SCRAPING")
    print("=" * 50)
    
    try:
        scraper = RedditScraper()
        
        # Test scraping from a popular AI subreddit
        print("Testing scraping from r/MachineLearning...")
        
        articles = scraper.scrape_subreddit(
            subreddit_name="MachineLearning",
            limit=3,
            sort_by="hot"
        )
        
        print(f"✅ Successfully scraped {len(articles)} articles")
        
        for i, article in enumerate(articles, 1):
            print(f"\n📄 Article {i}:")
            print(f"   Title: {article.title[:60]}...")
            print(f"   Author: {article.author}")
            print(f"   URL: {article.url}")
            print(f"   Score: {getattr(article, 'score', 'N/A')}")
            print(f"   Comments: {getattr(article, 'num_comments', 'N/A')}")
            print(f"   Content length: {len(article.content or '')} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit scraping test failed: {e}")
        return False


def test_reddit_monitoring():
    """Test Reddit monitoring functionality."""
    print("\n📡 TESTING REDDIT MONITORING")
    print("=" * 50)
    
    try:
        monitor = RedditMonitor()
        
        # Test monitoring configuration
        print("Testing monitoring configuration...")
        
        # Get default subreddits to monitor
        subreddits = [
            "MachineLearning",
            "artificial", 
            "deeplearning",
            "ChatGPT",
            "OpenAI"
        ]
        
        print(f"✅ Configured to monitor {len(subreddits)} subreddits:")
        for subreddit in subreddits:
            print(f"   - r/{subreddit}")
        
        # Test getting hot posts from one subreddit
        print(f"\nTesting hot posts from r/artificial...")
        
        hot_posts = monitor.get_hot_posts("artificial", limit=2)
        
        print(f"✅ Found {len(hot_posts)} hot posts")
        
        for i, post in enumerate(hot_posts, 1):
            print(f"\n🔥 Hot Post {i}:")
            print(f"   Title: {post.title[:60]}...")
            print(f"   Score: {post.score}")
            print(f"   Comments: {post.num_comments}")
            print(f"   Created: {post.created_utc}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit monitoring test failed: {e}")
        return False


def main():
    """Run all Reddit API tests."""
    print("🔄 REDDIT API TEST SUITE")
    print("=" * 80)
    
    tests = [
        ("Reddit Credentials", test_reddit_credentials),
        ("Reddit Scraping", test_reddit_scraping),
        ("Reddit Monitoring", test_reddit_monitoring)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Reddit API tests passed! Your Reddit integration is ready.")
        print("\n📋 Next steps:")
        print("   1. Run the integrated pipeline: python examples/test_integrated_pipeline.py")
        print("   2. Test the full integration: python examples/test_full_integration.py")
        print("   3. Start the API server: python -m api.main")
    else:
        print("⚠️  Some Reddit API tests failed. Check your credentials and configuration.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
