#!/usr/bin/env python3
"""
Test script for Reddit duplicate detection system.

This script tests:
- Exact Reddit ID duplicate detection
- URL-based duplicate detection
- Content similarity detection
- Title similarity detection
- Temporal clustering
- Integration with Reddit scraper
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from scrapers.deduplication.reddit_deduplicator import RedditDeduplicator, DuplicateMatch
from scrapers.sources.reddit_scraper import create_reddit_scraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_exact_id_duplicates():
    """Test exact Reddit ID duplicate detection."""
    print("🔍 Testing Exact ID Duplicate Detection")
    print("=" * 50)
    
    deduplicator = RedditDeduplicator()
    
    # Create test posts with same Reddit ID
    base_time = datetime.now()
    
    post1 = {
        'id': 'post_1',
        'reddit_id': 'abc123',
        'title': 'First post about AI',
        'content': 'This is about artificial intelligence',
        'url': 'https://reddit.com/r/MachineLearning/comments/abc123',
        'subreddit': 'MachineLearning',
        'author': 'user1',
        'created_utc': base_time
    }
    
    post2 = {
        'id': 'post_2',
        'reddit_id': 'abc123',  # Same Reddit ID
        'title': 'First post about AI',
        'content': 'This is about artificial intelligence',
        'url': 'https://reddit.com/r/artificial/comments/abc123',
        'subreddit': 'artificial',
        'author': 'user1',
        'created_utc': base_time + timedelta(minutes=5)
    }
    
    # Add first post
    deduplicator.add_post(post1)
    print(f"✅ Added post 1: {post1['title']}")
    
    # Check for duplicates when adding second post
    duplicates = deduplicator.find_duplicates(post2)
    
    if duplicates:
        print(f"✅ Found {len(duplicates)} exact ID duplicate(s)")
        for dup in duplicates:
            print(f"   Match type: {dup.match_type}")
            print(f"   Confidence: {dup.confidence}")
            print(f"   Details: {dup.details}")
    else:
        print("❌ No exact ID duplicates found")
    
    return len(duplicates) > 0


def test_url_duplicates():
    """Test URL-based duplicate detection."""
    print("\n🔗 Testing URL-Based Duplicate Detection")
    print("=" * 50)
    
    deduplicator = RedditDeduplicator()
    
    base_time = datetime.now()
    
    # Posts with same external URL
    post1 = {
        'id': 'post_1',
        'reddit_id': 'def456',
        'title': 'Interesting AI article',
        'content': 'Link: https://example.com/ai-article',
        'url': 'https://reddit.com/r/MachineLearning/comments/def456',
        'external_url': 'https://example.com/ai-article',
        'subreddit': 'MachineLearning',
        'author': 'user1',
        'created_utc': base_time
    }
    
    post2 = {
        'id': 'post_2',
        'reddit_id': 'ghi789',
        'title': 'Great AI article',  # Different title
        'content': 'Link: https://example.com/ai-article',
        'url': 'https://reddit.com/r/artificial/comments/ghi789',
        'external_url': 'https://example.com/ai-article',  # Same URL
        'subreddit': 'artificial',
        'author': 'user2',
        'created_utc': base_time + timedelta(hours=2)
    }
    
    # Add first post
    deduplicator.add_post(post1)
    print(f"✅ Added post 1: {post1['title']}")
    
    # Check for duplicates
    duplicates = deduplicator.find_duplicates(post2)
    
    url_duplicates = [d for d in duplicates if d.match_type == 'url_match']
    
    if url_duplicates:
        print(f"✅ Found {len(url_duplicates)} URL duplicate(s)")
        for dup in url_duplicates:
            print(f"   Confidence: {dup.confidence}")
            print(f"   Similarity: {dup.similarity_score}")
    else:
        print("❌ No URL duplicates found")
    
    return len(url_duplicates) > 0


def test_content_similarity():
    """Test content similarity detection."""
    print("\n📝 Testing Content Similarity Detection")
    print("=" * 50)
    
    deduplicator = RedditDeduplicator()
    
    base_time = datetime.now()
    
    # Posts with similar content
    post1 = {
        'id': 'post_1',
        'reddit_id': 'jkl012',
        'title': 'New breakthrough in machine learning',
        'content': 'Researchers at Stanford have developed a new neural network architecture that significantly improves performance on natural language tasks.',
        'url': 'https://reddit.com/r/MachineLearning/comments/jkl012',
        'subreddit': 'MachineLearning',
        'author': 'researcher1',
        'created_utc': base_time
    }
    
    post2 = {
        'id': 'post_2',
        'reddit_id': 'mno345',
        'title': 'Stanford breakthrough in ML',
        'content': 'Stanford researchers developed a new neural network architecture that greatly improves performance on NLP tasks.',  # Very similar content
        'url': 'https://reddit.com/r/artificial/comments/mno345',
        'subreddit': 'artificial',
        'author': 'researcher2',
        'created_utc': base_time + timedelta(hours=1)
    }
    
    # Add first post
    deduplicator.add_post(post1)
    print(f"✅ Added post 1: {post1['title']}")
    
    # Check for duplicates
    duplicates = deduplicator.find_duplicates(post2)
    
    content_duplicates = [d for d in duplicates if d.match_type == 'content_similarity']
    
    if content_duplicates:
        print(f"✅ Found {len(content_duplicates)} content similarity duplicate(s)")
        for dup in content_duplicates:
            print(f"   Confidence: {dup.confidence}")
            print(f"   Similarity: {dup.similarity_score}")
    else:
        print("❌ No content similarity duplicates found")
    
    return len(content_duplicates) > 0


def test_title_similarity():
    """Test title similarity detection."""
    print("\n📰 Testing Title Similarity Detection")
    print("=" * 50)
    
    deduplicator = RedditDeduplicator()
    
    base_time = datetime.now()
    
    # Posts with similar titles
    post1 = {
        'id': 'post_1',
        'reddit_id': 'pqr678',
        'title': 'GPT-4 shows remarkable performance on coding tasks',
        'content': 'Different content about GPT-4 coding abilities',
        'url': 'https://reddit.com/r/MachineLearning/comments/pqr678',
        'subreddit': 'MachineLearning',
        'author': 'coder1',
        'created_utc': base_time
    }
    
    post2 = {
        'id': 'post_2',
        'reddit_id': 'stu901',
        'title': 'GPT-4 demonstrates remarkable performance on coding tasks',  # Very similar title
        'content': 'Completely different content about something else',
        'url': 'https://reddit.com/r/artificial/comments/stu901',
        'subreddit': 'artificial',
        'author': 'coder2',
        'created_utc': base_time + timedelta(hours=3)
    }
    
    # Add first post
    deduplicator.add_post(post1)
    print(f"✅ Added post 1: {post1['title']}")
    
    # Check for duplicates
    duplicates = deduplicator.find_duplicates(post2)
    
    title_duplicates = [d for d in duplicates if d.match_type == 'title_similarity']
    
    if title_duplicates:
        print(f"✅ Found {len(title_duplicates)} title similarity duplicate(s)")
        for dup in title_duplicates:
            print(f"   Confidence: {dup.confidence}")
            print(f"   Similarity: {dup.similarity_score}")
    else:
        print("❌ No title similarity duplicates found")
    
    return len(title_duplicates) > 0


def test_duplicate_clusters():
    """Test duplicate clustering functionality."""
    print("\n🔗 Testing Duplicate Clustering")
    print("=" * 50)
    
    deduplicator = RedditDeduplicator()
    
    base_time = datetime.now()
    
    # Create a cluster of related posts
    posts = [
        {
            'id': 'post_1',
            'reddit_id': 'cluster1',
            'title': 'OpenAI releases new model',
            'content': 'OpenAI has released a new language model',
            'url': 'https://reddit.com/r/MachineLearning/comments/cluster1',
            'subreddit': 'MachineLearning',
            'author': 'user1',
            'created_utc': base_time
        },
        {
            'id': 'post_2',
            'reddit_id': 'cluster2',
            'title': 'OpenAI releases new model',  # Same title
            'content': 'OpenAI released a new language model today',  # Similar content
            'url': 'https://reddit.com/r/artificial/comments/cluster2',
            'subreddit': 'artificial',
            'author': 'user2',
            'created_utc': base_time + timedelta(minutes=30)
        },
        {
            'id': 'post_3',
            'reddit_id': 'cluster3',
            'title': 'New OpenAI model announcement',
            'content': 'OpenAI announced a new language model',  # Similar content
            'url': 'https://reddit.com/r/singularity/comments/cluster3',
            'subreddit': 'singularity',
            'author': 'user3',
            'created_utc': base_time + timedelta(hours=1)
        }
    ]
    
    # Add all posts
    for post in posts:
        deduplicator.add_post(post)
        print(f"✅ Added: {post['title']}")
    
    # Get clusters
    clusters = deduplicator.get_duplicate_clusters()
    
    print(f"\n📊 Found {len(clusters)} duplicate cluster(s)")
    for i, cluster in enumerate(clusters, 1):
        print(f"   Cluster {i}: {len(cluster)} posts")
        for post_id in cluster:
            print(f"      - {post_id}")
    
    # Get statistics
    stats = deduplicator.get_statistics()
    print(f"\n📈 Deduplication Statistics:")
    print(f"   Total posts: {stats['total_posts']}")
    print(f"   Unique posts: {stats['unique_posts']}")
    print(f"   Duplicate posts: {stats['duplicate_posts']}")
    print(f"   Deduplication ratio: {stats['deduplication_ratio']:.2%}")
    
    return len(clusters) > 0


def test_reddit_scraper_integration():
    """Test integration with Reddit scraper."""
    print("\n🚀 Testing Reddit Scraper Integration")
    print("=" * 50)
    
    try:
        # Create scraper (will fail without credentials, but we can test the structure)
        print("📊 Testing deduplication availability...")
        
        # Test with mock data since we don't have Reddit credentials
        from scrapers.deduplication.reddit_deduplicator import RedditDeduplicator
        
        deduplicator = RedditDeduplicator()
        
        # Test with sample post data
        sample_post = {
            'id': 'test_post',
            'reddit_id': 'sample123',
            'title': 'Test post for integration',
            'content': 'This is a test post for integration testing',
            'url': 'https://reddit.com/r/test/comments/sample123',
            'subreddit': 'test',
            'author': 'testuser',
            'created_utc': datetime.now()
        }
        
        # Test duplicate detection
        duplicates = deduplicator.find_duplicates(sample_post)
        print(f"✅ Duplicate detection working: {len(duplicates)} duplicates found")
        
        # Test statistics
        stats = deduplicator.get_statistics()
        print(f"✅ Statistics available: {stats}")
        
        print("✅ Reddit scraper integration structure is correct")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """Run all duplicate detection tests."""
    print("🔍 Reddit Duplicate Detection Tests")
    print("=" * 60)
    
    tests = [
        ("Exact ID Duplicates", test_exact_id_duplicates),
        ("URL Duplicates", test_url_duplicates),
        ("Content Similarity", test_content_similarity),
        ("Title Similarity", test_title_similarity),
        ("Duplicate Clusters", test_duplicate_clusters),
        ("Reddit Scraper Integration", test_reddit_scraper_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            logger.exception(f"{test_name} error")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 Test Results Summary")
    print("=" * 40)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All duplicate detection tests passed!")
    else:
        print("\n⚠️  Some tests failed.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
