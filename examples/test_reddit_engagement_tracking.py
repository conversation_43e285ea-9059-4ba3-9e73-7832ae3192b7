#!/usr/bin/env python3
"""
Test script for Reddit engagement metrics tracking.

This script tests:
- Engagement metrics calculation
- Viral potential scoring
- Discussion quality analysis
- Subreddit-level engagement tracking
- Top performing posts identification
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from scrapers.sources.reddit_scraper import RedditScraper, RedditScraperConfig, create_reddit_scraper
from scrapers.metrics.reddit_engagement_tracker import RedditEngagementTracker
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_engagement_tracker():
    """Test the standalone engagement tracker."""
    print("📊 Testing Engagement Tracker")
    print("=" * 50)
    
    try:
        # Create Reddit client for testing
        import praw
        reddit = praw.Reddit(
            client_id=settings.reddit.client_id,
            client_secret=settings.reddit.client_secret,
            user_agent=settings.reddit.user_agent
        )
        
        tracker = RedditEngagementTracker(reddit)
        print("✅ Engagement tracker created successfully")
        
        # Test with a popular subreddit
        subreddit = reddit.subreddit('MachineLearning')
        
        # Get a few posts for testing
        posts_tested = 0
        for submission in subreddit.hot(limit=3):
            if posts_tested >= 2:
                break
            
            print(f"\n📄 Testing engagement for: {submission.title[:60]}...")
            
            # Track engagement
            metrics = tracker.track_post_engagement(submission, include_comments=False)
            
            print(f"   📊 Basic metrics:")
            print(f"      Score: {metrics.score}")
            print(f"      Comments: {metrics.num_comments}")
            print(f"      Upvote ratio: {metrics.upvote_ratio:.3f}")
            print(f"      Awards: {metrics.total_awards_received}")
            
            print(f"   🚀 Engagement analysis:")
            print(f"      Engagement score: {metrics.engagement_score:.3f}")
            print(f"      Viral potential: {metrics.viral_potential:.3f}")
            print(f"      Discussion quality: {metrics.discussion_quality:.3f}")
            
            print(f"   ⚡ Velocity metrics:")
            print(f"      Comments/hour: {metrics.comments_per_hour:.2f}")
            print(f"      Score/hour: {metrics.score_per_hour:.2f}")
            
            posts_tested += 1
        
        # Test export functionality
        export_data = tracker.export_metrics()
        print(f"\n📤 Export data contains {len(export_data['tracked_posts'])} posts")
        
        return True
        
    except Exception as e:
        print(f"❌ Engagement tracker test failed: {e}")
        logger.exception("Engagement tracker error")
        return False


def test_reddit_scraper_with_engagement():
    """Test Reddit scraper with integrated engagement tracking."""
    print("\n🚀 Testing Reddit Scraper with Engagement")
    print("=" * 50)
    
    try:
        # Create scraper with engagement tracking
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=3,
            include_comments=True
        )
        
        # Check if engagement tracking is available
        stats = scraper.get_stats()
        engagement_available = stats.get('engagement_tracking_available', False)
        print(f"📊 Engagement tracking available: {engagement_available}")
        
        if not engagement_available:
            print("⚠️  Engagement tracking not available - testing basic functionality")
            return True
        
        # Scrape posts with engagement tracking
        posts_scraped = 0
        for url in scraper.get_article_urls():
            if posts_scraped >= 2:
                break
            
            print(f"\n📄 Scraping with engagement tracking: {url}")
            
            article = scraper.scrape_article(url)
            if article and article.metadata:
                print("✅ Article scraped with engagement data!")
                
                # Check for engagement metrics in metadata
                metadata = article.metadata
                engagement_fields = [
                    'engagement_score', 'viral_potential', 'discussion_quality',
                    'comments_per_hour', 'score_per_hour'
                ]
                
                print("📊 Engagement metrics in metadata:")
                for field in engagement_fields:
                    if field in metadata:
                        print(f"   {field}: {metadata[field]}")
                
                # Check for awards and advanced metrics
                if 'total_awards_received' in metadata:
                    print(f"   🏆 Awards: {metadata['total_awards_received']}")
                if 'award_types' in metadata and metadata['award_types']:
                    print(f"   🎖️  Award types: {metadata['award_types']}")
                
                posts_scraped += 1
            else:
                print("❌ Failed to scrape article")
        
        # Check enhanced statistics
        final_stats = scraper.get_stats()
        print(f"\n📊 Enhanced Statistics:")
        print(f"   Engagement tracked: {final_stats.get('engagement_tracked', 0)}")
        print(f"   Viral posts: {final_stats.get('viral_posts', 0)}")
        print(f"   High engagement posts: {final_stats.get('high_engagement_posts', 0)}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Reddit scraper engagement test failed: {e}")
        logger.exception("Reddit scraper engagement error")
        return False


def test_top_engaging_posts():
    """Test identification of top engaging posts."""
    print("\n⭐ Testing Top Engaging Posts")
    print("=" * 50)
    
    try:
        # Create scraper
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning', 'artificial'],
            max_posts_per_subreddit=5,
            include_comments=False
        )
        
        if not scraper.engagement_tracker:
            print("⚠️  Engagement tracker not available")
            return True
        
        # Scrape multiple posts to build engagement data
        print("🔍 Collecting posts for engagement analysis...")
        
        posts_collected = 0
        for url in scraper.get_article_urls():
            if posts_collected >= 8:
                break
            
            article = scraper.scrape_article(url)
            if article:
                posts_collected += 1
                print(f"   📄 Collected post {posts_collected}: {article.title[:50]}...")
        
        print(f"\n📊 Collected {posts_collected} posts for analysis")
        
        # Get top posts by different metrics
        metrics_to_test = ['engagement_score', 'viral_potential', 'discussion_quality']
        
        for metric in metrics_to_test:
            print(f"\n🏆 Top posts by {metric}:")
            top_posts = scraper.get_top_engaging_posts(limit=3, metric=metric)
            
            for i, post_summary in enumerate(top_posts, 1):
                if post_summary:
                    title = post_summary['title'][:50]
                    score = post_summary['engagement_analysis'][metric]
                    print(f"   {i}. {title}... (Score: {score})")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Top engaging posts test failed: {e}")
        logger.exception("Top engaging posts error")
        return False


def test_subreddit_engagement_analysis():
    """Test subreddit-level engagement analysis."""
    print("\n📈 Testing Subreddit Engagement Analysis")
    print("=" * 50)
    
    try:
        # Create scraper
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=1,
            include_comments=False
        )
        
        if not scraper.engagement_tracker:
            print("⚠️  Engagement tracker not available")
            return True
        
        # Analyze subreddit engagement
        subreddit_name = 'MachineLearning'
        print(f"🔍 Analyzing r/{subreddit_name} engagement...")
        
        engagement_stats = scraper.track_subreddit_engagement(
            subreddit_name, time_filter='day', limit=20
        )
        
        if not engagement_stats:
            print("⚠️  Subreddit engagement analysis not available")
            return True
        
        print("✅ Subreddit engagement analysis completed!")
        print(f"📊 Subreddit Analysis Results:")
        print(f"   Subreddit: r/{engagement_stats['subreddit']}")
        print(f"   Period: {engagement_stats['period']}")
        print(f"   Total posts: {engagement_stats['total_posts']}")
        print(f"   Average score: {engagement_stats['avg_score']}")
        print(f"   Average comments: {engagement_stats['avg_comments']}")
        print(f"   Average upvote ratio: {engagement_stats['avg_upvote_ratio']}")
        
        # Show top performers
        if engagement_stats['top_posts_by_score']:
            print(f"\n🏆 Top posts by score:")
            for i, (title, score) in enumerate(engagement_stats['top_posts_by_score'][:3], 1):
                print(f"   {i}. {title}... ({score} points)")
        
        # Show quality metrics
        print(f"\n📈 Quality metrics:")
        print(f"   Viral posts: {engagement_stats['viral_posts']}")
        print(f"   High quality posts: {engagement_stats['high_quality_posts']}")
        print(f"   Controversial posts: {engagement_stats['controversial_posts']}")
        
        # Show activity patterns
        if engagement_stats['peak_activity_hours']:
            print(f"   Peak activity hours: {engagement_stats['peak_activity_hours']}")
        
        # Show engagement trends
        trends = engagement_stats['engagement_trends']
        print(f"\n📊 Engagement trends:")
        for trend_name, value in trends.items():
            print(f"   {trend_name}: {value:.3f}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Subreddit engagement analysis test failed: {e}")
        logger.exception("Subreddit engagement analysis error")
        return False


def main():
    """Run all Reddit engagement tracking tests."""
    print("🚀 Reddit Engagement Tracking Tests")
    print("=" * 60)
    
    # Check credentials first
    if not settings.reddit.client_id or not settings.reddit.client_secret:
        print("⚠️  Reddit API credentials not configured")
        print("Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET environment variables")
        print("Some tests will be limited or may fail")
        print()
    
    tests = [
        ("Engagement Tracker", test_engagement_tracker),
        ("Reddit Scraper with Engagement", test_reddit_scraper_with_engagement),
        ("Top Engaging Posts", test_top_engaging_posts),
        ("Subreddit Engagement Analysis", test_subreddit_engagement_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 Test Results Summary")
    print("=" * 40)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All Reddit engagement tracking tests passed!")
    else:
        print("\n⚠️  Some tests failed.")
        print("This may be due to missing Reddit API credentials or dependencies.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
