#!/usr/bin/env python3
"""
Direct Reddit integration test that bypasses the settings system.
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime, timezone

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load .env file explicitly
def load_env_file():
    """Load .env file manually."""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ .env file loaded")
    else:
        print("❌ .env file not found")

# Load environment variables
load_env_file()

# Setup logging
logging.basicConfig(level=logging.INFO)

from models.connection import db_manager
from models.database import Article, SourceType
from scrapers.sources.reddit_scraper import RedditScraper, RedditScraperConfig
from processing.sentiment.analyzer import SentimentAnalyzer
from processing.topics.categorizer import TopicCategorizer

def test_reddit_scraping_with_database():
    """Test Reddit scraping and database storage."""
    print("🔄 REDDIT INTEGRATION TEST WITH DATABASE")
    print("=" * 80)
    
    try:
        # Get credentials from environment
        client_id = os.getenv("REDDIT_CLIENT_ID")
        client_secret = os.getenv("REDDIT_CLIENT_SECRET")
        user_agent = os.getenv("REDDIT_USER_AGENT", "AI_News_Scraper/1.0")
        
        if not client_id or not client_secret:
            print("❌ Reddit credentials not found in environment")
            return False
        
        print(f"✅ Reddit credentials loaded:")
        print(f"   Client ID: {client_id[:8]}...")
        print(f"   Client Secret: {client_secret[:8]}...")
        print(f"   User Agent: {user_agent}")
        
        # Create Reddit scraper configuration
        config = RedditScraperConfig(
            name="reddit_integration_test",
            base_url="https://www.reddit.com",
            subreddits=["MachineLearning", "artificial"]  # Limit to 2 subreddits for testing
        )
        
        # Create scraper
        scraper = RedditScraper(config)
        print("✅ Reddit scraper created")
        
        # Test scraping
        print("\n📡 Testing Reddit scraping...")
        articles = list(scraper.scrape_all())
        
        if not articles:
            print("❌ No articles scraped")
            return False
        
        print(f"✅ Scraped {len(articles)} articles:")
        for i, article in enumerate(articles[:3], 1):  # Show first 3
            print(f"   {i}. {article.title[:60]}...")
            print(f"      Source: r/{article.source_id}")
            print(f"      Score: {article.engagement_metrics.get('score', 'N/A')}")
        
        # Test content processing
        print("\n🧠 Testing content processing...")
        
        # Initialize analyzers
        sentiment_analyzer = SentimentAnalyzer()
        sentiment_analyzer.initialize()
        
        topic_categorizer = TopicCategorizer()
        topic_categorizer.initialize()
        
        processed_articles = []
        for article in articles[:2]:  # Process first 2 articles
            # Sentiment analysis
            sentiment_result = sentiment_analyzer.analyze(article.content or article.title)
            article.sentiment_score = sentiment_result.get('score', 0.0)
            article.sentiment_label = sentiment_result.get('label', 'neutral')
            article.sentiment_confidence = sentiment_result.get('confidence', 0.0)
            
            # Topic categorization
            topic_result = topic_categorizer.analyze(article.content or article.title)
            article.topics_analysis = topic_result
            
            # Set analysis timestamp
            article.analysis_timestamp = datetime.now(timezone.utc)
            article.analysis_confidence = 0.8  # Mock confidence
            
            processed_articles.append(article)
        
        print(f"✅ Processed {len(processed_articles)} articles with sentiment and topic analysis")
        
        # Test database storage
        print("\n💾 Testing database storage...")
        
        # Get database session
        with db_manager.get_session() as session:
            # Store articles
            for article in processed_articles:
                session.add(article)
            
            session.commit()
            print(f"✅ Stored {len(processed_articles)} articles in database")
            
            # Verify storage
            stored_count = session.query(Article).filter(
                Article.source_type == SourceType.REDDIT
            ).count()
            
            print(f"✅ Total Reddit articles in database: {stored_count}")
            
            # Show recent articles
            recent_articles = session.query(Article).filter(
                Article.source_type == SourceType.REDDIT,
                Article.analysis_timestamp.isnot(None)
            ).order_by(Article.analysis_timestamp.desc()).limit(3).all()
            
            print(f"\n📄 Recent analyzed Reddit articles:")
            for article in recent_articles:
                print(f"   - {article.title[:50]}...")
                print(f"     Sentiment: {article.sentiment_label} (score: {article.sentiment_score:.2f})")
                print(f"     Topics: {list(article.topics_analysis.keys()) if article.topics_analysis else 'None'}")
                print(f"     Analyzed: {article.analysis_timestamp}")
        
        print("\n🎉 Reddit integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Reddit integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the Reddit integration test."""
    print("🔄 DIRECT REDDIT INTEGRATION TEST")
    print("=" * 80)
    
    success = test_reddit_scraping_with_database()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 Reddit integration test passed!")
        print("\n📋 What was tested:")
        print("   ✅ Reddit API credentials loading")
        print("   ✅ Reddit content scraping")
        print("   ✅ Sentiment analysis processing")
        print("   ✅ Topic categorization processing")
        print("   ✅ Database storage and retrieval")
        print("\n📋 Next steps:")
        print("   1. Run the full integration test with both Reddit and Twitter")
        print("   2. Test the complete pipeline with monitoring")
    else:
        print("⚠️  Reddit integration test failed. Check the output above for details.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
