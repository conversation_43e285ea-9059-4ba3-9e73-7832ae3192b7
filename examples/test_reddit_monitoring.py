#!/usr/bin/env python3
"""
Test script for Reddit monitoring system.

This script tests the Reddit monitoring functionality including:
- Subreddit monitoring setup
- Priority-based scraping
- Real-time monitoring simulation
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from scrapers.monitoring.reddit_monitor import RedditMonitor, SubredditMonitorConfig, create_reddit_monitor
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_monitor_setup():
    """Test Reddit monitor setup and configuration."""
    print("🔧 Testing Reddit Monitor Setup")
    print("=" * 50)
    
    try:
        # Create a test configuration
        test_subreddits = {
            'MachineLearning': 1,
            'artificial': 1,
            'OpenAI': 2,
            'ChatGPT': 2,
            'programming': 3
        }
        
        config = SubredditMonitorConfig(
            subreddits=test_subreddits,
            high_priority_interval=5,  # 5 minutes for testing
            medium_priority_interval=10,
            low_priority_interval=15,
            max_posts_per_high_priority=5,  # Smaller limits for testing
            max_posts_per_medium_priority=3,
            max_posts_per_low_priority=2,
            enable_real_time_processing=False  # Disable for initial test
        )
        
        monitor = RedditMonitor(config)
        
        print("✅ Reddit monitor created successfully")
        print(f"📊 Configured subreddits: {list(test_subreddits.keys())}")
        print(f"🔄 Priority levels: {len(set(test_subreddits.values()))}")
        print(f"⚙️  Processing enabled: {config.enable_real_time_processing}")
        
        # Test configuration access
        stats = monitor.get_monitoring_stats()
        print(f"📈 Initial stats structure: {list(stats.keys())}")
        
        return monitor
        
    except Exception as e:
        print(f"❌ Monitor setup failed: {e}")
        logger.exception("Monitor setup error")
        return None


def test_subreddit_management(monitor: RedditMonitor):
    """Test adding and removing subreddits."""
    print("\n🏷️  Testing Subreddit Management")
    print("=" * 50)
    
    try:
        # Get initial subreddit count
        initial_count = len(monitor.config.subreddits)
        print(f"📊 Initial subreddit count: {initial_count}")
        
        # Add a new subreddit
        monitor.add_subreddit('LocalLLaMA', priority=2)
        new_count = len(monitor.config.subreddits)
        print(f"➕ Added LocalLLaMA, new count: {new_count}")
        
        if new_count == initial_count + 1:
            print("✅ Subreddit addition successful")
        else:
            print("❌ Subreddit addition failed")
            return False
        
        # Remove a subreddit
        monitor.remove_subreddit('programming')
        final_count = len(monitor.config.subreddits)
        print(f"➖ Removed programming, final count: {final_count}")
        
        if final_count == new_count - 1:
            print("✅ Subreddit removal successful")
        else:
            print("❌ Subreddit removal failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Subreddit management test failed: {e}")
        logger.exception("Subreddit management error")
        return False


def test_monitoring_simulation(monitor: RedditMonitor):
    """Test monitoring system with a short simulation."""
    print("\n🔄 Testing Monitoring Simulation")
    print("=" * 50)
    
    try:
        # Check Reddit credentials
        if not settings.reddit.client_id or not settings.reddit.client_secret:
            print("⚠️  Reddit API credentials not configured - using mock simulation")
            return test_mock_monitoring(monitor)
        
        print("🚀 Starting monitoring simulation...")
        
        # Start monitoring
        monitor.start_monitoring()
        
        if monitor.is_running:
            print("✅ Monitoring started successfully")
        else:
            print("❌ Failed to start monitoring")
            return False
        
        # Let it run for a short time
        print("⏱️  Running monitoring for 30 seconds...")
        time.sleep(30)
        
        # Force a check
        print("🔍 Forcing immediate check of all subreddits...")
        monitor.force_check_all()
        
        # Wait a bit more
        time.sleep(10)
        
        # Get statistics
        stats = monitor.get_monitoring_stats()
        print("📊 Monitoring Statistics:")
        print(f"   Posts found: {stats.get('total_posts_found', 0)}")
        print(f"   Posts processed: {stats.get('total_posts_processed', 0)}")
        print(f"   Articles saved: {stats.get('total_articles_saved', 0)}")
        print(f"   Errors: {stats.get('errors', 0)}")
        
        # Stop monitoring
        monitor.stop_monitoring()
        
        if not monitor.is_running:
            print("✅ Monitoring stopped successfully")
        else:
            print("❌ Failed to stop monitoring")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Monitoring simulation failed: {e}")
        logger.exception("Monitoring simulation error")
        
        # Ensure monitoring is stopped
        try:
            monitor.stop_monitoring()
        except:
            pass
        
        return False


def test_mock_monitoring(monitor: RedditMonitor):
    """Test monitoring with mock data when credentials aren't available."""
    print("🎭 Running mock monitoring test...")
    
    try:
        # Test the monitoring structure without actual Reddit API calls
        stats = monitor.get_monitoring_stats()
        
        print("✅ Mock monitoring structure test passed")
        print(f"📊 Available stats: {list(stats.keys())}")
        print(f"🔧 Configuration loaded: {bool(stats.get('config'))}")
        print(f"🤖 Scrapers initialized: {len(stats.get('scraper_stats', {}))}")
        
        # Test priority-based subreddit grouping
        for priority in [1, 2, 3]:
            subreddits = monitor._get_subreddits_by_priority(priority)
            print(f"   Priority {priority}: {subreddits}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock monitoring test failed: {e}")
        return False


def test_priority_system(monitor: RedditMonitor):
    """Test the priority-based monitoring system."""
    print("\n🎯 Testing Priority System")
    print("=" * 50)
    
    try:
        # Test priority grouping
        priorities = {}
        for priority in [1, 2, 3]:
            subreddits = monitor._get_subreddits_by_priority(priority)
            priorities[priority] = subreddits
            print(f"Priority {priority}: {subreddits}")
        
        # Verify all subreddits are assigned
        all_assigned = []
        for subreddits in priorities.values():
            all_assigned.extend(subreddits)
        
        configured_subreddits = list(monitor.config.subreddits.keys())
        
        if set(all_assigned) == set(configured_subreddits):
            print("✅ All subreddits properly assigned to priorities")
        else:
            print("❌ Priority assignment mismatch")
            print(f"   Assigned: {set(all_assigned)}")
            print(f"   Configured: {set(configured_subreddits)}")
            return False
        
        # Test interval checking
        current_time = datetime.now()
        for priority in [1, 2, 3]:
            should_check = monitor._should_check_priority(priority, current_time)
            print(f"Priority {priority} should check: {should_check}")
        
        print("✅ Priority system test passed")
        return True
        
    except Exception as e:
        print(f"❌ Priority system test failed: {e}")
        logger.exception("Priority system error")
        return False


def main():
    """Run all Reddit monitoring tests."""
    print("🚀 Reddit Monitoring System Tests")
    print("=" * 60)
    
    tests = [
        ("Monitor Setup", test_monitor_setup),
        ("Priority System", lambda m: test_priority_system(m) if m else False),
        ("Subreddit Management", lambda m: test_subreddit_management(m) if m else False),
        ("Monitoring Simulation", lambda m: test_monitoring_simulation(m) if m else False)
    ]
    
    results = {}
    monitor = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "Monitor Setup":
                monitor = test_func()
                results[test_name] = monitor is not None
            else:
                results[test_name] = test_func(monitor)
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Cleanup
    if monitor:
        try:
            monitor.stop_monitoring()
        except:
            pass
    
    # Summary
    print("\n🎯 Test Results Summary")
    print("=" * 40)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All Reddit monitoring tests passed!")
    else:
        print("\n⚠️  Some Reddit monitoring tests failed.")
        print("Check your Reddit API credentials and configuration.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
