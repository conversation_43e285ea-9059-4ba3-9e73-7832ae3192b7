#!/usr/bin/env python3
"""
Comprehensive test for Reddit post and comment scraping functionality.

This script tests:
- Reddit post parsing and content extraction
- Comment scraping and formatting
- Different post types (text, link, image)
- Metadata extraction
- Error handling
"""

import sys
import os
import logging
from datetime import datetime
from typing import List, Dict

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from scrapers.sources.reddit_scraper import RedditScraper, RedditScraperConfig, create_reddit_scraper
from scrapers.enhanced.reddit_processing_scraper import create_enhanced_reddit_scraper
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_basic_post_scraping():
    """Test basic Reddit post scraping functionality."""
    print("📄 Testing Basic Post Scraping")
    print("=" * 50)
    
    try:
        # Create a simple Reddit scraper
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=3,
            include_comments=False  # Test posts only first
        )
        
        print("🚀 Getting Reddit posts...")
        
        # Get a few post URLs
        post_urls = []
        for url in scraper.get_article_urls():
            post_urls.append(url)
            if len(post_urls) >= 3:
                break
        
        if not post_urls:
            print("⚠️  No posts found - may be due to rate limiting or credentials")
            return False
        
        print(f"✅ Found {len(post_urls)} posts to test")
        
        # Test scraping each post
        successful_scrapes = 0
        for i, url in enumerate(post_urls, 1):
            print(f"\n📖 Testing post {i}: {url}")
            
            article = scraper.scrape_article(url)
            if article:
                print("✅ Post scraped successfully!")
                print(f"   📰 Title: {article.title[:80]}...")
                print(f"   👤 Author: {article.author}")
                print(f"   📅 Published: {article.published_at}")
                print(f"   📊 Content length: {len(article.content)} chars")
                print(f"   🏷️  Tags: {article.tags}")
                
                # Check metadata
                if hasattr(article, 'metadata') and article.metadata:
                    metadata = article.metadata
                    print(f"   🔢 Score: {metadata.get('score', 'N/A')}")
                    print(f"   💬 Comments: {metadata.get('num_comments', 'N/A')}")
                    print(f"   📍 Subreddit: r/{metadata.get('subreddit', 'N/A')}")
                    print(f"   🔗 External URL: {metadata.get('external_url', 'None')}")
                
                successful_scrapes += 1
            else:
                print("❌ Failed to scrape post")
        
        scraper.shutdown()
        
        success_rate = successful_scrapes / len(post_urls) if post_urls else 0
        print(f"\n📊 Success rate: {successful_scrapes}/{len(post_urls)} ({success_rate:.1%})")
        
        return success_rate > 0.5  # At least 50% success rate
        
    except Exception as e:
        print(f"❌ Basic post scraping test failed: {e}")
        logger.exception("Post scraping error")
        return False


def test_comment_scraping():
    """Test Reddit comment scraping functionality."""
    print("\n💬 Testing Comment Scraping")
    print("=" * 50)
    
    try:
        # Create scraper with comments enabled
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=2,
            include_comments=True
        )
        
        print("🚀 Getting posts with comments...")
        
        # Get one post URL for comment testing
        post_url = None
        for url in scraper.get_article_urls():
            post_url = url
            break
        
        if not post_url:
            print("⚠️  No posts found for comment testing")
            return False
        
        print(f"📄 Testing comments for: {post_url}")
        
        # Scrape with comments
        article = scraper.scrape_article(post_url)
        
        if not article:
            print("❌ Failed to scrape article")
            return False
        
        print("✅ Article scraped successfully!")
        print(f"📰 Title: {article.title[:80]}...")
        print(f"📊 Total content length: {len(article.content)} chars")
        
        # Check if comments were included
        has_comments = "--- Top Comments ---" in article.content
        print(f"💬 Comments included: {has_comments}")
        
        if has_comments:
            # Analyze comment content
            comment_section = article.content.split("--- Top Comments ---")[1]
            comment_lines = [line.strip() for line in comment_section.split('\n') if line.strip()]
            
            print(f"💬 Number of comment lines: {len(comment_lines)}")
            
            # Show first few comments
            for i, comment_line in enumerate(comment_lines[:3]):
                if comment_line:
                    print(f"   Comment {i+1}: {comment_line[:100]}...")
        
        # Check scraper stats
        stats = scraper.get_stats()
        comments_scraped = stats.get('comments_scraped', 0)
        print(f"📊 Comments scraped: {comments_scraped}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Comment scraping test failed: {e}")
        logger.exception("Comment scraping error")
        return False


def test_different_post_types():
    """Test scraping different types of Reddit posts."""
    print("\n🎭 Testing Different Post Types")
    print("=" * 50)
    
    try:
        # Create scraper for multiple subreddits to get variety
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning', 'artificial', 'programming'],
            max_posts_per_subreddit=5,
            include_comments=False
        )
        
        print("🚀 Analyzing different post types...")
        
        post_types = {
            'text_posts': 0,
            'link_posts': 0,
            'unknown_posts': 0
        }
        
        posts_analyzed = 0
        max_posts = 10
        
        for url in scraper.get_article_urls():
            if posts_analyzed >= max_posts:
                break
            
            article = scraper.scrape_article(url)
            if article and hasattr(article, 'metadata') and article.metadata:
                metadata = article.metadata
                external_url = metadata.get('external_url')
                
                if external_url:
                    post_types['link_posts'] += 1
                    print(f"🔗 Link post: {article.title[:50]}... -> {external_url[:50]}...")
                elif article.content and len(article.content) > 100:
                    post_types['text_posts'] += 1
                    print(f"📝 Text post: {article.title[:50]}...")
                else:
                    post_types['unknown_posts'] += 1
                    print(f"❓ Unknown post: {article.title[:50]}...")
                
                posts_analyzed += 1
        
        print(f"\n📊 Post Type Analysis (from {posts_analyzed} posts):")
        for post_type, count in post_types.items():
            percentage = (count / posts_analyzed * 100) if posts_analyzed > 0 else 0
            print(f"   {post_type}: {count} ({percentage:.1f}%)")
        
        scraper.shutdown()
        return posts_analyzed > 0
        
    except Exception as e:
        print(f"❌ Post type analysis failed: {e}")
        logger.exception("Post type analysis error")
        return False


def test_enhanced_scraper_integration():
    """Test the enhanced scraper with content processing."""
    print("\n🚀 Testing Enhanced Scraper Integration")
    print("=" * 50)
    
    try:
        # Create enhanced scraper (may not work without processing dependencies)
        try:
            scraper = create_enhanced_reddit_scraper(
                subreddits=['MachineLearning'],
                max_posts_per_subreddit=2,
                include_comments=True,
                enable_processing=False  # Disable processing for this test
            )
            print("✅ Enhanced scraper created successfully")
        except Exception as e:
            print(f"⚠️  Enhanced scraper creation failed: {e}")
            print("   This is expected if content processing dependencies aren't available")
            return True  # Not a failure, just a limitation
        
        # Test basic functionality
        post_count = 0
        for url in scraper.get_article_urls():
            article = scraper.scrape_article(url)
            if article:
                print(f"📄 Enhanced scraper processed: {article.title[:60]}...")
                post_count += 1
                if post_count >= 2:
                    break
        
        # Get enhanced stats
        stats = scraper.get_stats()
        print(f"📊 Enhanced scraper stats: {list(stats.keys())}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Enhanced scraper test failed: {e}")
        logger.exception("Enhanced scraper error")
        return False


def test_error_handling():
    """Test error handling for invalid URLs and edge cases."""
    print("\n🛡️  Testing Error Handling")
    print("=" * 50)
    
    try:
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=1,
            include_comments=False
        )
        
        # Test invalid URLs
        invalid_urls = [
            "https://reddit.com/invalid/url",
            "https://reddit.com/r/nonexistent/comments/invalid",
            "not_a_url_at_all",
            ""
        ]
        
        print("🧪 Testing invalid URLs...")
        for url in invalid_urls:
            print(f"   Testing: {url}")
            article = scraper.scrape_article(url)
            if article is None:
                print("   ✅ Correctly handled invalid URL")
            else:
                print("   ⚠️  Unexpected success with invalid URL")
        
        # Check error stats
        stats = scraper.get_stats()
        errors = stats.get('errors', 0)
        print(f"📊 Errors recorded: {errors}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        logger.exception("Error handling test error")
        return False


def main():
    """Run all Reddit post and comment scraping tests."""
    print("🚀 Reddit Post & Comment Scraping Tests")
    print("=" * 60)
    
    # Check credentials first
    if not settings.reddit.client_id or not settings.reddit.client_secret:
        print("⚠️  Reddit API credentials not configured")
        print("Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET environment variables")
        print("Some tests will be limited or may fail")
        print()
    
    tests = [
        ("Basic Post Scraping", test_basic_post_scraping),
        ("Comment Scraping", test_comment_scraping),
        ("Different Post Types", test_different_post_types),
        ("Enhanced Scraper Integration", test_enhanced_scraper_integration),
        ("Error Handling", test_error_handling)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 Test Results Summary")
    print("=" * 40)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All Reddit post & comment scraping tests passed!")
    else:
        print("\n⚠️  Some tests failed.")
        print("This may be due to missing Reddit API credentials or network issues.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
