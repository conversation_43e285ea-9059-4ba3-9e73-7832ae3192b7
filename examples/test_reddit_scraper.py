#!/usr/bin/env python3
"""
Test script for Reddit scraper using PRAW.

This script tests the Reddit scraper functionality including:
- Reddit API connection
- Subreddit monitoring
- Post scraping
- Comment collection
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from scrapers.sources.reddit_scraper import RedditScraper, RedditScraperConfig, create_reddit_scraper
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_reddit_connection():
    """Test basic Reddit API connection."""
    print("🔗 Testing Reddit API Connection")
    print("=" * 50)
    
    try:
        # Check if credentials are configured
        if not settings.reddit.client_id or not settings.reddit.client_secret:
            print("❌ Reddit API credentials not configured")
            print("Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET environment variables")
            return False
        
        # Create a simple config for testing
        config = RedditScraperConfig(
            name="Test Reddit Scraper",
            base_url="https://reddit.com",
            subreddits=['MachineLearning'],  # Just one subreddit for testing
            max_posts_per_subreddit=5,
            include_comments=False  # Skip comments for initial test
        )
        
        scraper = RedditScraper(config)
        print("✅ Reddit scraper initialized successfully")
        print(f"📊 Configured subreddits: {config.subreddits}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit connection failed: {e}")
        return False


def test_reddit_scraping():
    """Test Reddit post scraping."""
    print("\n🔍 Testing Reddit Post Scraping")
    print("=" * 50)
    
    try:
        # Create Reddit scraper with minimal settings
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning', 'artificial'],
            max_posts_per_subreddit=3,
            include_comments=False
        )
        
        print("🚀 Starting Reddit scraping test...")
        
        # Get a few article URLs
        urls = []
        url_count = 0
        max_urls = 5
        
        for url in scraper.get_article_urls():
            urls.append(url)
            url_count += 1
            print(f"📄 Found post: {url}")
            
            if url_count >= max_urls:
                break
        
        if not urls:
            print("⚠️  No URLs found - this might be due to rate limiting or no recent posts")
            return False
        
        print(f"\n✅ Found {len(urls)} Reddit posts")
        
        # Test scraping one article
        if urls:
            print(f"\n📖 Testing article scraping for: {urls[0]}")
            article = scraper.scrape_article(urls[0])
            
            if article:
                print("✅ Article scraped successfully!")
                print(f"📰 Title: {article.title[:100]}...")
                print(f"👤 Author: {article.author}")
                print(f"📅 Published: {article.published_at}")
                print(f"🏷️  Tags: {article.tags}")
                print(f"📊 Content length: {len(article.content)} characters")
                
                if hasattr(article, 'metadata') and article.metadata:
                    print(f"🔢 Score: {article.metadata.get('score', 'N/A')}")
                    print(f"💬 Comments: {article.metadata.get('num_comments', 'N/A')}")
                    print(f"📍 Subreddit: r/{article.metadata.get('subreddit', 'N/A')}")
            else:
                print("❌ Failed to scrape article")
                return False
        
        # Show statistics
        stats = scraper.get_stats()
        print(f"\n📊 Scraping Statistics:")
        print(f"   Posts scraped: {stats.get('posts_scraped', 0)}")
        print(f"   Subreddits processed: {stats.get('subreddits_processed', 0)}")
        print(f"   API calls: {stats.get('api_calls', 0)}")
        print(f"   Errors: {stats.get('errors', 0)}")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Reddit scraping test failed: {e}")
        logger.exception("Reddit scraping error")
        return False


def test_reddit_with_comments():
    """Test Reddit scraping with comments."""
    print("\n💬 Testing Reddit Scraping with Comments")
    print("=" * 50)
    
    try:
        # Create scraper with comments enabled
        scraper = create_reddit_scraper(
            subreddits=['MachineLearning'],
            max_posts_per_subreddit=2,
            include_comments=True
        )
        
        # Get one URL and test comment scraping
        for url in scraper.get_article_urls():
            print(f"📄 Testing comments for: {url}")
            
            article = scraper.scrape_article(url)
            if article:
                print("✅ Article with comments scraped successfully!")
                print(f"📰 Title: {article.title[:80]}...")
                print(f"📊 Total content length: {len(article.content)} characters")
                
                # Check if comments were included
                if "--- Top Comments ---" in article.content:
                    print("💬 Comments successfully included in content")
                else:
                    print("ℹ️  No comments found or included")
                
                stats = scraper.get_stats()
                print(f"💬 Comments scraped: {stats.get('comments_scraped', 0)}")
                
                break
            else:
                print("❌ Failed to scrape article with comments")
        
        scraper.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Comment scraping test failed: {e}")
        logger.exception("Comment scraping error")
        return False


def main():
    """Run all Reddit scraper tests."""
    print("🚀 Reddit Scraper Tests")
    print("=" * 60)
    
    tests = [
        ("Reddit Connection", test_reddit_connection),
        ("Reddit Scraping", test_reddit_scraping),
        ("Reddit with Comments", test_reddit_with_comments)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n🎯 Test Results Summary")
    print("=" * 40)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All Reddit scraper tests passed!")
    else:
        print("\n⚠️  Some Reddit scraper tests failed.")
        print("Check your Reddit API credentials and network connection.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
