#!/usr/bin/env python3
"""
Simple Reddit API test with explicit .env loading.
"""

import os
from pathlib import Path

# Load .env file explicitly
def load_env_file():
    """Load .env file manually."""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ .env file loaded manually")
    else:
        print("❌ .env file not found")

def test_reddit_connection():
    """Test Reddit connection with loaded credentials."""
    print("🔍 TESTING REDDIT CONNECTION")
    print("=" * 50)
    
    # Load environment variables
    load_env_file()
    
    # Get credentials
    client_id = os.getenv("REDDIT_CLIENT_ID")
    client_secret = os.getenv("REDDIT_CLIENT_SECRET")
    user_agent = os.getenv("REDDIT_USER_AGENT", "AI_News_Scraper/1.0")
    
    print(f"Client ID: {client_id[:8] if client_id else 'None'}...")
    print(f"Client Secret: {client_secret[:8] if client_secret else 'None'}...")
    print(f"User Agent: {user_agent}")
    
    if not client_id or not client_secret:
        print("❌ Reddit credentials not found")
        return False
    
    try:
        import praw
        
        # Create Reddit instance
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # Test connection
        print("\n🔍 Testing Reddit API access...")
        
        # Try to access a subreddit
        subreddit = reddit.subreddit("MachineLearning")
        print(f"✅ Connected to r/{subreddit.display_name}")
        print(f"   Subscribers: {subreddit.subscribers:,}")
        print(f"   Description: {subreddit.public_description[:100]}...")
        
        # Try to get some posts
        print("\n📄 Testing post retrieval...")
        posts = list(subreddit.hot(limit=3))
        print(f"✅ Retrieved {len(posts)} hot posts:")
        
        for i, post in enumerate(posts, 1):
            print(f"   {i}. {post.title[:60]}...")
            print(f"      Score: {post.score}, Comments: {post.num_comments}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit connection failed: {e}")
        return False

def test_scraping():
    """Test basic scraping functionality."""
    print("\n🕷️ TESTING REDDIT SCRAPING")
    print("=" * 50)
    
    try:
        import praw
        
        # Get credentials
        client_id = os.getenv("REDDIT_CLIENT_ID")
        client_secret = os.getenv("REDDIT_CLIENT_SECRET")
        user_agent = os.getenv("REDDIT_USER_AGENT", "AI_News_Scraper/1.0")
        
        # Create Reddit instance
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # Test scraping from AI-related subreddits
        subreddits = ["artificial", "MachineLearning", "ChatGPT"]
        
        for subreddit_name in subreddits:
            print(f"\n📡 Scraping r/{subreddit_name}...")
            
            subreddit = reddit.subreddit(subreddit_name)
            posts = list(subreddit.hot(limit=2))
            
            print(f"✅ Found {len(posts)} posts:")
            for post in posts:
                print(f"   - {post.title[:50]}...")
                print(f"     Score: {post.score}, Comments: {post.num_comments}")
                print(f"     URL: {post.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit scraping failed: {e}")
        return False

def main():
    """Run Reddit tests."""
    print("🔄 SIMPLE REDDIT API TEST")
    print("=" * 80)
    
    success1 = test_reddit_connection()
    success2 = test_scraping() if success1 else False
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 Reddit API tests passed! Your credentials are working.")
        print("\n📋 Next steps:")
        print("   1. Run the full integration test: python examples/test_full_integration.py")
        print("   2. Start the integrated pipeline")
    else:
        print("⚠️  Reddit API tests failed. Check your credentials.")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
