#!/usr/bin/env python3
"""
Test Twitter API credentials and basic functionality.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_env_file():
    """Load environment variables from .env file."""
    env_path = project_root / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print("✅ .env file loaded")
        return True
    else:
        print("❌ .env file not found")
        return False

def test_twitter_credentials():
    """Test Twitter API credentials."""
    print("\n🔄 TWITTER CREDENTIALS TEST")
    print("=" * 80)
    
    # Load environment
    if not load_env_file():
        return False
    
    # Check credentials
    bearer_token = os.getenv("TWITTER_BEARER_TOKEN")
    api_key = os.getenv("TWITTER_API_KEY")
    api_secret = os.getenv("TWITTER_API_SECRET")
    access_token = os.getenv("TWITTER_ACCESS_TOKEN")
    access_token_secret = os.getenv("TWITTER_ACCESS_TOKEN_SECRET")
    
    print("✅ Twitter credentials loaded:")
    print(f"   Bearer Token: {'✅ Set' if bearer_token else '❌ Missing'}")
    print(f"   API Key: {'✅ Set' if api_key else '❌ Missing'}")
    print(f"   API Secret: {'✅ Set' if api_secret else '❌ Missing'}")
    print(f"   Access Token: {'✅ Set' if access_token else '❌ Missing'}")
    print(f"   Access Token Secret: {'✅ Set' if access_token_secret else '❌ Missing'}")
    
    if not bearer_token:
        print("❌ Twitter Bearer Token is required")
        return False
    
    # Test Twitter API connection
    try:
        import tweepy
        
        # Create client with available credentials
        client = tweepy.Client(
            bearer_token=bearer_token,
            consumer_key=api_key,
            consumer_secret=api_secret,
            access_token=access_token,
            access_token_secret=access_token_secret,
            wait_on_rate_limit=True
        )
        
        # Test connection with a simple search
        print("\n📡 Testing Twitter API connection...")
        tweets = client.search_recent_tweets(
            query="AI OR machine learning",
            max_results=10,
            tweet_fields=['created_at', 'author_id', 'public_metrics']
        )
        
        if tweets.data:
            print(f"✅ Twitter API connection successful! Found {len(tweets.data)} tweets")
            print("\n📄 Sample tweets:")
            for i, tweet in enumerate(tweets.data[:3], 1):
                print(f"   {i}. {tweet.text[:60]}...")
                print(f"      Created: {tweet.created_at}")
                if hasattr(tweet, 'public_metrics'):
                    metrics = tweet.public_metrics
                    print(f"      Likes: {metrics.get('like_count', 0)}, Retweets: {metrics.get('retweet_count', 0)}")
            return True
        else:
            print("⚠️  Twitter API connected but no tweets found")
            return True
            
    except Exception as e:
        print(f"❌ Twitter API connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_twitter_credentials()
    
    if success:
        print("\n🎉 Twitter credentials test passed!")
        print("\n📋 Next steps:")
        print("   1. Run full integration test with both Reddit and Twitter")
        print("   2. Test hashtag monitoring and real-time streaming")
    else:
        print("\n❌ Twitter credentials test failed!")
        print("\n📋 To fix:")
        print("   1. Ensure TWITTER_BEARER_TOKEN is set in .env")
        print("   2. Optionally add OAuth 1.0a credentials for enhanced access")
