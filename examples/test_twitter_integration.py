#!/usr/bin/env python3
"""
Test Twitter integration including scraping, streaming, and monitoring.

This example demonstrates:
1. Basic Twitter scraping
2. Duplicate detection
3. Real-time streaming
4. Comprehensive monitoring
5. Database integration
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Import Twitter components
from scrapers.sources.twitter_scraper import create_twitter_scraper
from scrapers.streaming.twitter_stream import create_twitter_stream
from scrapers.monitoring.twitter_monitor import create_twitter_monitor
from scrapers.deduplication.twitter_deduplicator import TwitterDeduplicator
from models.connection import db_manager
from config.settings import settings


def test_basic_twitter_scraping():
    """Test basic Twitter scraping functionality."""
    print("\n" + "="*60)
    print("TESTING BASIC TWITTER SCRAPING")
    print("="*60)
    
    try:
        # Create Twitter scraper
        scraper = create_twitter_scraper(
            hashtags=['#AI', '#MachineLearning'],
            user_accounts=['OpenAI', 'huggingface'],
            max_tweets_per_hashtag=5
        )
        
        print(f"Created Twitter scraper: {scraper.config.name}")
        print(f"Monitoring hashtags: {scraper.config.hashtags}")
        print(f"Monitoring users: {scraper.config.user_accounts}")
        
        # Test URL generation
        print("\nTesting URL generation...")
        urls = list(scraper.get_article_urls())
        print(f"Found {len(urls)} tweet URLs")
        
        if urls:
            # Test scraping a few tweets
            print("\nTesting tweet scraping...")
            scraped_count = 0
            for url in urls[:3]:  # Test first 3 URLs
                try:
                    article = scraper.scrape_article(url)
                    if article:
                        scraped_count += 1
                        print(f"✅ Scraped: {article.title[:50]}...")
                        print(f"   Author: {article.author}")
                        print(f"   URL: {article.url}")
                        
                        # Show engagement metrics
                        engagement = article.metadata.get('engagement_metrics', {})
                        print(f"   Engagement: {engagement.get('total_engagement', 0)} total")
                        print()
                    else:
                        print(f"❌ Failed to scrape: {url}")
                except Exception as e:
                    print(f"❌ Error scraping {url}: {e}")
            
            print(f"Successfully scraped {scraped_count} out of {min(3, len(urls))} tweets")
        
        # Show scraper statistics
        stats = scraper.get_stats()
        print(f"\nScraper Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Twitter scraping test failed: {e}")
        return False


def test_twitter_duplicate_detection():
    """Test Twitter duplicate detection system."""
    print("\n" + "="*60)
    print("TESTING TWITTER DUPLICATE DETECTION")
    print("="*60)
    
    try:
        deduplicator = TwitterDeduplicator()
        print("Created Twitter deduplicator")
        
        # Create test tweet data
        test_tweets = [
            {
                'id': 'tweet_1',
                'tweet_id': '1234567890',
                'title': 'Exciting news about AI development',
                'content': 'Just announced: new breakthrough in artificial intelligence research!',
                'url': 'https://twitter.com/user1/status/1234567890',
                'author': 'ai_researcher',
                'created_utc': datetime.now()
            },
            {
                'id': 'tweet_2',
                'tweet_id': '1234567891',
                'title': 'AI breakthrough announced',
                'content': 'Just announced: new breakthrough in artificial intelligence research!',
                'url': 'https://twitter.com/user2/status/1234567891',
                'author': 'tech_news',
                'created_utc': datetime.now()
            },
            {
                'id': 'tweet_3',
                'tweet_id': '1234567890',  # Same tweet ID as tweet_1
                'title': 'Retweet of AI news',
                'content': 'RT: Just announced: new breakthrough in artificial intelligence research!',
                'url': 'https://twitter.com/user3/status/1234567890',
                'author': 'ai_enthusiast',
                'created_utc': datetime.now()
            }
        ]
        
        print(f"Testing with {len(test_tweets)} sample tweets")
        
        # Test duplicate detection
        for i, tweet in enumerate(test_tweets):
            print(f"\nProcessing tweet {i+1}: {tweet['title']}")
            
            # Find duplicates
            duplicates = deduplicator.find_duplicates(tweet)
            
            if duplicates:
                print(f"  Found {len(duplicates)} duplicate(s):")
                for dup in duplicates:
                    print(f"    - Match type: {dup.match_type}")
                    print(f"    - Confidence: {dup.confidence:.2f}")
                    print(f"    - Original: {dup.original_post_id}")
                    print(f"    - Duplicate: {dup.duplicate_post_id}")
            else:
                print("  No duplicates found")
        
        # Test clustering
        print("\nTesting temporal clustering...")
        clusters = deduplicator.get_temporal_clusters()
        print(f"Found {len(clusters)} temporal clusters")
        
        for i, cluster in enumerate(clusters):
            print(f"  Cluster {i+1}: {len(cluster)} tweets")
        
        # Get statistics
        stats = deduplicator.get_deduplication_stats()
        print(f"\nDeduplication Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Twitter duplicate detection test failed: {e}")
        return False


def test_twitter_streaming():
    """Test Twitter real-time streaming (brief test)."""
    print("\n" + "="*60)
    print("TESTING TWITTER STREAMING")
    print("="*60)
    
    try:
        # Check if we have proper credentials for streaming
        if not settings.twitter.bearer_token:
            print("⚠️  No Twitter Bearer Token found, skipping streaming test")
            return True
        
        # Create stream manager
        stream_manager = create_twitter_stream(
            track_keywords=['artificial intelligence', 'machine learning'],
            track_hashtags=['#AI', '#ML'],
            enable_processing=True
        )
        
        print("Created Twitter stream manager")
        
        # Set up callback to collect tweets
        collected_tweets = []
        
        def tweet_callback(article):
            collected_tweets.append(article)
            print(f"📥 Received tweet: {article.title[:50]}...")
        
        stream_manager.set_tweet_callback(tweet_callback)
        
        # Start streaming for a short time
        print("Starting Twitter stream for 30 seconds...")
        stream_manager.start_streaming()
        
        # Wait and collect tweets
        time.sleep(30)
        
        # Stop streaming
        stream_manager.stop_streaming()
        
        print(f"Collected {len(collected_tweets)} tweets from stream")
        
        # Show stream statistics
        stats = stream_manager.get_stats()
        print(f"\nStreaming Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Twitter streaming test failed: {e}")
        return False


def test_twitter_monitoring():
    """Test Twitter monitoring system (brief test)."""
    print("\n" + "="*60)
    print("TESTING TWITTER MONITORING")
    print("="*60)
    
    try:
        # Create monitor with limited scope for testing
        monitor = create_twitter_monitor(
            hashtags={'#AI': 1, '#MachineLearning': 2},
            user_accounts={'OpenAI': 1, 'huggingface': 2},
            enable_streaming=False,  # Disable streaming for this test
            save_to_database=False   # Disable database saving for this test
        )
        
        print("Created Twitter monitor")
        
        # Set up callback to collect monitored tweets
        monitored_tweets = []
        
        def monitoring_callback(article):
            monitored_tweets.append(article)
            print(f"📊 Monitored tweet: {article.title[:50]}...")
        
        monitor.add_tweet_callback(monitoring_callback)
        
        # Start monitoring for a short time
        print("Starting Twitter monitoring for 60 seconds...")
        monitor.start_monitoring()
        
        # Wait for some monitoring cycles
        time.sleep(60)
        
        # Stop monitoring
        monitor.stop_monitoring()
        
        print(f"Monitored {len(monitored_tweets)} tweets")
        
        # Show monitoring statistics
        stats = monitor.get_monitoring_stats()
        print(f"\nMonitoring Statistics:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Twitter monitoring test failed: {e}")
        return False


def test_database_integration():
    """Test Twitter database integration."""
    print("\n" + "="*60)
    print("TESTING DATABASE INTEGRATION")
    print("="*60)
    
    try:
        # Test database connection
        with db_manager.get_session() as session:
            print("✅ Database connection successful")
        
        # Create a monitor with database saving enabled
        monitor = create_twitter_monitor(
            hashtags={'#AI': 1},
            user_accounts={'OpenAI': 1},
            enable_streaming=False,
            save_to_database=True
        )
        
        print("Created monitor with database integration")
        
        # Count tweets saved
        tweets_saved = 0
        
        def save_callback(article):
            nonlocal tweets_saved
            tweets_saved += 1
            print(f"💾 Saved tweet to database: {article.title[:50]}...")
        
        monitor.add_tweet_callback(save_callback)
        
        # Run monitoring briefly
        print("Running monitoring with database saving for 30 seconds...")
        monitor.start_monitoring()
        time.sleep(30)
        monitor.stop_monitoring()
        
        print(f"Saved {tweets_saved} tweets to database")
        
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        return False


def main():
    """Run all Twitter integration tests."""
    print("🐦 TWITTER INTEGRATION TEST SUITE")
    print("=" * 80)
    
    # Check Twitter credentials
    if not settings.twitter.bearer_token:
        print("⚠️  WARNING: No Twitter Bearer Token found!")
        print("   Set TWITTER_BEARER_TOKEN environment variable to run full tests")
        print("   Some tests will be skipped or may fail")
        print()
    
    tests = [
        ("Basic Twitter Scraping", test_basic_twitter_scraping),
        ("Duplicate Detection", test_twitter_duplicate_detection),
        ("Real-time Streaming", test_twitter_streaming),
        ("Monitoring System", test_twitter_monitoring),
        ("Database Integration", test_database_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAILED with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Twitter integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
