#!/usr/bin/env python3
"""
Management script for the AI/LLM News Scraper.
"""

import click
import asyncio
from models.connection import db_manager
from utils.logging import get_logger

logger = get_logger(__name__)


@click.group()
def cli():
    """AI/LLM News Scraper management commands."""
    pass


@cli.command()
def init_db():
    """Initialize the database with tables."""
    try:
        logger.info("Creating database tables...")
        db_manager.create_tables()
        logger.info("Database tables created successfully!")
    except Exception as e:
        logger.error("Failed to create database tables", error=str(e))
        raise click.ClickException(f"Database initialization failed: {e}")


@cli.command()
@click.confirmation_option(prompt="Are you sure you want to drop all tables?")
def drop_db():
    """Drop all database tables."""
    try:
        logger.info("Dropping database tables...")
        db_manager.drop_tables()
        logger.info("Database tables dropped successfully!")
    except Exception as e:
        logger.error("Failed to drop database tables", error=str(e))
        raise click.ClickException(f"Database drop failed: {e}")


@cli.command()
def reset_db():
    """Reset the database (drop and recreate tables)."""
    try:
        logger.info("Resetting database...")
        db_manager.drop_tables()
        db_manager.create_tables()
        logger.info("Database reset successfully!")
    except Exception as e:
        logger.error("Failed to reset database", error=str(e))
        raise click.ClickException(f"Database reset failed: {e}")


@cli.command()
def run_api():
    """Run the FastAPI development server."""
    import uvicorn
    from config.settings import settings
    
    logger.info("Starting API server", host=settings.api.host, port=settings.api.port)
    uvicorn.run(
        "api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug
    )


@cli.command()
def test_db():
    """Test database connection."""
    try:
        with db_manager.get_session() as session:
            from sqlalchemy import text
            result = session.execute(text("SELECT 1")).scalar()
            if result == 1:
                logger.info("Database connection successful!")
            else:
                raise Exception("Unexpected result from database")
    except Exception as e:
        logger.error("Database connection failed", error=str(e))
        raise click.ClickException(f"Database test failed: {e}")


if __name__ == "__main__":
    cli()
