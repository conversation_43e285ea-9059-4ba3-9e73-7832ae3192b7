#!/usr/bin/env python3
"""
Database migration to add content analysis fields to the articles table.

This migration adds fields for storing:
- Enhanced sentiment analysis results
- Topic categorization data
- Trend detection results
- Keyword extraction results
- Analysis metadata

Usage:
    python migrations/add_content_analysis_fields.py
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from models.connection import db_manager
from config.settings import settings


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def check_column_exists(session, table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table."""
    try:
        # This query works for both PostgreSQL and SQLite
        result = session.execute(text(f"""
            SELECT COUNT(*) as count
            FROM pragma_table_info('{table_name}')
            WHERE name = '{column_name}'
        """))
        
        count = result.fetchone()[0]
        return count > 0
        
    except Exception:
        # Fallback for PostgreSQL
        try:
            result = session.execute(text(f"""
                SELECT COUNT(*) as count
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' 
                AND column_name = '{column_name}'
            """))
            
            count = result.fetchone()[0]
            return count > 0
            
        except Exception as e:
            logging.warning(f"Could not check if column {column_name} exists: {e}")
            return False


def add_content_analysis_fields():
    """Add content analysis fields to the articles table."""
    logger = logging.getLogger(__name__)
    
    try:
        # Get database session
        session = db_manager.get_session_sync()
        
        # Define the new columns to add
        new_columns = [
            ("sentiment_label", "VARCHAR(50)"),
            ("sentiment_confidence", "FLOAT"),
            ("topics_analysis", "JSON"),
            ("trends_analysis", "JSON"), 
            ("keywords_analysis", "JSON"),
            ("analysis_confidence", "FLOAT"),
            ("analysis_timestamp", "TIMESTAMP")
        ]
        
        logger.info("Starting content analysis fields migration...")
        
        # Check which columns already exist
        existing_columns = []
        for column_name, column_type in new_columns:
            if check_column_exists(session, 'articles', column_name):
                existing_columns.append(column_name)
                logger.info(f"Column {column_name} already exists, skipping...")
        
        # Add missing columns
        columns_added = 0
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    # Handle JSON type for SQLite compatibility
                    if column_type == "JSON":
                        # SQLite doesn't have native JSON type, use TEXT
                        if "sqlite" in settings.database.url.lower():
                            column_type = "TEXT"
                    
                    sql = f"ALTER TABLE articles ADD COLUMN {column_name} {column_type}"
                    session.execute(text(sql))
                    session.commit()
                    
                    logger.info(f"Added column: {column_name} ({column_type})")
                    columns_added += 1
                    
                except Exception as e:
                    logger.error(f"Failed to add column {column_name}: {e}")
                    session.rollback()
                    raise
        
        logger.info(f"Migration completed successfully. Added {columns_added} new columns.")
        
        # Verify the migration
        logger.info("Verifying migration...")
        for column_name, _ in new_columns:
            if check_column_exists(session, 'articles', column_name):
                logger.info(f"✓ Column {column_name} verified")
            else:
                logger.error(f"✗ Column {column_name} not found after migration")
        
        session.close()
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


def rollback_content_analysis_fields():
    """Rollback the content analysis fields migration."""
    logger = logging.getLogger(__name__)
    
    try:
        session = db_manager.get_session_sync()
        
        # Define columns to remove
        columns_to_remove = [
            "sentiment_label",
            "sentiment_confidence", 
            "topics_analysis",
            "trends_analysis",
            "keywords_analysis",
            "analysis_confidence",
            "analysis_timestamp"
        ]
        
        logger.info("Starting rollback of content analysis fields...")
        
        columns_removed = 0
        for column_name in columns_to_remove:
            if check_column_exists(session, 'articles', column_name):
                try:
                    # Note: SQLite doesn't support DROP COLUMN, so this may not work for SQLite
                    sql = f"ALTER TABLE articles DROP COLUMN {column_name}"
                    session.execute(text(sql))
                    session.commit()
                    
                    logger.info(f"Removed column: {column_name}")
                    columns_removed += 1
                    
                except Exception as e:
                    logger.warning(f"Could not remove column {column_name}: {e}")
                    session.rollback()
            else:
                logger.info(f"Column {column_name} does not exist, skipping...")
        
        logger.info(f"Rollback completed. Removed {columns_removed} columns.")
        session.close()
        
    except Exception as e:
        logger.error(f"Rollback failed: {e}")
        raise


if __name__ == "__main__":
    setup_logging()
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        print("🔄 Rolling back content analysis fields migration...")
        rollback_content_analysis_fields()
        print("✅ Rollback completed!")
    else:
        print("🚀 Adding content analysis fields to articles table...")
        add_content_analysis_fields()
        print("✅ Migration completed!")
