"""
Database models for the AI/LLM News Scraper.
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, String, Text, DateTime, Float, Boolean, Integer,
    ForeignKey, Table, Enum, JSON
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.types import TypeDecorator, CHAR
from enum import Enum as PyEnum


Base = declarative_base()


class GUID(TypeDecorator):
    """Platform-independent GUID type.

    Uses PostgreSQL's UUID type when available, otherwise uses CHAR(36).
    """
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID())
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return str(uuid.UUID(value))
            else:
                return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value


class SourceType(PyEnum):
    """Enumeration for different source types."""
    TWITTER = "twitter"
    REDDIT = "reddit"
    NEWS = "news"
    BLOG = "blog"
    PAPER = "paper"
    RSS = "rss"


# Association table for many-to-many relationship between articles and tags
article_tags = Table(
    'article_tags',
    Base.metadata,
    Column('article_id', GUID(), ForeignKey('articles.id'), primary_key=True),
    Column('tag_id', GUID(), ForeignKey('tags.id'), primary_key=True),
    Column('confidence', Float, default=1.0)
)


class Article(Base):
    """Article model representing scraped content."""
    
    __tablename__ = 'articles'
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    title = Column(Text, nullable=False)
    content = Column(Text)
    url = Column(Text, unique=True, nullable=False)
    source_type = Column(Enum(SourceType), nullable=False)
    source_id = Column(String(255))
    author = Column(String(255))
    published_at = Column(DateTime)
    scraped_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    sentiment_score = Column(Float)
    sentiment_label = Column(String(50))  # positive, negative, neutral
    sentiment_confidence = Column(Float)

    # Content analysis results stored as JSON
    topics_analysis = Column(JSON)  # List of topic categorization results
    trends_analysis = Column(JSON)  # List of trend detection results
    keywords_analysis = Column(JSON)  # List of keyword extraction results
    analysis_confidence = Column(Float)  # Overall analysis confidence
    analysis_timestamp = Column(DateTime)  # When analysis was performed

    engagement_metrics = Column(JSON)
    
    # Foreign key to source
    source_uuid = Column(GUID(), ForeignKey('sources.id'))
    
    # Relationships
    source = relationship("Source", back_populates="articles")
    tags = relationship("Tag", secondary=article_tags, back_populates="articles")
    
    def __repr__(self):
        return f"<Article(id={self.id}, title='{self.title[:50]}...', source_type={self.source_type})>"


class Source(Base):
    """Source model representing data sources."""
    
    __tablename__ = 'sources'
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    type = Column(String(100), nullable=False)
    url = Column(Text)
    api_config = Column(JSON)
    is_active = Column(Boolean, default=True)
    last_scraped = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    articles = relationship("Article", back_populates="source")
    
    def __repr__(self):
        return f"<Source(id={self.id}, name='{self.name}', type='{self.type}')>"


class Tag(Base):
    """Tag model for content classification."""
    
    __tablename__ = 'tags'
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), unique=True, nullable=False)
    category = Column(String(100))
    confidence_score = Column(Float, default=1.0)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    articles = relationship("Article", secondary=article_tags, back_populates="tags")
    
    def __repr__(self):
        return f"<Tag(id={self.id}, name='{self.name}', category='{self.category}')>"


class ScrapingJob(Base):
    """Model to track scraping jobs and their status."""
    
    __tablename__ = 'scraping_jobs'
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    source_id = Column(GUID(), ForeignKey('sources.id'), nullable=False)
    status = Column(String(50), default='pending')  # pending, running, completed, failed
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    articles_scraped = Column(Integer, default=0)
    errors = Column(JSON)
    job_metadata = Column(JSON)
    
    # Relationships
    source = relationship("Source")
    
    def __repr__(self):
        return f"<ScrapingJob(id={self.id}, source_id={self.source_id}, status='{self.status}')>"
