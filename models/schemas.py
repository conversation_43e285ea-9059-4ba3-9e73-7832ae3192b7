"""
Pydantic schemas for API validation and serialization.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, HttpUrl

from models.database import SourceType


class TagBase(BaseModel):
    """Base schema for tags."""
    name: str = Field(..., min_length=1, max_length=255)
    category: Optional[str] = Field(None, max_length=100)
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0)


class TagCreate(TagBase):
    """Schema for creating tags."""
    pass


class TagResponse(TagBase):
    """Schema for tag responses."""
    id: UUID
    created_at: datetime
    
    class Config:
        from_attributes = True


class SourceBase(BaseModel):
    """Base schema for sources."""
    name: str = Field(..., min_length=1, max_length=255)
    type: str = Field(..., min_length=1, max_length=100)
    url: Optional[HttpUrl] = None
    api_config: Optional[Dict[str, Any]] = None
    is_active: bool = True


class SourceCreate(SourceBase):
    """Schema for creating sources."""
    pass


class SourceUpdate(BaseModel):
    """Schema for updating sources."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    type: Optional[str] = Field(None, min_length=1, max_length=100)
    url: Optional[HttpUrl] = None
    api_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class SourceResponse(SourceBase):
    """Schema for source responses."""
    id: UUID
    last_scraped: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ArticleBase(BaseModel):
    """Base schema for articles."""
    title: str = Field(..., min_length=1)
    content: Optional[str] = None
    url: HttpUrl
    source_type: SourceType
    source_id: Optional[str] = Field(None, max_length=255)
    author: Optional[str] = Field(None, max_length=255)
    published_at: Optional[datetime] = None

    # Sentiment analysis fields
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    sentiment_label: Optional[str] = Field(None, max_length=50)
    sentiment_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)

    # Content analysis results
    topics_analysis: Optional[List[Dict[str, Any]]] = None
    trends_analysis: Optional[List[Dict[str, Any]]] = None
    keywords_analysis: Optional[List[Dict[str, Any]]] = None
    analysis_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    analysis_timestamp: Optional[datetime] = None

    engagement_metrics: Optional[Dict[str, Any]] = None


class ArticleCreate(ArticleBase):
    """Schema for creating articles."""
    source_uuid: Optional[UUID] = None
    tag_ids: Optional[List[UUID]] = []


class ArticleUpdate(BaseModel):
    """Schema for updating articles."""
    title: Optional[str] = Field(None, min_length=1)
    content: Optional[str] = None
    author: Optional[str] = Field(None, max_length=255)

    # Sentiment analysis fields
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    sentiment_label: Optional[str] = Field(None, max_length=50)
    sentiment_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)

    # Content analysis results
    topics_analysis: Optional[List[Dict[str, Any]]] = None
    trends_analysis: Optional[List[Dict[str, Any]]] = None
    keywords_analysis: Optional[List[Dict[str, Any]]] = None
    analysis_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    analysis_timestamp: Optional[datetime] = None

    engagement_metrics: Optional[Dict[str, Any]] = None


class ArticleResponse(ArticleBase):
    """Schema for article responses."""
    id: UUID
    scraped_at: datetime
    updated_at: datetime
    source: Optional[SourceResponse] = None
    tags: List[TagResponse] = []

    class Config:
        from_attributes = True


# Additional schemas for content analysis
class SentimentAnalysisResponse(BaseModel):
    """Schema for sentiment analysis results."""
    score: float = Field(..., ge=-1.0, le=1.0)
    label: str
    confidence: float = Field(..., ge=0.0, le=1.0)


class TopicAnalysisResponse(BaseModel):
    """Schema for topic analysis results."""
    category: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    subcategory: Optional[str] = None


class TrendAnalysisResponse(BaseModel):
    """Schema for trend analysis results."""
    trend_type: str
    description: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    timeframe: Optional[str] = None


class ContentAnalysisResponse(BaseModel):
    """Schema for complete content analysis results."""
    sentiment: Optional[SentimentAnalysisResponse] = None
    topics: List[TopicAnalysisResponse] = []
    trends: List[TrendAnalysisResponse] = []
    keywords: List[Dict[str, Any]] = []
    overall_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    analysis_timestamp: Optional[datetime] = None


class ArticleSearchParams(BaseModel):
    """Schema for article search parameters."""
    query: Optional[str] = None
    source_type: Optional[SourceType] = None
    author: Optional[str] = None
    tags: Optional[List[str]] = []
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    sentiment_min: Optional[float] = Field(None, ge=-1.0, le=1.0)
    sentiment_max: Optional[float] = Field(None, ge=-1.0, le=1.0)
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)


class ScrapingJobBase(BaseModel):
    """Base schema for scraping jobs."""
    source_id: UUID
    status: str = "pending"
    articles_scraped: int = 0
    errors: Optional[Dict[str, Any]] = None
    job_metadata: Optional[Dict[str, Any]] = None


class ScrapingJobCreate(ScrapingJobBase):
    """Schema for creating scraping jobs."""
    pass


class ScrapingJobResponse(ScrapingJobBase):
    """Schema for scraping job responses."""
    id: UUID
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    source: SourceResponse
    
    class Config:
        from_attributes = True


class PaginatedResponse(BaseModel):
    """Schema for paginated responses."""
    items: List[Any]
    total: int
    limit: int
    offset: int
    has_next: bool
    has_prev: bool
