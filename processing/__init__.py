"""
Content processing and analysis framework for AI/LLM news articles.

This package provides comprehensive content analysis capabilities including:
- Sentiment analysis
- Topic modeling and categorization
- Trend detection
- Content summarization
- Entity extraction
- Keyword analysis
"""

from .base.analyzer import BaseAnalyzer, AnalysisResult, ContentAnalysis, AnalyzerPipeline, AnalysisType
from .sentiment.analyzer import SentimentAnalyzer
from .topics.categorizer import TopicCategorizer
from .trends.detector import TrendDetector
from .keywords.extractor import KeywordExtractor
from .processor import ContentProcessor, ProcessingConfig, ProcessingStats

__version__ = "0.1.0"
__all__ = [
    # Base framework
    "BaseAnalyzer",
    "AnalysisResult",
    "ContentAnalysis",
    "AnalyzerPipeline",
    "AnalysisType",

    # Analysis components
    "SentimentAnalyzer",
    "TopicCategorizer",
    "TrendDetector",
    "KeywordExtractor",

    # Main processor
    "ContentProcessor",
    "ProcessingConfig",
    "ProcessingStats"
]
