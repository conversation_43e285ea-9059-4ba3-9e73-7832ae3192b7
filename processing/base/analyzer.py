"""
Base analyzer class and result structures for content processing.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum

from scrapers.base.content_parser import ParsedArticle


class AnalysisType(Enum):
    """Types of content analysis."""
    SENTIMENT = "sentiment"
    TOPIC = "topic"
    TREND = "trend"
    KEYWORD = "keyword"
    ENTITY = "entity"
    SUMMARY = "summary"


class ConfidenceLevel(Enum):
    """Confidence levels for analysis results."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class AnalysisResult:
    """Result of content analysis."""
    analysis_type: AnalysisType
    confidence: float  # 0.0 to 1.0
    confidence_level: ConfidenceLevel
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    processing_time_ms: Optional[float] = None
    
    def __post_init__(self):
        """Set confidence level based on confidence score."""
        if self.confidence >= 0.9:
            self.confidence_level = ConfidenceLevel.VERY_HIGH
        elif self.confidence >= 0.7:
            self.confidence_level = ConfidenceLevel.HIGH
        elif self.confidence >= 0.5:
            self.confidence_level = ConfidenceLevel.MEDIUM
        else:
            self.confidence_level = ConfidenceLevel.LOW
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'analysis_type': self.analysis_type.value,
            'confidence': self.confidence,
            'confidence_level': self.confidence_level.value,
            'data': self.data,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat(),
            'processing_time_ms': self.processing_time_ms
        }


@dataclass
class ContentAnalysis:
    """Complete analysis results for an article."""
    article_url: str
    article_title: str
    analysis_results: List[AnalysisResult] = field(default_factory=list)
    overall_confidence: float = 0.0
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def add_result(self, result: AnalysisResult):
        """Add an analysis result."""
        self.analysis_results.append(result)
        self._update_overall_confidence()
    
    def get_result(self, analysis_type: AnalysisType) -> Optional[AnalysisResult]:
        """Get result by analysis type."""
        for result in self.analysis_results:
            if result.analysis_type == analysis_type:
                return result
        return None
    
    def get_results_by_type(self, analysis_type: AnalysisType) -> List[AnalysisResult]:
        """Get all results of a specific type."""
        return [r for r in self.analysis_results if r.analysis_type == analysis_type]
    
    def _update_overall_confidence(self):
        """Update overall confidence based on all results."""
        if not self.analysis_results:
            self.overall_confidence = 0.0
            return
        
        # Weighted average based on confidence levels
        total_weight = 0
        weighted_sum = 0
        
        for result in self.analysis_results:
            weight = self._get_confidence_weight(result.confidence_level)
            weighted_sum += result.confidence * weight
            total_weight += weight
        
        self.overall_confidence = weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _get_confidence_weight(self, level: ConfidenceLevel) -> float:
        """Get weight for confidence level."""
        weights = {
            ConfidenceLevel.VERY_HIGH: 1.0,
            ConfidenceLevel.HIGH: 0.8,
            ConfidenceLevel.MEDIUM: 0.6,
            ConfidenceLevel.LOW: 0.4
        }
        return weights.get(level, 0.5)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'article_url': self.article_url,
            'article_title': self.article_title,
            'analysis_results': [r.to_dict() for r in self.analysis_results],
            'overall_confidence': self.overall_confidence,
            'created_at': self.created_at.isoformat()
        }


class BaseAnalyzer(ABC):
    """Base class for all content analyzers."""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.is_initialized = False
        self._stats = {
            'analyses_performed': 0,
            'total_processing_time_ms': 0.0,
            'average_processing_time_ms': 0.0,
            'errors_encountered': 0
        }
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the analyzer (load models, etc.)."""
        pass
    
    @abstractmethod
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Analyze an article and return results."""
        pass
    
    @abstractmethod
    def get_analysis_type(self) -> AnalysisType:
        """Get the type of analysis this analyzer performs."""
        pass
    
    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if this analyzer can process the given article."""
        if not article or not article.content:
            return False
        
        # Basic content length check
        if len(article.content.strip()) < 50:
            return False
        
        return True
    
    def analyze_safe(self, article: ParsedArticle) -> Optional[AnalysisResult]:
        """Safely analyze an article with error handling."""
        if not self.is_initialized:
            if not self.initialize():
                return None
        
        if not self.can_analyze(article):
            return None
        
        try:
            start_time = datetime.utcnow()
            result = self.analyze(article)
            end_time = datetime.utcnow()
            
            # Calculate processing time
            processing_time_ms = (end_time - start_time).total_seconds() * 1000
            result.processing_time_ms = processing_time_ms
            
            # Update statistics
            self._update_stats(processing_time_ms, success=True)
            
            return result
            
        except Exception as e:
            self._update_stats(0, success=False)
            # Log error but don't raise
            print(f"Error in {self.name} analyzer: {e}")
            return None
    
    def _update_stats(self, processing_time_ms: float, success: bool):
        """Update analyzer statistics."""
        if success:
            self._stats['analyses_performed'] += 1
            self._stats['total_processing_time_ms'] += processing_time_ms
            
            if self._stats['analyses_performed'] > 0:
                self._stats['average_processing_time_ms'] = (
                    self._stats['total_processing_time_ms'] / 
                    self._stats['analyses_performed']
                )
        else:
            self._stats['errors_encountered'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get analyzer statistics."""
        return {
            'name': self.name,
            'version': self.version,
            'is_initialized': self.is_initialized,
            **self._stats
        }
    
    def reset_stats(self):
        """Reset analyzer statistics."""
        self._stats = {
            'analyses_performed': 0,
            'total_processing_time_ms': 0.0,
            'average_processing_time_ms': 0.0,
            'errors_encountered': 0
        }


class AnalyzerPipeline:
    """Pipeline for running multiple analyzers on content."""
    
    def __init__(self, analyzers: List[BaseAnalyzer]):
        self.analyzers = analyzers
        self.parallel_execution = False  # Can be enabled for performance
    
    def add_analyzer(self, analyzer: BaseAnalyzer):
        """Add an analyzer to the pipeline."""
        self.analyzers.append(analyzer)
    
    def remove_analyzer(self, analyzer_name: str):
        """Remove an analyzer by name."""
        self.analyzers = [a for a in self.analyzers if a.name != analyzer_name]
    
    def analyze(self, article: ParsedArticle) -> ContentAnalysis:
        """Run all analyzers on an article."""
        analysis = ContentAnalysis(
            article_url=article.url,
            article_title=article.title or "Unknown"
        )
        
        for analyzer in self.analyzers:
            result = analyzer.analyze_safe(article)
            if result:
                analysis.add_result(result)
        
        return analysis
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get statistics for all analyzers in the pipeline."""
        return {
            'total_analyzers': len(self.analyzers),
            'analyzer_stats': [a.get_stats() for a in self.analyzers]
        }
