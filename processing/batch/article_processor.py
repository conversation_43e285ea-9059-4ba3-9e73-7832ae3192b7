"""
Batch processing system for existing articles in the database.

This module provides functionality to:
1. Process existing articles that lack content analysis
2. Update articles with sentiment analysis, topic categorization, and trend detection
3. Handle batch processing with progress tracking and error handling
4. Support selective processing based on criteria
"""

import logging
import time
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session

from models.connection import db_manager
from models.database import Article, SourceType
from scrapers.base.content_parser import ParsedArticle
from processing.base.analyzer import AnalyzerPipeline, AnalysisType
from processing.sentiment.analyzer import SentimentAnalyzer
from processing.topics.categorizer import TopicCategorizer
from processing.trends.detector import TrendDetector


@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing of articles."""
    
    # Processing criteria
    process_unanalyzed_only: bool = True  # Only process articles without analysis
    max_articles_per_batch: int = 100
    max_total_articles: Optional[int] = None  # None = no limit
    
    # Time-based filtering
    min_article_age_hours: int = 0  # Minimum age before processing
    max_article_age_days: Optional[int] = None  # Maximum age to process
    
    # Source filtering
    source_types: List[SourceType] = field(default_factory=lambda: [SourceType.REDDIT, SourceType.TWITTER])
    
    # Content filtering
    min_content_length: int = 50
    require_content: bool = True  # Skip articles without content
    
    # Processing settings
    enable_sentiment_analysis: bool = True
    enable_topic_categorization: bool = True
    enable_trend_detection: bool = True
    
    # Performance settings
    max_workers: int = 4
    processing_timeout_seconds: int = 30
    batch_commit_size: int = 50
    
    # Error handling
    max_retries: int = 3
    skip_on_error: bool = True  # Continue processing other articles on error
    
    # Progress tracking
    enable_progress_logging: bool = True
    progress_log_interval: int = 10  # Log progress every N articles


class BatchArticleProcessor:
    """Batch processor for existing articles in the database."""
    
    def __init__(self, config: BatchProcessingConfig):
        self.config = config
        self.logger = logging.getLogger("batch.processor")
        
        # Initialize content processing pipeline
        self.content_processor = AnalyzerPipeline([])
        self._initialize_analyzers()
        
        # Thread pool for concurrent processing
        self.executor = ThreadPoolExecutor(max_workers=config.max_workers)
        
        # Processing state
        self.is_processing = False
        self.stop_requested = False
        
        # Statistics
        self.stats = {
            'processing_start_time': None,
            'articles_found': 0,
            'articles_processed': 0,
            'articles_updated': 0,
            'articles_skipped': 0,
            'processing_errors': 0,
            'database_errors': 0,
            'total_processing_time_ms': 0,
            'average_processing_time_ms': 0,
            'batches_processed': 0
        }
        
        # Callbacks
        self.progress_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        self.article_callbacks: List[Callable[[Article, Any], None]] = []
    
    def add_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for progress updates."""
        self.progress_callbacks.append(callback)
    
    def add_article_callback(self, callback: Callable[[Article, Any], None]):
        """Add callback for processed articles."""
        self.article_callbacks.append(callback)
    
    def _initialize_analyzers(self):
        """Initialize content analysis components."""
        try:
            if self.config.enable_sentiment_analysis:
                sentiment_analyzer = SentimentAnalyzer()
                sentiment_analyzer.initialize()
                self.content_processor.add_analyzer(sentiment_analyzer)
                self.logger.info("Sentiment analyzer initialized")
            
            if self.config.enable_topic_categorization:
                topic_categorizer = TopicCategorizer()
                topic_categorizer.initialize()
                self.content_processor.add_analyzer(topic_categorizer)
                self.logger.info("Topic categorizer initialized")
            
            if self.config.enable_trend_detection:
                trend_detector = TrendDetector()
                trend_detector.initialize()
                self.content_processor.add_analyzer(trend_detector)
                self.logger.info("Trend detector initialized")
            
            self.logger.info("Content processing pipeline initialized for batch processing")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize analyzers: {e}")
            raise
    
    def process_articles(self) -> Dict[str, Any]:
        """Process articles in batches."""
        self.logger.info("Starting batch processing of articles...")
        self.is_processing = True
        self.stop_requested = False
        self.stats['processing_start_time'] = time.time()
        
        try:
            # Get articles to process
            articles_to_process = self._get_articles_to_process()
            self.stats['articles_found'] = len(articles_to_process)
            
            if not articles_to_process:
                self.logger.info("No articles found matching processing criteria")
                return self.get_processing_stats()
            
            self.logger.info(f"Found {len(articles_to_process)} articles to process")
            
            # Process in batches
            self._process_articles_in_batches(articles_to_process)
            
            # Update final statistics
            self._update_final_stats()
            
            self.logger.info("Batch processing completed successfully")
            return self.get_processing_stats()
            
        except Exception as e:
            self.logger.error(f"Batch processing failed: {e}")
            raise
        finally:
            self.is_processing = False
    
    def stop_processing(self):
        """Request to stop processing."""
        self.logger.info("Stop requested for batch processing")
        self.stop_requested = True
    
    def _get_articles_to_process(self) -> List[Article]:
        """Get articles that need processing based on configuration."""
        with db_manager.get_session() as session:
            query = session.query(Article)
            
            # Filter by source types
            if self.config.source_types:
                query = query.filter(Article.source_type.in_(self.config.source_types))
            
            # Filter by content requirements
            if self.config.require_content:
                query = query.filter(Article.content.isnot(None))
                query = query.filter(func.length(Article.content) >= self.config.min_content_length)
            
            # Filter by analysis status
            if self.config.process_unanalyzed_only:
                query = query.filter(
                    or_(
                        Article.analysis_timestamp.is_(None),
                        Article.sentiment_score.is_(None),
                        Article.topics_analysis.is_(None),
                        Article.trends_analysis.is_(None)
                    )
                )
            
            # Filter by article age
            if self.config.min_article_age_hours > 0:
                min_time = datetime.now(timezone.utc) - timedelta(hours=self.config.min_article_age_hours)
                query = query.filter(Article.scraped_at <= min_time)
            
            if self.config.max_article_age_days:
                max_time = datetime.now(timezone.utc) - timedelta(days=self.config.max_article_age_days)
                query = query.filter(Article.scraped_at >= max_time)
            
            # Order by scraped_at (oldest first)
            query = query.order_by(Article.scraped_at.asc())
            
            # Apply limits
            if self.config.max_total_articles:
                query = query.limit(self.config.max_total_articles)
            
            return query.all()
    
    def _process_articles_in_batches(self, articles: List[Article]):
        """Process articles in batches."""
        total_articles = len(articles)
        
        for i in range(0, total_articles, self.config.max_articles_per_batch):
            if self.stop_requested:
                self.logger.info("Processing stopped by request")
                break
            
            batch = articles[i:i + self.config.max_articles_per_batch]
            batch_number = (i // self.config.max_articles_per_batch) + 1
            
            self.logger.info(f"Processing batch {batch_number} ({len(batch)} articles)")
            
            try:
                self._process_batch(batch)
                self.stats['batches_processed'] += 1
                
                # Log progress
                if self.config.enable_progress_logging:
                    progress = {
                        'batch_number': batch_number,
                        'articles_processed': self.stats['articles_processed'],
                        'total_articles': total_articles,
                        'progress_percentage': (self.stats['articles_processed'] / total_articles) * 100
                    }
                    self._notify_progress(progress)
                
            except Exception as e:
                self.logger.error(f"Error processing batch {batch_number}: {e}")
                if not self.config.skip_on_error:
                    raise
    
    def _process_batch(self, articles: List[Article]):
        """Process a single batch of articles."""
        # Convert to ParsedArticle objects for processing
        parsed_articles = []
        article_mapping = {}
        
        for article in articles:
            try:
                parsed_article = self._convert_to_parsed_article(article)
                parsed_articles.append(parsed_article)
                article_mapping[parsed_article.url] = article
            except Exception as e:
                self.logger.error(f"Error converting article {article.id}: {e}")
                self.stats['articles_skipped'] += 1
        
        # Process articles concurrently
        futures = []
        for parsed_article in parsed_articles:
            future = self.executor.submit(self._process_single_article, parsed_article)
            futures.append((future, parsed_article))
        
        # Collect results and update database
        processed_results = []
        
        for future, parsed_article in futures:
            try:
                analysis = future.result(timeout=self.config.processing_timeout_seconds)
                original_article = article_mapping[parsed_article.url]
                processed_results.append((original_article, analysis))
                self.stats['articles_processed'] += 1
                
                # Call article callbacks
                for callback in self.article_callbacks:
                    try:
                        callback(original_article, analysis)
                    except Exception as e:
                        self.logger.error(f"Error in article callback: {e}")
                
            except Exception as e:
                self.logger.error(f"Error processing article {parsed_article.url}: {e}")
                self.stats['processing_errors'] += 1
                if not self.config.skip_on_error:
                    raise
        
        # Update database in batches
        if processed_results:
            self._update_articles_in_database(processed_results)
    
    def _convert_to_parsed_article(self, article: Article) -> ParsedArticle:
        """Convert database Article to ParsedArticle for processing."""
        return ParsedArticle(
            title=article.title,
            content=article.content or "",
            url=article.url,
            author=article.author,
            published_at=article.published_at,
            source_name=article.source_type.value if article.source_type else "unknown"
        )
    
    def _process_single_article(self, article: ParsedArticle):
        """Process a single article with content analysis."""
        start_time = time.time()
        
        try:
            analysis = self.content_processor.analyze(article)
            
            # Update processing time statistics
            processing_time = (time.time() - start_time) * 1000  # Convert to ms
            self.stats['total_processing_time_ms'] += processing_time
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing article {article.url}: {e}")
            raise
    
    def _update_articles_in_database(self, processed_results: List[tuple]):
        """Update articles in database with analysis results."""
        try:
            with db_manager.get_session() as session:
                updated_count = 0
                
                for article, analysis in processed_results:
                    try:
                        # Refresh the article from database
                        db_article = session.query(Article).filter_by(id=article.id).first()
                        if not db_article:
                            self.logger.warning(f"Article {article.id} not found in database")
                            continue
                        
                        # Update with analysis results
                        self._update_article_with_analysis(db_article, analysis)
                        updated_count += 1
                        
                        # Commit in batches
                        if updated_count % self.config.batch_commit_size == 0:
                            session.commit()
                            self.logger.debug(f"Committed batch of {self.config.batch_commit_size} articles")
                        
                    except Exception as e:
                        self.logger.error(f"Error updating article {article.id}: {e}")
                        self.stats['database_errors'] += 1
                        if not self.config.skip_on_error:
                            raise
                
                # Final commit
                session.commit()
                self.stats['articles_updated'] += updated_count
                self.logger.info(f"Updated {updated_count} articles in database")
                
        except Exception as e:
            self.logger.error(f"Database update failed: {e}")
            self.stats['database_errors'] += 1
            raise
    
    def _update_article_with_analysis(self, article: Article, analysis):
        """Update article with content analysis results."""
        try:
            # Add sentiment analysis
            sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
            if sentiment_result:
                article.sentiment_score = sentiment_result.data.get('score', 0)
                article.sentiment_label = sentiment_result.data.get('label', 'neutral')
                article.sentiment_confidence = sentiment_result.confidence
            
            # Add topic categorization
            topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
            if topic_results:
                article.topics_analysis = [
                    {
                        'category': result.data.get('category', 'unknown'),
                        'confidence': result.confidence,
                        'subcategory': result.data.get('subcategory', '')
                    }
                    for result in topic_results
                ]
            
            # Add trend detection
            trend_results = analysis.get_results_by_type(AnalysisType.TREND)
            if trend_results:
                article.trends_analysis = [
                    {
                        'trend_type': result.data.get('trend_type', 'unknown'),
                        'description': result.data.get('description', ''),
                        'confidence': result.confidence,
                        'timeframe': result.data.get('timeframe', '')
                    }
                    for result in trend_results
                ]
            
            # Set overall analysis metadata
            article.analysis_confidence = analysis.get_overall_confidence()
            article.analysis_timestamp = datetime.now(timezone.utc)
            
        except Exception as e:
            self.logger.error(f"Error updating article with analysis: {e}")
            raise
    
    def _notify_progress(self, progress: Dict[str, Any]):
        """Notify progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(progress)
            except Exception as e:
                self.logger.error(f"Error in progress callback: {e}")
    
    def _update_final_stats(self):
        """Update final processing statistics."""
        if self.stats['articles_processed'] > 0:
            self.stats['average_processing_time_ms'] = (
                self.stats['total_processing_time_ms'] / self.stats['articles_processed']
            )
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics."""
        elapsed_time = 0
        if self.stats['processing_start_time']:
            elapsed_time = time.time() - self.stats['processing_start_time']
        
        return {
            **self.stats,
            'elapsed_time_seconds': elapsed_time,
            'articles_per_second': (
                self.stats['articles_processed'] / max(elapsed_time, 1)
            ),
            'is_processing': self.is_processing,
            'stop_requested': self.stop_requested,
            'config': {
                'process_unanalyzed_only': self.config.process_unanalyzed_only,
                'max_articles_per_batch': self.config.max_articles_per_batch,
                'max_total_articles': self.config.max_total_articles,
                'source_types': [st.value for st in self.config.source_types],
                'enable_sentiment_analysis': self.config.enable_sentiment_analysis,
                'enable_topic_categorization': self.config.enable_topic_categorization,
                'enable_trend_detection': self.config.enable_trend_detection
            }
        }
    
    def shutdown(self):
        """Shutdown the processor and cleanup resources."""
        self.logger.info("Shutting down batch processor...")
        self.stop_requested = True
        self.executor.shutdown(wait=True)
        self.logger.info("Batch processor shutdown completed")


def create_batch_processor(
    process_unanalyzed_only: bool = True,
    max_articles: Optional[int] = None,
    source_types: Optional[List[SourceType]] = None,
    enable_all_analysis: bool = True
) -> BatchArticleProcessor:
    """Factory function to create a batch article processor."""

    config = BatchProcessingConfig(
        process_unanalyzed_only=process_unanalyzed_only,
        max_total_articles=max_articles,
        source_types=source_types or [SourceType.REDDIT, SourceType.TWITTER],
        enable_sentiment_analysis=enable_all_analysis,
        enable_topic_categorization=enable_all_analysis,
        enable_trend_detection=enable_all_analysis
    )

    return BatchArticleProcessor(config)


def process_unanalyzed_articles(max_articles: int = 100) -> Dict[str, Any]:
    """Convenience function to process unanalyzed articles."""
    processor = create_batch_processor(
        process_unanalyzed_only=True,
        max_articles=max_articles
    )

    try:
        return processor.process_articles()
    finally:
        processor.shutdown()


def reprocess_all_articles(max_articles: Optional[int] = None) -> Dict[str, Any]:
    """Convenience function to reprocess all articles (including already analyzed)."""
    processor = create_batch_processor(
        process_unanalyzed_only=False,
        max_articles=max_articles
    )

    try:
        return processor.process_articles()
    finally:
        processor.shutdown()
