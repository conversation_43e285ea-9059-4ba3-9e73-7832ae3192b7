"""
Automatic topic clustering for AI/LLM news articles.

This module provides intelligent clustering of articles based on semantic similarity,
keyword overlap, and topic categorization to identify trending themes.
"""

import re
import math
import numpy as np
from typing import Dict, List, Any, Set, Tuple, Optional
from collections import defaultdict, Counter
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans, DBSCAN
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA

from ..base.analyzer import BaseAnalyzer, AnalysisResult, AnalysisType, ConfidenceLevel, ConfidenceLevel
from scrapers.base.content_parser import ParsedArticle


@dataclass
class ArticleCluster:
    """Represents a cluster of related articles."""
    cluster_id: str
    articles: List[ParsedArticle] = field(default_factory=list)
    centroid_keywords: List[str] = field(default_factory=list)
    dominant_topics: List[str] = field(default_factory=list)
    cluster_score: float = 0.0
    coherence_score: float = 0.0
    trending_score: float = 0.0
    time_span: Tuple[datetime, datetime] = None
    summary: str = ""


@dataclass
class ClusteringResult:
    """Complete clustering analysis result."""
    clusters: List[ArticleCluster]
    total_articles: int
    clustered_articles: int
    noise_articles: int
    silhouette_score: float
    optimal_clusters: int
    trending_clusters: List[str]


class TopicClusterer(BaseAnalyzer):
    """
    Advanced topic clustering system for AI/LLM news articles.
    
    Groups similar articles using:
    - Semantic similarity via TF-IDF and cosine similarity
    - Keyword overlap analysis
    - Topic categorization alignment
    - Temporal clustering for trending topics
    - Content quality weighting
    """
    
    def __init__(self, min_cluster_size: int = 2, max_clusters: int = 20):
        super().__init__("TopicClusterer", "1.0.0")
        
        self.min_cluster_size = min_cluster_size
        self.max_clusters = max_clusters
        
        # TF-IDF vectorizer for semantic similarity
        self.vectorizer = None
        
        # AI/LLM specific stop words (in addition to standard ones)
        self.custom_stop_words = {
            'ai', 'artificial', 'intelligence', 'machine', 'learning', 'deep',
            'neural', 'network', 'model', 'algorithm', 'data', 'technology',
            'system', 'new', 'latest', 'recent', 'today', 'yesterday', 'news',
            'article', 'report', 'study', 'research', 'company', 'tech'
        }
        
        # Trending topic indicators
        self.trending_indicators = {
            'breakthrough', 'launch', 'release', 'announcement', 'unveils',
            'introduces', 'reveals', 'first', 'major', 'significant', 'new',
            'latest', 'cutting-edge', 'revolutionary', 'game-changing'
        }
        
        # Topic categories for clustering guidance
        self.topic_categories = {
            'llm_models': ['gpt', 'claude', 'gemini', 'llama', 'palm', 'bert', 'transformer'],
            'computer_vision': ['vision', 'image', 'video', 'visual', 'opencv', 'cnn'],
            'nlp': ['language', 'text', 'nlp', 'processing', 'understanding', 'generation'],
            'robotics': ['robot', 'robotics', 'autonomous', 'automation', 'control'],
            'ethics_ai': ['ethics', 'bias', 'fairness', 'responsible', 'safety', 'alignment'],
            'business_ai': ['business', 'enterprise', 'commercial', 'industry', 'market'],
            'research': ['research', 'paper', 'study', 'experiment', 'findings', 'arxiv']
        }
    
    def initialize(self) -> bool:
        """Initialize the topic clusterer."""
        try:
            # Initialize TF-IDF vectorizer
            self.vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2),
                min_df=2,
                max_df=0.8,
                lowercase=True,
                token_pattern=r'\b[a-zA-Z][a-zA-Z0-9]*\b'
            )
            
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize TopicClusterer: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.CLUSTERING
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """
        Analyze a single article for clustering preparation.
        Note: Full clustering requires multiple articles via cluster_articles method.
        """
        # Extract features for clustering
        features = self._extract_article_features(article)
        
        # Determine potential topic categories
        potential_topics = self._identify_potential_topics(article)
        
        # Calculate clustering readiness score
        clustering_score = self._calculate_clustering_readiness(article, features)
        
        result_data = {
            'features': features,
            'potential_topics': potential_topics,
            'clustering_score': clustering_score,
            'keywords': self._extract_keywords(article),
            'content_length': len(article.content or ''),
            'has_timestamp': article.published_at is not None
        }
        
        # Determine confidence level
        if clustering_score >= 0.8:
            confidence_level = ConfidenceLevel.VERY_HIGH
        elif clustering_score >= 0.6:
            confidence_level = ConfidenceLevel.HIGH
        elif clustering_score >= 0.4:
            confidence_level = ConfidenceLevel.MEDIUM
        else:
            confidence_level = ConfidenceLevel.LOW

        return AnalysisResult(
            analysis_type=AnalysisType.CLUSTERING,
            confidence=clustering_score,
            confidence_level=confidence_level,
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'analysis_timestamp': datetime.now().isoformat(),
                'feature_count': len(features),
                'topic_categories': len(potential_topics)
            }
        )
    
    def cluster_articles(self, articles: List[ParsedArticle]) -> ClusteringResult:
        """
        Perform clustering analysis on a collection of articles.
        """
        if len(articles) < self.min_cluster_size:
            return ClusteringResult(
                clusters=[],
                total_articles=len(articles),
                clustered_articles=0,
                noise_articles=len(articles),
                silhouette_score=0.0,
                optimal_clusters=0,
                trending_clusters=[]
            )
        
        # Prepare article texts for vectorization
        article_texts = []
        valid_articles = []
        
        for article in articles:
            text = self._prepare_text_for_clustering(article)
            if text and len(text.split()) >= 10:  # Minimum word count
                article_texts.append(text)
                valid_articles.append(article)
        
        if len(valid_articles) < self.min_cluster_size:
            return ClusteringResult(
                clusters=[],
                total_articles=len(articles),
                clustered_articles=0,
                noise_articles=len(articles),
                silhouette_score=0.0,
                optimal_clusters=0,
                trending_clusters=[]
            )
        
        # Create TF-IDF matrix
        try:
            tfidf_matrix = self.vectorizer.fit_transform(article_texts)
        except Exception as e:
            print(f"TF-IDF vectorization failed: {e}")
            return ClusteringResult(
                clusters=[],
                total_articles=len(articles),
                clustered_articles=0,
                noise_articles=len(articles),
                silhouette_score=0.0,
                optimal_clusters=0,
                trending_clusters=[]
            )
        
        # Determine optimal number of clusters
        optimal_k = self._find_optimal_clusters(tfidf_matrix, valid_articles)
        
        # Perform clustering
        clusters = self._perform_clustering(tfidf_matrix, valid_articles, optimal_k)
        
        # Calculate cluster quality metrics
        silhouette_score = self._calculate_silhouette_score(tfidf_matrix, clusters)
        
        # Identify trending clusters
        trending_clusters = self._identify_trending_clusters(clusters)
        
        # Count clustered vs noise articles
        clustered_count = sum(len(cluster.articles) for cluster in clusters)
        noise_count = len(valid_articles) - clustered_count
        
        return ClusteringResult(
            clusters=clusters,
            total_articles=len(articles),
            clustered_articles=clustered_count,
            noise_articles=noise_count,
            silhouette_score=silhouette_score,
            optimal_clusters=optimal_k,
            trending_clusters=[c.cluster_id for c in trending_clusters]
        )
    
    def _extract_article_features(self, article: ParsedArticle) -> Dict[str, Any]:
        """Extract features for clustering analysis."""
        text = f"{article.title or ''} {article.content or ''}"
        
        features = {
            'word_count': len(text.split()),
            'char_count': len(text),
            'has_author': bool(article.author),
            'has_timestamp': bool(article.published_at),
            'url_domain': self._extract_domain(article.url) if article.url else None,
        }
        
        # Add keyword density features
        for category, keywords in self.topic_categories.items():
            density = sum(text.lower().count(keyword) for keyword in keywords)
            features[f'{category}_density'] = density / max(1, len(text.split()))
        
        return features
    
    def _identify_potential_topics(self, article: ParsedArticle) -> List[str]:
        """Identify potential topic categories for the article."""
        text = f"{article.title or ''} {article.content or ''}".lower()
        potential_topics = []
        
        for category, keywords in self.topic_categories.items():
            if any(keyword in text for keyword in keywords):
                potential_topics.append(category)
        
        return potential_topics
    
    def _calculate_clustering_readiness(self, article: ParsedArticle, features: Dict[str, Any]) -> float:
        """Calculate how ready an article is for clustering."""
        score = 0.5  # Base score
        
        # Content length factor
        word_count = features.get('word_count', 0)
        if word_count > 100:
            score += 0.2
        elif word_count < 20:
            score -= 0.3
        
        # Metadata completeness
        if features.get('has_author'):
            score += 0.1
        if features.get('has_timestamp'):
            score += 0.1
        
        # Topic category presence
        topic_densities = [v for k, v in features.items() if k.endswith('_density')]
        if topic_densities and max(topic_densities) > 0.01:
            score += 0.2
        
        return max(0.0, min(1.0, score))
    
    def _extract_keywords(self, article: ParsedArticle) -> List[str]:
        """Extract key terms from article for clustering."""
        text = f"{article.title or ''} {article.content or ''}".lower()
        
        # Simple keyword extraction (could be enhanced with more sophisticated methods)
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        word_freq = Counter(words)
        
        # Filter out common words and get top keywords
        keywords = []
        for word, freq in word_freq.most_common(20):
            if word not in self.custom_stop_words and freq > 1:
                keywords.append(word)
        
        return keywords[:10]  # Return top 10 keywords

    def _prepare_text_for_clustering(self, article: ParsedArticle) -> str:
        """Prepare article text for TF-IDF vectorization."""
        # Combine title and content with title weighted more heavily
        title = article.title or ''
        content = article.content or ''

        # Weight title more heavily by repeating it
        text = f"{title} {title} {content}"

        # Clean and normalize text
        text = re.sub(r'[^\w\s]', ' ', text)  # Remove punctuation
        text = re.sub(r'\s+', ' ', text)      # Normalize whitespace
        text = text.lower().strip()

        return text

    def _extract_domain(self, url: str) -> Optional[str]:
        """Extract domain from URL."""
        if not url:
            return None

        match = re.search(r'https?://(?:www\.)?([^/]+)', url)
        return match.group(1).lower() if match else None

    def _find_optimal_clusters(self, tfidf_matrix, articles: List[ParsedArticle]) -> int:
        """Find optimal number of clusters using elbow method."""
        n_samples = tfidf_matrix.shape[0]
        max_k = min(self.max_clusters, n_samples // 2)

        if max_k < 2:
            return 2

        # Try different numbers of clusters
        inertias = []
        k_range = range(2, max_k + 1)

        for k in k_range:
            try:
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                kmeans.fit(tfidf_matrix)
                inertias.append(kmeans.inertia_)
            except Exception:
                continue

        if not inertias:
            return 2

        # Find elbow point (simplified)
        if len(inertias) >= 3:
            # Calculate rate of change
            changes = [inertias[i-1] - inertias[i] for i in range(1, len(inertias))]
            # Find where improvement starts to diminish
            for i in range(1, len(changes)):
                if changes[i] < changes[i-1] * 0.7:  # 30% reduction in improvement
                    return k_range[i]

        # Default to middle value
        return k_range[len(k_range) // 2]

    def _perform_clustering(self, tfidf_matrix, articles: List[ParsedArticle], n_clusters: int) -> List[ArticleCluster]:
        """Perform the actual clustering."""
        try:
            # Use KMeans for primary clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(tfidf_matrix)

            # Create clusters
            clusters = []
            for cluster_id in range(n_clusters):
                cluster_articles = [articles[i] for i, label in enumerate(cluster_labels) if label == cluster_id]

                if len(cluster_articles) >= self.min_cluster_size:
                    cluster = self._create_cluster(cluster_id, cluster_articles, tfidf_matrix, cluster_labels)
                    clusters.append(cluster)

            return clusters

        except Exception as e:
            print(f"Clustering failed: {e}")
            return []

    def _create_cluster(self, cluster_id: int, articles: List[ParsedArticle],
                       tfidf_matrix, cluster_labels) -> ArticleCluster:
        """Create a detailed cluster object."""
        cluster_indices = [i for i, label in enumerate(cluster_labels) if label == cluster_id]

        # Extract centroid keywords
        centroid_keywords = self._extract_centroid_keywords(cluster_indices, tfidf_matrix)

        # Identify dominant topics
        dominant_topics = self._identify_dominant_topics(articles)

        # Calculate cluster scores
        cluster_score = self._calculate_cluster_score(articles)
        coherence_score = self._calculate_coherence_score(cluster_indices, tfidf_matrix)
        trending_score = self._calculate_trending_score(articles)

        # Calculate time span
        time_span = self._calculate_time_span(articles)

        # Generate cluster summary
        summary = self._generate_cluster_summary(articles, centroid_keywords, dominant_topics)

        return ArticleCluster(
            cluster_id=f"cluster_{cluster_id}",
            articles=articles,
            centroid_keywords=centroid_keywords,
            dominant_topics=dominant_topics,
            cluster_score=cluster_score,
            coherence_score=coherence_score,
            trending_score=trending_score,
            time_span=time_span,
            summary=summary
        )

    def _extract_centroid_keywords(self, cluster_indices: List[int], tfidf_matrix) -> List[str]:
        """Extract keywords that represent the cluster centroid."""
        if not cluster_indices:
            return []

        # Calculate centroid
        cluster_vectors = tfidf_matrix[cluster_indices]
        centroid = np.mean(cluster_vectors.toarray(), axis=0)

        # Get feature names
        feature_names = self.vectorizer.get_feature_names_out()

        # Get top features by TF-IDF score
        top_indices = np.argsort(centroid)[-10:][::-1]  # Top 10 features

        return [feature_names[i] for i in top_indices if centroid[i] > 0]

    def _identify_dominant_topics(self, articles: List[ParsedArticle]) -> List[str]:
        """Identify dominant topic categories in the cluster."""
        topic_counts = defaultdict(int)

        for article in articles:
            potential_topics = self._identify_potential_topics(article)
            for topic in potential_topics:
                topic_counts[topic] += 1

        # Return topics that appear in at least 30% of articles
        threshold = len(articles) * 0.3
        return [topic for topic, count in topic_counts.items() if count >= threshold]

    def _calculate_cluster_score(self, articles: List[ParsedArticle]) -> float:
        """Calculate overall quality score for the cluster."""
        if not articles:
            return 0.0

        scores = []
        for article in articles:
            score = 0.5  # Base score

            # Content quality factors
            if article.content and len(article.content) > 500:
                score += 0.2
            if article.author:
                score += 0.1
            if article.published_at:
                score += 0.1

            # Relevance factors (simplified)
            text = f"{article.title or ''} {article.content or ''}".lower()
            ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'deep learning']
            if any(keyword in text for keyword in ai_keywords):
                score += 0.2

            scores.append(score)

        return sum(scores) / len(scores)

    def _calculate_coherence_score(self, cluster_indices: List[int], tfidf_matrix) -> float:
        """Calculate how coherent/similar articles in the cluster are."""
        if len(cluster_indices) < 2:
            return 1.0

        # Calculate pairwise similarities within cluster
        cluster_vectors = tfidf_matrix[cluster_indices]
        similarities = cosine_similarity(cluster_vectors)

        # Get upper triangle (excluding diagonal)
        n = similarities.shape[0]
        upper_triangle = similarities[np.triu_indices(n, k=1)]

        return float(np.mean(upper_triangle)) if len(upper_triangle) > 0 else 0.0

    def _calculate_trending_score(self, articles: List[ParsedArticle]) -> float:
        """Calculate how trending/timely the cluster is."""
        if not articles:
            return 0.0

        trending_score = 0.0

        # Check for trending keywords
        for article in articles:
            text = f"{article.title or ''} {article.content or ''}".lower()
            trending_count = sum(1 for indicator in self.trending_indicators if indicator in text)
            trending_score += trending_count

        # Normalize by number of articles
        trending_score /= len(articles)

        # Time recency factor
        recent_articles = 0
        now = datetime.now()

        for article in articles:
            if article.published_at:
                age_days = (now - article.published_at).days
                if age_days <= 7:  # Within a week
                    recent_articles += 1

        recency_factor = recent_articles / len(articles)

        return min(1.0, (trending_score * 0.7) + (recency_factor * 0.3))

    def _calculate_time_span(self, articles: List[ParsedArticle]) -> Optional[Tuple[datetime, datetime]]:
        """Calculate the time span of articles in the cluster."""
        timestamps = [article.published_at for article in articles if article.published_at]

        if not timestamps:
            return None

        return (min(timestamps), max(timestamps))

    def _generate_cluster_summary(self, articles: List[ParsedArticle],
                                 keywords: List[str], topics: List[str]) -> str:
        """Generate a human-readable summary of the cluster."""
        if not articles:
            return "Empty cluster"

        # Basic cluster info
        article_count = len(articles)

        # Most common words from titles
        title_words = []
        for article in articles:
            if article.title:
                words = re.findall(r'\b[A-Za-z]{3,}\b', article.title)
                title_words.extend(words)

        common_title_words = [word for word, count in Counter(title_words).most_common(3)]

        # Build summary
        summary_parts = [f"Cluster of {article_count} articles"]

        if topics:
            summary_parts.append(f"focusing on {', '.join(topics)}")

        if common_title_words:
            summary_parts.append(f"commonly discussing {', '.join(common_title_words)}")

        if keywords:
            summary_parts.append(f"with key terms: {', '.join(keywords[:5])}")

        return ". ".join(summary_parts) + "."

    def _calculate_silhouette_score(self, tfidf_matrix, clusters: List[ArticleCluster]) -> float:
        """Calculate silhouette score for clustering quality."""
        if len(clusters) < 2:
            return 0.0

        try:
            from sklearn.metrics import silhouette_score

            # Create labels array
            labels = []
            for i, cluster in enumerate(clusters):
                labels.extend([i] * len(cluster.articles))

            if len(set(labels)) < 2:
                return 0.0

            # Calculate silhouette score
            score = silhouette_score(tfidf_matrix[:len(labels)], labels)
            return float(score)

        except Exception:
            return 0.0

    def _identify_trending_clusters(self, clusters: List[ArticleCluster]) -> List[ArticleCluster]:
        """Identify which clusters represent trending topics."""
        if not clusters:
            return []

        # Sort by trending score
        trending_clusters = sorted(clusters, key=lambda c: c.trending_score, reverse=True)

        # Return top 30% or at least 1 cluster
        num_trending = max(1, len(clusters) // 3)
        return trending_clusters[:num_trending]

    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for clustering."""
        if not super().can_analyze(article):
            return False

        # Need at least title or substantial content
        text = f"{article.title or ''} {article.content or ''}"
        return len(text.split()) >= 10
