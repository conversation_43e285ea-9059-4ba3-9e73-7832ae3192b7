"""
Keyword extraction for AI/LLM news articles.
"""

import re
from typing import Dict, List, Any, Set, Tuple, Optional
from collections import Counter, defaultdict
from dataclasses import dataclass
from math import log

from ..base.analyzer import BaseAnalyzer, AnalysisResult, AnalysisType
from scrapers.base.content_parser import Parsed<PERSON>rticle


@dataclass
class ExtractedKeyword:
    """Represents an extracted keyword with metadata."""
    keyword: str
    score: float
    frequency: int
    positions: List[int]
    context: List[str]
    category: Optional[str] = None


class KeywordExtractor(BaseAnalyzer):
    """
    Advanced keyword extractor for AI/LLM news content.
    
    Uses multiple extraction methods:
    1. TF-IDF based extraction
    2. AI/ML domain-specific keyword identification
    3. Named entity recognition patterns
    4. Phrase extraction
    """
    
    def __init__(self):
        super().__init__("KeywordExtractor", "1.0.0")
        
        # Stop words for filtering
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'this', 'that',
            'these', 'those', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours',
            'ourselves', 'you', 'your', 'yours', 'yourself', 'yourselves', 'he',
            'him', 'his', 'himself', 'she', 'her', 'hers', 'herself', 'it', 'its',
            'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what',
            'which', 'who', 'whom', 'whose', 'this', 'that', 'these', 'those',
            'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
            'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'will', 'would',
            'could', 'should', 'may', 'might', 'must', 'can', 'said', 'says'
        }
        
        # AI/ML domain-specific keyword categories
        self.domain_categories = {
            'technologies': {
                'machine learning', 'deep learning', 'neural network', 'artificial intelligence',
                'natural language processing', 'computer vision', 'reinforcement learning',
                'supervised learning', 'unsupervised learning', 'transformer', 'gpt', 'bert',
                'llm', 'large language model', 'generative ai', 'chatgpt', 'claude'
            },
            'companies': {
                'openai', 'anthropic', 'google', 'microsoft', 'amazon', 'meta', 'nvidia',
                'intel', 'apple', 'tesla', 'deepmind', 'hugging face', 'stability ai'
            },
            'concepts': {
                'algorithm', 'model', 'training', 'inference', 'fine-tuning', 'prompt engineering',
                'few-shot learning', 'zero-shot learning', 'transfer learning', 'federated learning',
                'multimodal', 'embedding', 'attention mechanism', 'backpropagation'
            },
            'applications': {
                'autonomous vehicles', 'robotics', 'healthcare ai', 'fintech', 'edtech',
                'recommendation system', 'fraud detection', 'sentiment analysis', 'chatbot',
                'virtual assistant', 'image recognition', 'speech recognition'
            },
            'metrics': {
                'accuracy', 'precision', 'recall', 'f1 score', 'auc', 'loss function',
                'gradient descent', 'learning rate', 'epoch', 'batch size', 'overfitting',
                'underfitting', 'bias', 'variance', 'cross-validation'
            }
        }
        
        # Create flat set of all domain keywords
        self.domain_keywords = set()
        for category_keywords in self.domain_categories.values():
            self.domain_keywords.update(category_keywords)
        
        # Patterns for named entities and technical terms
        self.entity_patterns = [
            r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # Person names
            r'\b[A-Z]{2,}\b',  # Acronyms
            r'\b\w+(?:-\w+)+\b',  # Hyphenated terms
            r'\b\w+\d+\b',  # Terms with numbers (GPT-4, etc.)
            r'\b[A-Z][a-z]*(?:[A-Z][a-z]*)+\b'  # CamelCase terms
        ]
        
        # Compile patterns
        self.compiled_entity_patterns = [re.compile(p) for p in self.entity_patterns]
        
        # Common AI/ML phrase patterns
        self.phrase_patterns = [
            r'\b(?:machine|deep|reinforcement) learning\b',
            r'\b(?:natural language|computer vision|speech recognition)\b',
            r'\b(?:neural|convolutional|recurrent) network\b',
            r'\b(?:large language|foundation|generative) model\b',
            r'\b(?:artificial|general|narrow) intelligence\b'
        ]
        
        self.compiled_phrase_patterns = [re.compile(p, re.IGNORECASE) for p in self.phrase_patterns]
    
    def initialize(self) -> bool:
        """Initialize the keyword extractor."""
        try:
            # Patterns are already compiled in __init__
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize KeywordExtractor: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.KEYWORD
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Extract keywords from an article."""
        # Combine title and content for analysis
        text = f"{article.title or ''} {article.content or ''}"
        
        # Extract keywords using different methods
        tfidf_keywords = self._extract_tfidf_keywords(text)
        domain_keywords = self._extract_domain_keywords(text)
        entity_keywords = self._extract_entity_keywords(text)
        phrase_keywords = self._extract_phrase_keywords(text)
        
        # Combine and rank all keywords
        all_keywords = self._combine_and_rank_keywords(
            tfidf_keywords, domain_keywords, entity_keywords, phrase_keywords
        )
        
        # Calculate overall confidence
        confidence = self._calculate_extraction_confidence(all_keywords, text)
        
        # Get top keywords
        top_keywords = all_keywords[:20]  # Top 20 keywords
        
        # Categorize keywords
        categorized_keywords = self._categorize_keywords(top_keywords)
        
        # Create result data
        result_data = {
            'keywords': [self._keyword_to_dict(kw) for kw in top_keywords],
            'categorized_keywords': categorized_keywords,
            'extraction_methods': {
                'tfidf_count': len(tfidf_keywords),
                'domain_count': len(domain_keywords),
                'entity_count': len(entity_keywords),
                'phrase_count': len(phrase_keywords)
            },
            'total_keywords': len(all_keywords),
            'text_length': len(text),
            'analysis_method': 'multi_method_extraction'
        }
        
        return AnalysisResult(
            analysis_type=AnalysisType.KEYWORD,
            confidence=confidence,
            confidence_level=None,  # Will be set in __post_init__
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'keywords_extracted': len(top_keywords)
            }
        )
    
    def _extract_tfidf_keywords(self, text: str) -> List[ExtractedKeyword]:
        """Extract keywords using TF-IDF approach."""
        # Simple TF-IDF implementation
        words = re.findall(r'\b\w+\b', text.lower())
        words = [w for w in words if w not in self.stop_words and len(w) > 2]
        
        if not words:
            return []
        
        # Calculate term frequency
        word_count = Counter(words)
        total_words = len(words)
        
        # Calculate TF-IDF scores (simplified - no corpus for IDF)
        keywords = []
        for word, count in word_count.items():
            tf = count / total_words
            # Use log frequency as a proxy for IDF
            idf = log(total_words / count) if count > 0 else 0
            score = tf * idf
            
            if score > 0.001:  # Minimum threshold
                # Find positions and context
                positions = self._find_word_positions(text.lower(), word)
                context = self._extract_word_context(text, word, positions[:3])
                
                keyword = ExtractedKeyword(
                    keyword=word,
                    score=score,
                    frequency=count,
                    positions=positions,
                    context=context
                )
                keywords.append(keyword)
        
        return sorted(keywords, key=lambda k: k.score, reverse=True)[:15]
    
    def _extract_domain_keywords(self, text: str) -> List[ExtractedKeyword]:
        """Extract AI/ML domain-specific keywords."""
        text_lower = text.lower()
        keywords = []
        
        for keyword in self.domain_keywords:
            if keyword in text_lower:
                frequency = text_lower.count(keyword)
                positions = self._find_phrase_positions(text_lower, keyword)
                context = self._extract_word_context(text, keyword, positions[:3])
                
                # Higher score for domain-specific keywords
                score = frequency * 0.5 + 0.3  # Base boost for domain relevance
                
                extracted_keyword = ExtractedKeyword(
                    keyword=keyword,
                    score=score,
                    frequency=frequency,
                    positions=positions,
                    context=context,
                    category=self._get_keyword_category(keyword)
                )
                keywords.append(extracted_keyword)
        
        return sorted(keywords, key=lambda k: k.score, reverse=True)
    
    def _extract_entity_keywords(self, text: str) -> List[ExtractedKeyword]:
        """Extract named entities and technical terms."""
        keywords = []
        
        for pattern in self.compiled_entity_patterns:
            matches = pattern.finditer(text)
            entity_counts = Counter()
            entity_positions = defaultdict(list)
            
            for match in matches:
                entity = match.group().lower()
                if entity not in self.stop_words and len(entity) > 2:
                    entity_counts[entity] += 1
                    entity_positions[entity].append(match.start())
            
            for entity, count in entity_counts.items():
                if count >= 1:  # At least one occurrence
                    positions = entity_positions[entity]
                    context = self._extract_word_context(text, entity, positions[:3])
                    
                    # Score based on frequency and entity type
                    score = count * 0.3 + 0.2  # Base score for being an entity
                    
                    keyword = ExtractedKeyword(
                        keyword=entity,
                        score=score,
                        frequency=count,
                        positions=positions,
                        context=context,
                        category='entity'
                    )
                    keywords.append(keyword)
        
        return sorted(keywords, key=lambda k: k.score, reverse=True)[:10]
    
    def _extract_phrase_keywords(self, text: str) -> List[ExtractedKeyword]:
        """Extract multi-word phrases and technical terms."""
        keywords = []
        
        for pattern in self.compiled_phrase_patterns:
            matches = pattern.finditer(text)
            phrase_counts = Counter()
            phrase_positions = defaultdict(list)
            
            for match in matches:
                phrase = match.group().lower()
                phrase_counts[phrase] += 1
                phrase_positions[phrase].append(match.start())
            
            for phrase, count in phrase_counts.items():
                positions = phrase_positions[phrase]
                context = self._extract_word_context(text, phrase, positions[:3])
                
                # Higher score for technical phrases
                score = count * 0.6 + 0.4  # Base boost for being a technical phrase
                
                keyword = ExtractedKeyword(
                    keyword=phrase,
                    score=score,
                    frequency=count,
                    positions=positions,
                    context=context,
                    category='phrase'
                )
                keywords.append(keyword)
        
        return sorted(keywords, key=lambda k: k.score, reverse=True)
    
    def _combine_and_rank_keywords(self, *keyword_lists) -> List[ExtractedKeyword]:
        """Combine keywords from different methods and rank them."""
        keyword_map = {}
        
        for keyword_list in keyword_lists:
            for keyword in keyword_list:
                key = keyword.keyword.lower()
                if key in keyword_map:
                    # Combine scores and update metadata
                    existing = keyword_map[key]
                    existing.score = max(existing.score, keyword.score)
                    existing.frequency += keyword.frequency
                    existing.positions.extend(keyword.positions)
                    existing.context.extend(keyword.context)
                    if keyword.category and not existing.category:
                        existing.category = keyword.category
                else:
                    keyword_map[key] = keyword
        
        # Remove duplicates from positions and context
        for keyword in keyword_map.values():
            keyword.positions = sorted(list(set(keyword.positions)))
            keyword.context = list(set(keyword.context))[:5]  # Limit context
        
        return sorted(keyword_map.values(), key=lambda k: k.score, reverse=True)
    
    def _calculate_extraction_confidence(self, keywords: List[ExtractedKeyword], text: str) -> float:
        """Calculate confidence in keyword extraction."""
        if not keywords:
            return 0.0
        
        # Factors affecting confidence
        keyword_count = len(keywords)
        avg_score = sum(k.score for k in keywords) / keyword_count
        text_length = len(text.split())
        
        # Confidence based on number of keywords, average score, and text length
        confidence = min(1.0, (keyword_count / 20.0) * 0.4 + avg_score * 0.4 + min(1.0, text_length / 500.0) * 0.2)
        
        return confidence
    
    def _categorize_keywords(self, keywords: List[ExtractedKeyword]) -> Dict[str, List[str]]:
        """Categorize keywords by domain categories."""
        categorized = defaultdict(list)
        
        for keyword in keywords:
            if keyword.category:
                categorized[keyword.category].append(keyword.keyword)
            else:
                # Try to categorize based on domain categories
                category = self._get_keyword_category(keyword.keyword)
                if category:
                    categorized[category].append(keyword.keyword)
                else:
                    categorized['general'].append(keyword.keyword)
        
        return dict(categorized)
    
    def _get_keyword_category(self, keyword: str) -> Optional[str]:
        """Get the category of a keyword."""
        keyword_lower = keyword.lower()
        for category, keywords in self.domain_categories.items():
            if keyword_lower in keywords:
                return category
        return None
    
    def _find_word_positions(self, text: str, word: str) -> List[int]:
        """Find all positions of a word in text."""
        positions = []
        start = 0
        while True:
            pos = text.find(word, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        return positions
    
    def _find_phrase_positions(self, text: str, phrase: str) -> List[int]:
        """Find all positions of a phrase in text."""
        positions = []
        start = 0
        while True:
            pos = text.find(phrase, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + len(phrase)
        return positions
    
    def _extract_word_context(self, text: str, word: str, positions: List[int]) -> List[str]:
        """Extract context around word occurrences."""
        contexts = []
        for pos in positions[:3]:  # Limit to first 3 occurrences
            start = max(0, pos - 50)
            end = min(len(text), pos + len(word) + 50)
            context = text[start:end].strip()
            if context:
                contexts.append(context)
        return contexts
    
    def _keyword_to_dict(self, keyword: ExtractedKeyword) -> Dict[str, Any]:
        """Convert ExtractedKeyword to dictionary."""
        return {
            'keyword': keyword.keyword,
            'score': keyword.score,
            'frequency': keyword.frequency,
            'category': keyword.category,
            'context_preview': keyword.context[0] if keyword.context else None
        }
    
    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for keywords."""
        if not super().can_analyze(article):
            return False
        
        # Check if article has enough text for meaningful keyword extraction
        text = f"{article.title or ''} {article.content or ''}"
        words = re.findall(r'\b\w+\b', text.lower())
        
        return len(words) >= 25  # Minimum 25 words for keyword extraction
