"""
Main content processor that orchestrates all analysis components.
"""

import time
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from .base.analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ContentAnalysis, AnalysisType
from .sentiment.analyzer import SentimentAnalyzer
from .topics.categorizer import TopicCategorizer
from .trends.detector import TrendDetector
from .keywords.extractor import KeywordExtractor
from .relevance.scorer import ContentRelevanceScorer
from .clustering.topic_clusterer import TopicClusterer
from .quality.filter import ContentQualityFilter
from .summarization.summarizer import ContentSummarizer
from scrapers.base.content_parser import ParsedArticle


@dataclass
class ProcessingConfig:
    """Configuration for content processing."""
    enable_sentiment: bool = True
    enable_topics: bool = True
    enable_trends: bool = True
    enable_keywords: bool = True
    enable_relevance: bool = True
    enable_clustering: bool = True
    enable_quality_filter: bool = True
    enable_summarization: bool = True
    parallel_processing: bool = False
    max_workers: int = 4
    timeout_seconds: int = 30
    min_confidence_threshold: float = 0.3
    filter_low_quality: bool = True
    quality_threshold: float = 0.4


@dataclass
class ProcessingStats:
    """Statistics for content processing operations."""
    articles_processed: int = 0
    total_processing_time_ms: float = 0.0
    average_processing_time_ms: float = 0.0
    successful_analyses: int = 0
    failed_analyses: int = 0
    analysis_type_stats: Dict[str, int] = field(default_factory=dict)
    
    def update(self, processing_time_ms: float, success: bool, analysis_types: Set[AnalysisType]):
        """Update processing statistics."""
        self.articles_processed += 1
        self.total_processing_time_ms += processing_time_ms
        self.average_processing_time_ms = self.total_processing_time_ms / self.articles_processed
        
        if success:
            self.successful_analyses += 1
            for analysis_type in analysis_types:
                self.analysis_type_stats[analysis_type.value] = (
                    self.analysis_type_stats.get(analysis_type.value, 0) + 1
                )
        else:
            self.failed_analyses += 1


class ContentProcessor:
    """
    Main content processor that orchestrates all analysis components.
    
    Provides a unified interface for analyzing AI/LLM news articles with
    sentiment analysis, topic categorization, trend detection, and keyword extraction.
    """
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        self.stats = ProcessingStats()
        
        # Initialize analyzers based on configuration
        self.analyzers = []
        self._initialize_analyzers()
        
        # Create analyzer pipeline
        self.pipeline = AnalyzerPipeline(self.analyzers)
        
        # Thread pool for parallel processing
        self.executor = None
        if self.config.parallel_processing:
            self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
    
    def _initialize_analyzers(self):
        """Initialize analyzers based on configuration."""
        self.analyzers.clear()

        if self.config.enable_sentiment:
            self.analyzers.append(SentimentAnalyzer())

        if self.config.enable_topics:
            self.analyzers.append(TopicCategorizer())

        if self.config.enable_trends:
            self.analyzers.append(TrendDetector())

        if self.config.enable_keywords:
            self.analyzers.append(KeywordExtractor())

        # Phase 8 analyzers
        if self.config.enable_relevance:
            self.analyzers.append(ContentRelevanceScorer())

        if self.config.enable_quality_filter:
            self.analyzers.append(ContentQualityFilter(strict_mode=False))

        if self.config.enable_summarization:
            self.analyzers.append(ContentSummarizer())

        # Note: Clustering analyzer is handled separately as it requires multiple articles
    
    def initialize(self) -> bool:
        """Initialize all analyzers."""
        success = True
        for analyzer in self.analyzers:
            if not analyzer.initialize():
                print(f"Failed to initialize {analyzer.name}")
                success = False
        return success
    
    def process_article(self, article: ParsedArticle) -> Optional[ContentAnalysis]:
        """Process a single article with all enabled analyzers."""
        if not article or not self._can_process_article(article):
            return None

        start_time = time.time()

        try:
            # Run analysis pipeline
            analysis = self.pipeline.analyze(article)

            # Check quality filter if enabled
            if self.config.enable_quality_filter and self.config.filter_low_quality:
                quality_result = analysis.get_result(AnalysisType.QUALITY)
                if quality_result:
                    should_filter = quality_result.data.get('should_filter', False)
                    overall_score = quality_result.data.get('overall_score', 1.0)

                    if should_filter or overall_score < self.config.quality_threshold:
                        # Article filtered out due to low quality
                        processing_time_ms = (time.time() - start_time) * 1000
                        self.stats.update(processing_time_ms, False, set())
                        return None

            # Filter results by confidence threshold
            filtered_results = []
            analysis_types = set()

            for result in analysis.analysis_results:
                if result.confidence >= self.config.min_confidence_threshold:
                    filtered_results.append(result)
                    analysis_types.add(result.analysis_type)

            analysis.analysis_results = filtered_results
            analysis._update_overall_confidence()

            # Update statistics
            processing_time_ms = (time.time() - start_time) * 1000
            self.stats.update(processing_time_ms, True, analysis_types)

            return analysis

        except Exception as e:
            print(f"Error processing article {article.url}: {e}")
            processing_time_ms = (time.time() - start_time) * 1000
            self.stats.update(processing_time_ms, False, set())
            return None
    
    def process_articles(self, articles: List[ParsedArticle]) -> List[ContentAnalysis]:
        """Process multiple articles."""
        if not articles:
            return []
        
        if self.config.parallel_processing and self.executor:
            return self._process_articles_parallel(articles)
        else:
            return self._process_articles_sequential(articles)
    
    def _process_articles_sequential(self, articles: List[ParsedArticle]) -> List[ContentAnalysis]:
        """Process articles sequentially."""
        results = []
        
        for article in articles:
            analysis = self.process_article(article)
            if analysis:
                results.append(analysis)
        
        return results
    
    def _process_articles_parallel(self, articles: List[ParsedArticle]) -> List[ContentAnalysis]:
        """Process articles in parallel."""
        results = []
        
        # Submit all articles for processing
        future_to_article = {
            self.executor.submit(self.process_article, article): article
            for article in articles
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_article, timeout=self.config.timeout_seconds):
            try:
                analysis = future.result()
                if analysis:
                    results.append(analysis)
            except Exception as e:
                article = future_to_article[future]
                print(f"Error processing article {article.url}: {e}")
        
        return results

    def cluster_articles(self, articles: List[ParsedArticle]) -> Optional[Any]:
        """Cluster articles using the topic clusterer."""
        if not self.config.enable_clustering or not articles:
            return None

        try:
            clusterer = TopicClusterer()
            if not clusterer.initialize():
                print("Failed to initialize topic clusterer")
                return None

            clustering_result = clusterer.cluster_articles(articles)
            return clustering_result

        except Exception as e:
            print(f"Error clustering articles: {e}")
            return None

    def get_analysis_summary(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Generate a summary of analysis results."""
        if not analyses:
            return {}
        
        # Aggregate sentiment data
        sentiment_data = self._aggregate_sentiment_data(analyses)
        
        # Aggregate topic data
        topic_data = self._aggregate_topic_data(analyses)
        
        # Aggregate trend data
        trend_data = self._aggregate_trend_data(analyses)
        
        # Aggregate keyword data
        keyword_data = self._aggregate_keyword_data(analyses)

        # Aggregate Phase 8 data
        relevance_data = self._aggregate_relevance_data(analyses)
        quality_data = self._aggregate_quality_data(analyses)
        summary_data = self._aggregate_summary_data(analyses)

        # Overall statistics
        total_articles = len(analyses)
        avg_confidence = sum(a.overall_confidence for a in analyses) / total_articles

        return {
            'summary_stats': {
                'total_articles': total_articles,
                'average_confidence': avg_confidence,
                'analysis_types_used': list(set(
                    result.analysis_type.value
                    for analysis in analyses
                    for result in analysis.analysis_results
                ))
            },
            'sentiment_summary': sentiment_data,
            'topic_summary': topic_data,
            'trend_summary': trend_data,
            'keyword_summary': keyword_data,
            'relevance_summary': relevance_data,
            'quality_summary': quality_data,
            'summary_summary': summary_data,
            'processing_stats': self.get_processing_stats()
        }
    
    def _aggregate_sentiment_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate sentiment analysis data."""
        sentiment_results = []
        for analysis in analyses:
            sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
            if sentiment_result:
                sentiment_results.append(sentiment_result)
        
        if not sentiment_results:
            return {}
        
        # Count polarities
        polarity_counts = {}
        scores = []
        
        for result in sentiment_results:
            polarity = result.data.get('polarity')
            if polarity:
                polarity_counts[polarity] = polarity_counts.get(polarity, 0) + 1
            
            score = result.data.get('score')
            if score is not None:
                scores.append(score)
        
        avg_score = sum(scores) / len(scores) if scores else 0
        
        return {
            'polarity_distribution': polarity_counts,
            'average_sentiment_score': avg_score,
            'total_analyzed': len(sentiment_results)
        }
    
    def _aggregate_topic_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate topic categorization data."""
        topic_results = []
        for analysis in analyses:
            topic_result = analysis.get_result(AnalysisType.TOPIC)
            if topic_result:
                topic_results.append(topic_result)
        
        if not topic_results:
            return {}
        
        # Count primary categories
        primary_category_counts = {}
        all_categories = set()
        
        for result in topic_results:
            primary_categories = result.data.get('primary_categories', [])
            for category in primary_categories:
                primary_category_counts[category] = primary_category_counts.get(category, 0) + 1
                all_categories.add(category)
            
            secondary_categories = result.data.get('secondary_categories', [])
            all_categories.update(secondary_categories)
        
        return {
            'primary_category_distribution': primary_category_counts,
            'total_categories_detected': len(all_categories),
            'total_analyzed': len(topic_results)
        }
    
    def _aggregate_trend_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate trend detection data."""
        trend_results = []
        for analysis in analyses:
            trend_result = analysis.get_result(AnalysisType.TREND)
            if trend_result:
                trend_results.append(trend_result)
        
        if not trend_results:
            return {}
        
        # Count trend types
        trend_type_counts = {}
        trend_strengths = {}
        
        for result in trend_results:
            primary_trends = result.data.get('primary_trends', [])
            for trend in primary_trends:
                trend_type_counts[trend] = trend_type_counts.get(trend, 0) + 1
            
            overall_strength = result.data.get('overall_strength')
            if overall_strength:
                trend_strengths[overall_strength] = trend_strengths.get(overall_strength, 0) + 1
        
        return {
            'trend_type_distribution': trend_type_counts,
            'trend_strength_distribution': trend_strengths,
            'total_analyzed': len(trend_results)
        }
    
    def _aggregate_keyword_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate keyword extraction data."""
        keyword_results = []
        for analysis in analyses:
            keyword_result = analysis.get_result(AnalysisType.KEYWORD)
            if keyword_result:
                keyword_results.append(keyword_result)
        
        if not keyword_results:
            return {}
        
        # Aggregate top keywords
        keyword_counts = {}
        category_counts = {}
        
        for result in keyword_results:
            keywords = result.data.get('keywords', [])
            for kw_data in keywords:
                keyword = kw_data.get('keyword')
                if keyword:
                    keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
            
            categorized = result.data.get('categorized_keywords', {})
            for category, kw_list in categorized.items():
                category_counts[category] = category_counts.get(category, 0) + len(kw_list)
        
        # Get top keywords
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:20]
        
        return {
            'top_keywords': dict(top_keywords),
            'keyword_category_distribution': category_counts,
            'total_analyzed': len(keyword_results)
        }

    def _aggregate_relevance_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate relevance scoring data."""
        relevance_results = []
        for analysis in analyses:
            relevance_result = analysis.get_result(AnalysisType.RELEVANCE)
            if relevance_result:
                relevance_results.append(relevance_result)

        if not relevance_results:
            return {}

        scores = []
        high_relevance_count = 0

        for result in relevance_results:
            overall_score = result.data.get('overall_score', 0)
            scores.append(overall_score)
            if overall_score > 0.7:
                high_relevance_count += 1

        avg_score = sum(scores) / len(scores) if scores else 0

        return {
            'average_relevance_score': avg_score,
            'high_relevance_articles': high_relevance_count,
            'relevance_distribution': {
                'high': sum(1 for s in scores if s > 0.7),
                'medium': sum(1 for s in scores if 0.4 <= s <= 0.7),
                'low': sum(1 for s in scores if s < 0.4)
            },
            'total_analyzed': len(relevance_results)
        }

    def _aggregate_quality_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate quality filtering data."""
        quality_results = []
        for analysis in analyses:
            quality_result = analysis.get_result(AnalysisType.QUALITY)
            if quality_result:
                quality_results.append(quality_result)

        if not quality_results:
            return {}

        quality_levels = {}
        filtered_count = 0
        scores = []

        for result in quality_results:
            quality_level = result.data.get('quality_level', 'unknown')
            quality_levels[quality_level] = quality_levels.get(quality_level, 0) + 1

            should_filter = result.data.get('should_filter', False)
            if should_filter:
                filtered_count += 1

            overall_score = result.data.get('overall_score', 0)
            scores.append(overall_score)

        avg_score = sum(scores) / len(scores) if scores else 0

        return {
            'quality_level_distribution': quality_levels,
            'filtered_articles_count': filtered_count,
            'average_quality_score': avg_score,
            'filter_rate': filtered_count / len(quality_results) if quality_results else 0,
            'total_analyzed': len(quality_results)
        }

    def _aggregate_summary_data(self, analyses: List[ContentAnalysis]) -> Dict[str, Any]:
        """Aggregate summarization data."""
        summary_results = []
        for analysis in analyses:
            summary_result = analysis.get_result(AnalysisType.SUMMARY)
            if summary_result:
                summary_results.append(summary_result)

        if not summary_results:
            return {}

        compression_ratios = []
        summary_lengths = []

        for result in summary_results:
            compression_ratio = result.data.get('compression_ratio', 0)
            if compression_ratio > 0:
                compression_ratios.append(compression_ratio)

            summary_length = result.data.get('summary_length', 0)
            if summary_length > 0:
                summary_lengths.append(summary_length)

        avg_compression = sum(compression_ratios) / len(compression_ratios) if compression_ratios else 0
        avg_length = sum(summary_lengths) / len(summary_lengths) if summary_lengths else 0

        return {
            'average_compression_ratio': avg_compression,
            'average_summary_length': avg_length,
            'summaries_generated': len(summary_results),
            'total_analyzed': len(summary_results)
        }
    
    def _can_process_article(self, article: ParsedArticle) -> bool:
        """Check if article can be processed."""
        if not article or not article.content:
            return False
        
        # Basic content length check
        text = f"{article.title or ''} {article.content or ''}"
        return len(text.strip()) >= 100  # Minimum content length
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return {
            'articles_processed': self.stats.articles_processed,
            'total_processing_time_ms': self.stats.total_processing_time_ms,
            'average_processing_time_ms': self.stats.average_processing_time_ms,
            'successful_analyses': self.stats.successful_analyses,
            'failed_analyses': self.stats.failed_analyses,
            'success_rate': (
                self.stats.successful_analyses / self.stats.articles_processed
                if self.stats.articles_processed > 0 else 0
            ),
            'analysis_type_stats': self.stats.analysis_type_stats,
            'analyzer_stats': [analyzer.get_stats() for analyzer in self.analyzers]
        }
    
    def reset_stats(self):
        """Reset processing statistics."""
        self.stats = ProcessingStats()
        for analyzer in self.analyzers:
            analyzer.reset_stats()
    
    def shutdown(self):
        """Shutdown the processor and cleanup resources."""
        if self.executor:
            self.executor.shutdown(wait=True)
