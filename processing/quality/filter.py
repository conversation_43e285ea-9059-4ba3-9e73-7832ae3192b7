"""
Content quality filtering for AI/LLM news articles.

This module provides comprehensive quality filtering to detect and filter out
spam, low-quality content, duplicate articles, and irrelevant content.
"""

import re
import math
import hashlib
from typing import Dict, List, Any, Set, Tuple, Optional, Union
from collections import defaultdict, Counter
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from ..base.analyzer import BaseAnalyzer, AnalysisResult, AnalysisType, ConfidenceLevel, ConfidenceLevel
from scrapers.base.content_parser import ParsedArticle


class QualityLevel(Enum):
    """Content quality levels."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    SPAM = "spam"
    DUPLICATE = "duplicate"


class FilterReason(Enum):
    """Reasons for filtering content."""
    SPAM_CONTENT = "spam_content"
    LOW_QUALITY = "low_quality"
    DUPLICATE_CONTENT = "duplicate_content"
    IRRELEVANT_TOPIC = "irrelevant_topic"
    INSUFFICIENT_CONTENT = "insufficient_content"
    SUSPICIOUS_PATTERNS = "suspicious_patterns"
    POOR_LANGUAGE = "poor_language"
    CLICKBAIT = "clickbait"


@dataclass
class QualityMetrics:
    """Individual quality assessment metrics."""
    content_length_score: float = 0.0
    language_quality_score: float = 0.0
    relevance_score: float = 0.0
    spam_probability: float = 0.0
    duplicate_probability: float = 0.0
    clickbait_score: float = 0.0
    source_credibility: float = 0.0
    structural_quality: float = 0.0


@dataclass
class QualityAssessment:
    """Complete quality assessment result."""
    quality_level: QualityLevel
    overall_score: float
    metrics: QualityMetrics
    filter_reasons: List[FilterReason]
    confidence: float
    should_filter: bool
    detailed_analysis: Dict[str, Any]


class ContentQualityFilter(BaseAnalyzer):
    """
    Advanced content quality filter for AI/LLM news articles.
    
    Detects and filters:
    - Spam content and promotional material
    - Low-quality articles with poor writing
    - Duplicate or near-duplicate content
    - Irrelevant content outside AI/LLM domain
    - Clickbait and sensationalized content
    - Articles with insufficient substantive content
    """
    
    def __init__(self, strict_mode: bool = False):
        super().__init__("ContentQualityFilter", "1.0.0")
        
        self.strict_mode = strict_mode
        
        # Spam indicators
        self.spam_patterns = {
            'promotional': [
                r'\b(buy now|click here|limited time|act fast|don\'t miss)\b',
                r'\b(special offer|discount|sale|free trial|sign up)\b',
                r'\b(earn money|make money|get rich|work from home)\b'
            ],
            'suspicious': [
                r'\b(you won\'t believe|doctors hate|one weird trick)\b',
                r'\b(shocking|amazing|incredible|unbelievable)\b',
                r'\b(secret|hidden|revealed|exposed)\b'
            ],
            'low_effort': [
                r'\b(lol|omg|wtf|tbh|imo)\b',
                r'^(this|that|it|he|she)\s',  # Starts with pronouns
                r'\b(stuff|things|whatever|basically)\b'
            ]
        }
        
        # Quality indicators
        self.quality_indicators = {
            'positive': {
                'research', 'study', 'analysis', 'findings', 'methodology',
                'experiment', 'data', 'results', 'conclusion', 'evidence',
                'technical', 'detailed', 'comprehensive', 'peer-reviewed',
                'published', 'journal', 'conference', 'proceedings'
            },
            'negative': {
                'clickbait', 'shocking', 'unbelievable', 'secret', 'trick',
                'hack', 'amazing', 'incredible', 'mind-blowing', 'viral',
                'you won\'t believe', 'doctors hate', 'one weird'
            }
        }
        
        # AI/LLM relevance keywords
        self.relevance_keywords = {
            'core': {
                'artificial intelligence', 'ai', 'machine learning', 'ml',
                'deep learning', 'neural network', 'transformer', 'gpt',
                'llm', 'large language model', 'natural language processing',
                'nlp', 'computer vision', 'reinforcement learning'
            },
            'related': {
                'algorithm', 'data science', 'automation', 'robotics',
                'cognitive computing', 'intelligent system', 'smart technology',
                'predictive analytics', 'pattern recognition', 'chatbot',
                'voice assistant', 'recommendation system'
            }
        }
        
        # Clickbait patterns
        self.clickbait_patterns = [
            r'\d+\s+(reasons|ways|things|facts|secrets)',
            r'you won\'t believe',
            r'this will (change|blow|shock)',
            r'(amazing|incredible|shocking|unbelievable)\s+\w+',
            r'what happens next will',
            r'the \w+ that will change everything'
        ]
        
        # Language quality patterns
        self.poor_language_patterns = [
            r'\b\w{1,2}\b.*\b\w{1,2}\b.*\b\w{1,2}\b',  # Too many short words
            r'(.)\1{3,}',  # Repeated characters
            r'[A-Z]{5,}',  # Excessive caps
            r'[!?]{3,}',   # Excessive punctuation
        ]
        
        # Content hashes for duplicate detection
        self.content_hashes: Set[str] = set()
        self.title_hashes: Set[str] = set()
    
    def initialize(self) -> bool:
        """Initialize the quality filter."""
        try:
            # Compile regex patterns for efficiency
            self.compiled_patterns = {}
            
            for category, patterns in self.spam_patterns.items():
                self.compiled_patterns[f'spam_{category}'] = [
                    re.compile(pattern, re.IGNORECASE) for pattern in patterns
                ]
            
            self.compiled_patterns['clickbait'] = [
                re.compile(pattern, re.IGNORECASE) for pattern in self.clickbait_patterns
            ]
            
            self.compiled_patterns['poor_language'] = [
                re.compile(pattern, re.IGNORECASE) for pattern in self.poor_language_patterns
            ]
            
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize ContentQualityFilter: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.QUALITY
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Analyze content quality of an article."""
        # Calculate quality metrics
        metrics = QualityMetrics()
        filter_reasons = []
        detailed_analysis = {}
        
        # Content length assessment
        metrics.content_length_score = self._assess_content_length(article)
        if metrics.content_length_score < 0.3:
            filter_reasons.append(FilterReason.INSUFFICIENT_CONTENT)
        
        # Language quality assessment
        metrics.language_quality_score = self._assess_language_quality(article)
        if metrics.language_quality_score < 0.4:
            filter_reasons.append(FilterReason.POOR_LANGUAGE)
        
        # Relevance assessment
        metrics.relevance_score = self._assess_relevance(article)
        if metrics.relevance_score < 0.2:
            filter_reasons.append(FilterReason.IRRELEVANT_TOPIC)
        
        # Spam detection
        metrics.spam_probability = self._detect_spam(article)
        if metrics.spam_probability > 0.7:
            filter_reasons.append(FilterReason.SPAM_CONTENT)
        
        # Duplicate detection
        metrics.duplicate_probability = self._detect_duplicates(article)
        if metrics.duplicate_probability > 0.8:
            filter_reasons.append(FilterReason.DUPLICATE_CONTENT)
        
        # Clickbait detection
        metrics.clickbait_score = self._detect_clickbait(article)
        if metrics.clickbait_score > 0.7:
            filter_reasons.append(FilterReason.CLICKBAIT)
        
        # Source credibility (simplified)
        metrics.source_credibility = self._assess_source_credibility(article)
        
        # Structural quality
        metrics.structural_quality = self._assess_structural_quality(article)
        
        # Calculate overall quality score
        overall_score = self._calculate_overall_score(metrics)
        
        # Determine quality level
        quality_level = self._determine_quality_level(overall_score, filter_reasons)
        
        # Determine if content should be filtered
        should_filter = self._should_filter_content(quality_level, overall_score, filter_reasons)
        
        # Calculate confidence
        confidence = self._calculate_confidence(metrics, overall_score)
        
        # Create detailed analysis
        detailed_analysis = {
            'content_length': len(article.content or ''),
            'title_length': len(article.title or ''),
            'has_author': bool(article.author),
            'has_timestamp': bool(article.published_at),
            'word_count': len((article.content or '').split()),
            'sentence_count': len(re.split(r'[.!?]+', article.content or '')),
            'spam_indicators_found': self._count_spam_indicators(article),
            'quality_indicators_found': self._count_quality_indicators(article)
        }
        
        # Create assessment
        assessment = QualityAssessment(
            quality_level=quality_level,
            overall_score=overall_score,
            metrics=metrics,
            filter_reasons=filter_reasons,
            confidence=confidence,
            should_filter=should_filter,
            detailed_analysis=detailed_analysis
        )
        
        # Create analysis result
        result_data = {
            'quality_level': quality_level.value,
            'overall_score': overall_score,
            'should_filter': should_filter,
            'filter_reasons': [reason.value for reason in filter_reasons],
            'metrics': {
                'content_length_score': metrics.content_length_score,
                'language_quality_score': metrics.language_quality_score,
                'relevance_score': metrics.relevance_score,
                'spam_probability': metrics.spam_probability,
                'duplicate_probability': metrics.duplicate_probability,
                'clickbait_score': metrics.clickbait_score,
                'source_credibility': metrics.source_credibility,
                'structural_quality': metrics.structural_quality
            },
            'detailed_analysis': detailed_analysis
        }
        
        # Determine confidence level
        if confidence >= 0.8:
            confidence_level = ConfidenceLevel.VERY_HIGH
        elif confidence >= 0.6:
            confidence_level = ConfidenceLevel.HIGH
        elif confidence >= 0.4:
            confidence_level = ConfidenceLevel.MEDIUM
        else:
            confidence_level = ConfidenceLevel.LOW

        return AnalysisResult(
            analysis_type=AnalysisType.QUALITY,
            confidence=confidence,
            confidence_level=confidence_level,
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'analysis_timestamp': datetime.now().isoformat(),
                'strict_mode': self.strict_mode,
                'filter_reasons_count': len(filter_reasons)
            }
        )

    def _assess_content_length(self, article: ParsedArticle) -> float:
        """Assess content length quality."""
        content = article.content or ''
        word_count = len(content.split())

        if word_count < 50:
            return 0.1  # Too short
        elif word_count < 100:
            return 0.4  # Short but acceptable
        elif word_count < 300:
            return 0.7  # Good length
        elif word_count < 1000:
            return 0.9  # Very good length
        else:
            return 1.0  # Excellent length

    def _assess_language_quality(self, article: ParsedArticle) -> float:
        """Assess language and writing quality."""
        text = f"{article.title or ''} {article.content or ''}"

        if not text.strip():
            return 0.0

        quality_score = 0.7  # Start with neutral score

        # Check for poor language patterns
        for pattern in self.compiled_patterns.get('poor_language', []):
            matches = len(pattern.findall(text))
            quality_score -= matches * 0.1

        # Check sentence structure
        sentences = re.split(r'[.!?]+', text)
        valid_sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        if valid_sentences:
            avg_sentence_length = sum(len(s.split()) for s in valid_sentences) / len(valid_sentences)

            # Optimal sentence length is 15-25 words
            if 15 <= avg_sentence_length <= 25:
                quality_score += 0.2
            elif avg_sentence_length < 5 or avg_sentence_length > 40:
                quality_score -= 0.2

        # Check for proper capitalization
        if text and text[0].isupper():
            quality_score += 0.1

        return max(0.0, min(1.0, quality_score))

    def _assess_relevance(self, article: ParsedArticle) -> float:
        """Assess relevance to AI/LLM topics."""
        text = f"{article.title or ''} {article.content or ''}".lower()

        if not text.strip():
            return 0.0

        relevance_score = 0.0

        # Check for core AI/LLM keywords
        core_matches = sum(1 for keyword in self.relevance_keywords['core'] if keyword in text)
        related_matches = sum(1 for keyword in self.relevance_keywords['related'] if keyword in text)

        # Weight core keywords more heavily
        relevance_score = (core_matches * 0.3) + (related_matches * 0.1)

        return min(1.0, relevance_score)

    def _detect_spam(self, article: ParsedArticle) -> float:
        """Detect spam content probability."""
        text = f"{article.title or ''} {article.content or ''}".lower()

        if not text.strip():
            return 0.0

        spam_score = 0.0

        # Check for spam patterns
        for category, patterns in self.compiled_patterns.items():
            if category.startswith('spam_'):
                for pattern in patterns:
                    matches = len(pattern.findall(text))
                    if category == 'spam_promotional':
                        spam_score += matches * 0.3
                    elif category == 'spam_suspicious':
                        spam_score += matches * 0.2
                    elif category == 'spam_low_effort':
                        spam_score += matches * 0.1

        # Check for excessive repetition
        words = text.split()
        if words:
            word_freq = Counter(words)
            most_common_freq = word_freq.most_common(1)[0][1]
            if most_common_freq > len(words) * 0.1:  # Single word > 10% of content
                spam_score += 0.3

        return min(1.0, spam_score)

    def _detect_duplicates(self, article: ParsedArticle) -> float:
        """Detect duplicate content probability."""
        # Generate content hash
        content = article.content or ''
        title = article.title or ''

        if not content and not title:
            return 0.0

        # Create normalized content for hashing
        normalized_content = re.sub(r'\s+', ' ', content.lower().strip())
        normalized_title = re.sub(r'\s+', ' ', title.lower().strip())

        content_hash = hashlib.md5(normalized_content.encode()).hexdigest()
        title_hash = hashlib.md5(normalized_title.encode()).hexdigest()

        duplicate_probability = 0.0

        # Check for exact duplicates
        if content_hash in self.content_hashes:
            duplicate_probability += 0.8
        if title_hash in self.title_hashes:
            duplicate_probability += 0.6

        # Add hashes to tracking sets
        self.content_hashes.add(content_hash)
        self.title_hashes.add(title_hash)

        # Check for near-duplicates (simplified)
        if len(normalized_content) > 100:
            # Check for high similarity in first/last paragraphs
            content_start = normalized_content[:200]
            content_end = normalized_content[-200:]

            # This is a simplified check - in production, you'd use more sophisticated similarity
            for existing_hash in list(self.content_hashes)[-10:]:  # Check last 10
                if content_start in existing_hash or content_end in existing_hash:
                    duplicate_probability += 0.3
                    break

        return min(1.0, duplicate_probability)

    def _detect_clickbait(self, article: ParsedArticle) -> float:
        """Detect clickbait content."""
        title = article.title or ''

        if not title:
            return 0.0

        clickbait_score = 0.0

        # Check for clickbait patterns
        for pattern in self.compiled_patterns.get('clickbait', []):
            if pattern.search(title):
                clickbait_score += 0.3

        # Check for excessive punctuation
        if title.count('!') > 1 or title.count('?') > 1:
            clickbait_score += 0.2

        # Check for all caps words
        words = title.split()
        caps_words = sum(1 for word in words if word.isupper() and len(word) > 2)
        if caps_words > 0:
            clickbait_score += caps_words * 0.1

        # Check for numbers (often used in clickbait)
        if re.search(r'\d+', title):
            clickbait_score += 0.1

        return min(1.0, clickbait_score)

    def _assess_source_credibility(self, article: ParsedArticle) -> float:
        """Assess source credibility (simplified)."""
        if not article.url:
            return 0.5

        # Extract domain
        domain_match = re.search(r'https?://(?:www\.)?([^/]+)', article.url)
        if not domain_match:
            return 0.5

        domain = domain_match.group(1).lower()

        # Simple credibility mapping
        high_credibility = [
            'arxiv.org', 'nature.com', 'science.org', 'ieee.org',
            'openai.com', 'anthropic.com', 'deepmind.com',
            'techcrunch.com', 'wired.com', 'arstechnica.com'
        ]

        medium_credibility = [
            'theverge.com', 'venturebeat.com', 'zdnet.com',
            'cnet.com', 'engadget.com', 'gizmodo.com'
        ]

        for cred_domain in high_credibility:
            if cred_domain in domain:
                return 0.9

        for cred_domain in medium_credibility:
            if cred_domain in domain:
                return 0.7

        return 0.5  # Unknown source

    def _assess_structural_quality(self, article: ParsedArticle) -> float:
        """Assess structural quality of the article."""
        quality_score = 0.5

        # Check for title
        if article.title and len(article.title.strip()) > 10:
            quality_score += 0.2

        # Check for author
        if article.author and article.author.strip():
            quality_score += 0.1

        # Check for publication date
        if article.published_at:
            quality_score += 0.1

        # Check content structure
        content = article.content or ''
        if content:
            paragraphs = content.split('\n\n')
            if len(paragraphs) > 2:  # Multiple paragraphs
                quality_score += 0.1

        return min(1.0, quality_score)

    def _calculate_overall_score(self, metrics: QualityMetrics) -> float:
        """Calculate overall quality score from individual metrics."""
        # Weighted combination of metrics
        score = (
            metrics.content_length_score * 0.20 +
            metrics.language_quality_score * 0.25 +
            metrics.relevance_score * 0.20 +
            (1.0 - metrics.spam_probability) * 0.15 +
            (1.0 - metrics.duplicate_probability) * 0.05 +
            (1.0 - metrics.clickbait_score) * 0.05 +
            metrics.source_credibility * 0.05 +
            metrics.structural_quality * 0.05
        )

        return max(0.0, min(1.0, score))

    def _determine_quality_level(self, overall_score: float, filter_reasons: List[FilterReason]) -> QualityLevel:
        """Determine quality level based on score and filter reasons."""
        if FilterReason.SPAM_CONTENT in filter_reasons:
            return QualityLevel.SPAM
        elif FilterReason.DUPLICATE_CONTENT in filter_reasons:
            return QualityLevel.DUPLICATE
        elif overall_score >= 0.7:
            return QualityLevel.HIGH
        elif overall_score >= 0.4:
            return QualityLevel.MEDIUM
        else:
            return QualityLevel.LOW

    def _should_filter_content(self, quality_level: QualityLevel, overall_score: float,
                              filter_reasons: List[FilterReason]) -> bool:
        """Determine if content should be filtered out."""
        # Always filter spam and duplicates
        if quality_level in [QualityLevel.SPAM, QualityLevel.DUPLICATE]:
            return True

        # Filter based on mode and score
        if self.strict_mode:
            return overall_score < 0.6 or len(filter_reasons) > 1
        else:
            return overall_score < 0.3 or len(filter_reasons) > 2

    def _calculate_confidence(self, metrics: QualityMetrics, overall_score: float) -> float:
        """Calculate confidence in the quality assessment."""
        # Higher confidence when metrics are consistent
        metric_values = [
            metrics.content_length_score,
            metrics.language_quality_score,
            metrics.relevance_score,
            1.0 - metrics.spam_probability,
            1.0 - metrics.duplicate_probability,
            1.0 - metrics.clickbait_score,
            metrics.source_credibility,
            metrics.structural_quality
        ]

        # Calculate standard deviation
        mean_metric = sum(metric_values) / len(metric_values)
        variance = sum((x - mean_metric) ** 2 for x in metric_values) / len(metric_values)
        std_dev = math.sqrt(variance)

        # Lower std dev = higher confidence
        consistency_confidence = max(0.0, 1.0 - (std_dev * 2))

        # Extreme scores have higher confidence
        if overall_score > 0.8 or overall_score < 0.2:
            score_confidence = 0.9
        else:
            score_confidence = 0.6

        return (consistency_confidence * 0.6) + (score_confidence * 0.4)

    def _count_spam_indicators(self, article: ParsedArticle) -> int:
        """Count spam indicators found in the article."""
        text = f"{article.title or ''} {article.content or ''}".lower()
        count = 0

        for category, patterns in self.compiled_patterns.items():
            if category.startswith('spam_'):
                for pattern in patterns:
                    count += len(pattern.findall(text))

        return count

    def _count_quality_indicators(self, article: ParsedArticle) -> Dict[str, int]:
        """Count quality indicators found in the article."""
        text = f"{article.title or ''} {article.content or ''}".lower()

        positive_count = sum(1 for indicator in self.quality_indicators['positive'] if indicator in text)
        negative_count = sum(1 for indicator in self.quality_indicators['negative'] if indicator in text)

        return {
            'positive': positive_count,
            'negative': negative_count
        }

    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for quality."""
        if not super().can_analyze(article):
            return False

        # Need at least title or content
        return bool(article.title or article.content)
