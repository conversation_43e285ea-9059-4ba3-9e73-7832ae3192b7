"""
Sentiment analysis for AI/LLM news articles.
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum

from ..base.analyzer import BaseAnalyzer, AnalysisResult, AnalysisType
from scrapers.base.content_parser import ParsedArticle


class SentimentPolarity(Enum):
    """Sentiment polarity categories."""
    VERY_NEGATIVE = "very_negative"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    POSITIVE = "positive"
    VERY_POSITIVE = "very_positive"


class SentimentAnalyzer(BaseAnalyzer):
    """
    Sentiment analyzer for AI/LLM news content.
    
    Uses multiple approaches:
    1. Lexicon-based analysis with AI/tech-specific terms
    2. Pattern-based analysis for tech news
    3. Context-aware sentiment scoring
    """
    
    def __init__(self):
        super().__init__("SentimentAnalyzer", "1.0.0")
        
        # AI/Tech-specific sentiment lexicons
        self.positive_terms = {
            # General positive
            'breakthrough', 'innovation', 'advancement', 'progress', 'success',
            'achievement', 'improvement', 'efficient', 'effective', 'powerful',
            'revolutionary', 'groundbreaking', 'cutting-edge', 'state-of-the-art',
            
            # AI/ML specific positive
            'accurate', 'precise', 'robust', 'scalable', 'optimized', 'enhanced',
            'intelligent', 'smart', 'automated', 'streamlined', 'accelerated',
            'outperforms', 'surpasses', 'exceeds', 'superior', 'advanced',
            'promising', 'potential', 'opportunity', 'benefit', 'advantage',
            
            # Business/adoption positive
            'adoption', 'investment', 'funding', 'partnership', 'collaboration',
            'growth', 'expansion', 'launch', 'release', 'debut', 'unveil'
        }
        
        self.negative_terms = {
            # General negative
            'problem', 'issue', 'challenge', 'difficulty', 'failure', 'error',
            'bug', 'flaw', 'limitation', 'concern', 'risk', 'threat', 'danger',
            'controversy', 'criticism', 'backlash', 'scandal', 'crisis',
            
            # AI/ML specific negative
            'bias', 'hallucination', 'inaccurate', 'unreliable', 'unstable',
            'overfitting', 'underfitting', 'degradation', 'vulnerability',
            'exploit', 'attack', 'manipulation', 'misuse', 'abuse',
            
            # Ethical/social negative
            'unemployment', 'displacement', 'privacy', 'surveillance', 'control',
            'manipulation', 'deception', 'misinformation', 'deepfake',
            'regulation', 'ban', 'restriction', 'prohibition'
        }
        
        # Intensity modifiers
        self.intensifiers = {
            'very': 1.5, 'extremely': 2.0, 'highly': 1.4, 'significantly': 1.3,
            'substantially': 1.3, 'considerably': 1.2, 'remarkably': 1.4,
            'exceptionally': 1.6, 'tremendously': 1.7, 'incredibly': 1.8
        }
        
        self.diminishers = {
            'slightly': 0.7, 'somewhat': 0.8, 'rather': 0.9, 'fairly': 0.9,
            'moderately': 0.8, 'partially': 0.7, 'marginally': 0.6
        }
        
        # Negation words
        self.negations = {
            'not', 'no', 'never', 'none', 'nothing', 'neither', 'nor',
            'cannot', 'can\'t', 'won\'t', 'wouldn\'t', 'shouldn\'t',
            'isn\'t', 'aren\'t', 'wasn\'t', 'weren\'t', 'hasn\'t', 'haven\'t',
            'hadn\'t', 'doesn\'t', 'don\'t', 'didn\'t', 'without', 'lack',
            'lacking', 'fails', 'failed', 'unable', 'impossible'
        }
        
        # Context patterns for AI news
        self.positive_patterns = [
            r'breakthrough in \w+',
            r'new \w+ achieves',
            r'outperforms \w+',
            r'significant improvement',
            r'major advancement',
            r'successfully \w+',
            r'record-breaking \w+',
            r'first \w+ to achieve'
        ]
        
        self.negative_patterns = [
            r'fails to \w+',
            r'struggles with \w+',
            r'concerns about \w+',
            r'risks of \w+',
            r'problems with \w+',
            r'criticism of \w+',
            r'backlash against \w+',
            r'controversy over \w+'
        ]
    
    def initialize(self) -> bool:
        """Initialize the sentiment analyzer."""
        try:
            # Compile regex patterns for efficiency
            self.positive_pattern_regex = [re.compile(p, re.IGNORECASE) for p in self.positive_patterns]
            self.negative_pattern_regex = [re.compile(p, re.IGNORECASE) for p in self.negative_patterns]
            
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize SentimentAnalyzer: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.SENTIMENT
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Analyze sentiment of an article."""
        # Combine title and content for analysis
        text = f"{article.title or ''} {article.content or ''}"
        
        # Perform different types of sentiment analysis
        lexicon_score, lexicon_confidence = self._lexicon_based_analysis(text)
        pattern_score, pattern_confidence = self._pattern_based_analysis(text)
        context_score, context_confidence = self._context_aware_analysis(text)
        
        # Combine scores with weights
        final_score = (
            lexicon_score * 0.4 +
            pattern_score * 0.3 +
            context_score * 0.3
        )
        
        # Combine confidences
        final_confidence = (
            lexicon_confidence * 0.4 +
            pattern_confidence * 0.3 +
            context_confidence * 0.3
        )
        
        # Determine polarity
        polarity = self._score_to_polarity(final_score)
        
        # Create result data
        result_data = {
            'polarity': polarity.value,
            'score': final_score,  # -1.0 to 1.0
            'scores': {
                'lexicon': lexicon_score,
                'pattern': pattern_score,
                'context': context_score
            },
            'confidences': {
                'lexicon': lexicon_confidence,
                'pattern': pattern_confidence,
                'context': context_confidence
            },
            'text_length': len(text),
            'analysis_method': 'hybrid'
        }
        
        return AnalysisResult(
            analysis_type=AnalysisType.SENTIMENT,
            confidence=final_confidence,
            confidence_level=None,  # Will be set in __post_init__
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'text_preview': text[:200] + '...' if len(text) > 200 else text
            }
        )
    
    def _lexicon_based_analysis(self, text: str) -> Tuple[float, float]:
        """Perform lexicon-based sentiment analysis."""
        words = re.findall(r'\b\w+\b', text.lower())
        
        positive_score = 0
        negative_score = 0
        total_sentiment_words = 0
        
        i = 0
        while i < len(words):
            word = words[i]
            
            # Check for negation in previous 3 words
            negated = self._check_negation(words, i)
            
            # Check for intensity modifiers
            intensity = self._check_intensity(words, i)
            
            # Calculate sentiment
            if word in self.positive_terms:
                score = 1.0 * intensity
                if negated:
                    score = -score
                positive_score += max(0, score)
                negative_score += max(0, -score)
                total_sentiment_words += 1
            
            elif word in self.negative_terms:
                score = -1.0 * intensity
                if negated:
                    score = -score
                positive_score += max(0, score)
                negative_score += max(0, -score)
                total_sentiment_words += 1
            
            i += 1
        
        if total_sentiment_words == 0:
            return 0.0, 0.0
        
        # Calculate final score
        net_score = (positive_score - negative_score) / total_sentiment_words
        
        # Calculate confidence based on number of sentiment words
        confidence = min(1.0, total_sentiment_words / 10.0)  # Max confidence at 10+ sentiment words
        
        return net_score, confidence
    
    def _pattern_based_analysis(self, text: str) -> Tuple[float, float]:
        """Perform pattern-based sentiment analysis."""
        positive_matches = 0
        negative_matches = 0
        
        # Check positive patterns
        for pattern in self.positive_pattern_regex:
            matches = pattern.findall(text)
            positive_matches += len(matches)
        
        # Check negative patterns
        for pattern in self.negative_pattern_regex:
            matches = pattern.findall(text)
            negative_matches += len(matches)
        
        total_matches = positive_matches + negative_matches
        
        if total_matches == 0:
            return 0.0, 0.0
        
        # Calculate score
        score = (positive_matches - negative_matches) / total_matches
        
        # Calculate confidence
        confidence = min(1.0, total_matches / 5.0)  # Max confidence at 5+ pattern matches
        
        return score, confidence
    
    def _context_aware_analysis(self, text: str) -> Tuple[float, float]:
        """Perform context-aware sentiment analysis."""
        sentences = re.split(r'[.!?]+', text)
        
        sentence_scores = []
        
        for sentence in sentences:
            if len(sentence.strip()) < 10:  # Skip very short sentences
                continue
            
            # Simple sentence-level analysis
            words = re.findall(r'\b\w+\b', sentence.lower())
            
            pos_count = sum(1 for word in words if word in self.positive_terms)
            neg_count = sum(1 for word in words if word in self.negative_terms)
            
            if pos_count + neg_count > 0:
                sentence_score = (pos_count - neg_count) / (pos_count + neg_count)
                sentence_scores.append(sentence_score)
        
        if not sentence_scores:
            return 0.0, 0.0
        
        # Average sentence scores
        avg_score = sum(sentence_scores) / len(sentence_scores)
        
        # Confidence based on number of analyzed sentences
        confidence = min(1.0, len(sentence_scores) / 5.0)
        
        return avg_score, confidence
    
    def _check_negation(self, words: List[str], index: int) -> bool:
        """Check if word at index is negated."""
        # Look back up to 3 words for negation
        start = max(0, index - 3)
        for i in range(start, index):
            if words[i] in self.negations:
                return True
        return False
    
    def _check_intensity(self, words: List[str], index: int) -> float:
        """Check for intensity modifiers before the word."""
        if index > 0:
            prev_word = words[index - 1]
            if prev_word in self.intensifiers:
                return self.intensifiers[prev_word]
            elif prev_word in self.diminishers:
                return self.diminishers[prev_word]
        return 1.0
    
    def _score_to_polarity(self, score: float) -> SentimentPolarity:
        """Convert numerical score to polarity category."""
        if score >= 0.6:
            return SentimentPolarity.VERY_POSITIVE
        elif score >= 0.2:
            return SentimentPolarity.POSITIVE
        elif score <= -0.6:
            return SentimentPolarity.VERY_NEGATIVE
        elif score <= -0.2:
            return SentimentPolarity.NEGATIVE
        else:
            return SentimentPolarity.NEUTRAL
    
    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for sentiment."""
        if not super().can_analyze(article):
            return False
        
        # Check if article has enough text for meaningful sentiment analysis
        text = f"{article.title or ''} {article.content or ''}"
        words = re.findall(r'\b\w+\b', text.lower())
        
        return len(words) >= 20  # Minimum 20 words for sentiment analysis
