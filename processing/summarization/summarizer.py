"""
Content summarization for AI/LLM news articles.

This module provides intelligent summarization using both extractive and 
abstractive techniques optimized for AI/LLM news content.
"""

import re
import math
import numpy as np
from typing import Dict, List, Any, Set, Tuple, Optional
from collections import defaultdict, Counter
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from ..base.analyzer import BaseAnalyzer, AnalysisResult, AnalysisType, ConfidenceLevel, ConfidenceLevel
from scrapers.base.content_parser import ParsedArticle


class SummaryType(Enum):
    """Types of summaries that can be generated."""
    EXTRACTIVE = "extractive"
    ABSTRACTIVE = "abstractive"
    HYBRID = "hybrid"
    BULLET_POINTS = "bullet_points"
    HEADLINE = "headline"


class SummaryLength(Enum):
    """Summary length options."""
    SHORT = "short"      # 1-2 sentences
    MEDIUM = "medium"    # 3-5 sentences
    LONG = "long"        # 6-10 sentences


@dataclass
class SentenceScore:
    """Scoring information for a sentence."""
    sentence: str
    position: int
    length: int
    keyword_score: float
    position_score: float
    similarity_score: float
    overall_score: float


@dataclass
class SummaryResult:
    """Complete summarization result."""
    summary_text: str
    summary_type: SummaryType
    summary_length: SummaryLength
    key_points: List[str]
    confidence: float
    compression_ratio: float
    original_length: int
    summary_length_chars: int
    metadata: Dict[str, Any]


class ContentSummarizer(BaseAnalyzer):
    """
    Advanced content summarizer for AI/LLM news articles.
    
    Provides multiple summarization approaches:
    - Extractive: Select most important sentences from original text
    - Abstractive: Generate new sentences that capture key information
    - Hybrid: Combine extractive and abstractive approaches
    - Bullet points: Extract key facts and findings
    - Headlines: Generate concise titles
    """
    
    def __init__(self, default_length: SummaryLength = SummaryLength.MEDIUM):
        super().__init__("ContentSummarizer", "1.0.0")
        
        self.default_length = default_length
        
        # AI/LLM specific keywords for importance scoring
        self.importance_keywords = {
            'high': {
                'breakthrough', 'discovery', 'innovation', 'revolutionary', 'first',
                'new', 'novel', 'significant', 'major', 'important', 'critical',
                'artificial intelligence', 'machine learning', 'deep learning',
                'neural network', 'transformer', 'gpt', 'llm', 'chatgpt'
            },
            'medium': {
                'research', 'study', 'analysis', 'findings', 'results', 'data',
                'experiment', 'methodology', 'approach', 'technique', 'algorithm',
                'model', 'system', 'technology', 'development', 'improvement'
            },
            'low': {
                'said', 'according', 'reported', 'mentioned', 'noted', 'stated',
                'explained', 'described', 'discussed', 'talked', 'spoke'
            }
        }
        
        # Sentence patterns that indicate key information
        self.key_patterns = [
            r'researchers? (?:found|discovered|developed|created)',
            r'study (?:shows|reveals|demonstrates|finds)',
            r'results? (?:show|indicate|suggest|reveal)',
            r'(?:new|novel|first) (?:method|approach|technique|system)',
            r'breakthrough in',
            r'significant (?:improvement|advance|progress)',
            r'(?:can|could|will) (?:help|enable|allow|improve)'
        ]
        
        # Stop words for sentence filtering
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you',
            'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
    
    def initialize(self) -> bool:
        """Initialize the summarizer."""
        try:
            # Compile regex patterns for efficiency
            self.compiled_patterns = [
                re.compile(pattern, re.IGNORECASE) for pattern in self.key_patterns
            ]
            
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize ContentSummarizer: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.SUMMARY
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Analyze and summarize an article."""
        # Generate different types of summaries
        extractive_summary = self.generate_extractive_summary(article, self.default_length)
        bullet_points = self.generate_bullet_points(article)
        headline = self.generate_headline(article)
        
        # Use extractive as primary summary
        primary_summary = extractive_summary
        
        # Calculate compression ratio
        original_length = len(article.content or '')
        summary_length = len(primary_summary.summary_text)
        compression_ratio = summary_length / max(1, original_length)
        
        # Create result data
        result_data = {
            'primary_summary': primary_summary.summary_text,
            'summary_type': primary_summary.summary_type.value,
            'summary_length': primary_summary.summary_length.value,
            'bullet_points': bullet_points,
            'generated_headline': headline,
            'compression_ratio': compression_ratio,
            'original_length': original_length,
            'summary_length': summary_length,
            'key_points_count': len(bullet_points)
        }
        
        # Determine confidence level
        confidence = primary_summary.confidence
        if confidence >= 0.8:
            confidence_level = ConfidenceLevel.VERY_HIGH
        elif confidence >= 0.6:
            confidence_level = ConfidenceLevel.HIGH
        elif confidence >= 0.4:
            confidence_level = ConfidenceLevel.MEDIUM
        else:
            confidence_level = ConfidenceLevel.LOW

        return AnalysisResult(
            analysis_type=AnalysisType.SUMMARY,
            confidence=confidence,
            confidence_level=confidence_level,
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'analysis_timestamp': datetime.now().isoformat(),
                'summarization_method': 'extractive',
                'target_length': self.default_length.value
            }
        )
    
    def generate_extractive_summary(self, article: ParsedArticle, 
                                   length: SummaryLength = None) -> SummaryResult:
        """Generate extractive summary by selecting key sentences."""
        if length is None:
            length = self.default_length
        
        content = article.content or ''
        if not content.strip():
            return SummaryResult(
                summary_text="No content available for summarization.",
                summary_type=SummaryType.EXTRACTIVE,
                summary_length=length,
                key_points=[],
                confidence=0.0,
                compression_ratio=0.0,
                original_length=0,
                summary_length_chars=0,
                metadata={}
            )
        
        # Split into sentences
        sentences = self._split_into_sentences(content)
        
        if not sentences:
            return SummaryResult(
                summary_text="Unable to extract sentences for summarization.",
                summary_type=SummaryType.EXTRACTIVE,
                summary_length=length,
                key_points=[],
                confidence=0.0,
                compression_ratio=0.0,
                original_length=len(content),
                summary_length_chars=0,
                metadata={}
            )
        
        # Score sentences
        sentence_scores = self._score_sentences(sentences, article.title or '')
        
        # Select top sentences based on length requirement
        target_sentence_count = self._get_target_sentence_count(length)
        selected_sentences = self._select_top_sentences(sentence_scores, target_sentence_count)
        
        # Create summary text
        summary_text = ' '.join(selected_sentences)
        
        # Extract key points
        key_points = self._extract_key_points(selected_sentences)
        
        # Calculate confidence
        confidence = self._calculate_summary_confidence(sentence_scores, selected_sentences)
        
        return SummaryResult(
            summary_text=summary_text,
            summary_type=SummaryType.EXTRACTIVE,
            summary_length=length,
            key_points=key_points,
            confidence=confidence,
            compression_ratio=len(summary_text) / len(content),
            original_length=len(content),
            summary_length_chars=len(summary_text),
            metadata={
                'sentences_analyzed': len(sentences),
                'sentences_selected': len(selected_sentences),
                'avg_sentence_score': np.mean([s.overall_score for s in sentence_scores])
            }
        )
    
    def generate_bullet_points(self, article: ParsedArticle, max_points: int = 5) -> List[str]:
        """Generate bullet points highlighting key information."""
        content = article.content or ''
        if not content.strip():
            return []
        
        sentences = self._split_into_sentences(content)
        if not sentences:
            return []
        
        # Score sentences for key information
        sentence_scores = self._score_sentences(sentences, article.title or '')
        
        # Filter for sentences with high keyword scores or pattern matches
        key_sentences = []
        for score_obj in sentence_scores:
            if (score_obj.keyword_score > 0.3 or 
                any(pattern.search(score_obj.sentence) for pattern in self.compiled_patterns)):
                key_sentences.append(score_obj.sentence)
        
        # Sort by overall score and take top points
        key_sentences = sorted(key_sentences, 
                              key=lambda s: next(sc.overall_score for sc in sentence_scores 
                                                if sc.sentence == s), 
                              reverse=True)
        
        # Clean and format bullet points
        bullet_points = []
        for sentence in key_sentences[:max_points]:
            # Clean up sentence
            clean_sentence = re.sub(r'\s+', ' ', sentence.strip())
            if len(clean_sentence) > 20 and clean_sentence not in bullet_points:
                bullet_points.append(clean_sentence)
        
        return bullet_points
    
    def generate_headline(self, article: ParsedArticle) -> str:
        """Generate a concise headline for the article."""
        # Use existing title if good
        if article.title and len(article.title.split()) <= 12:
            return article.title
        
        content = article.content or ''
        if not content.strip():
            return "AI/LLM News Article"
        
        # Extract first sentence as potential headline
        sentences = self._split_into_sentences(content)
        if not sentences:
            return "AI/LLM News Article"
        
        # Score sentences for headline potential
        sentence_scores = self._score_sentences(sentences, '')
        
        # Find best headline candidate
        best_sentence = max(sentence_scores, key=lambda s: s.keyword_score + s.position_score)
        
        # Truncate if too long
        headline = best_sentence.sentence
        words = headline.split()
        if len(words) > 12:
            headline = ' '.join(words[:12]) + '...'
        
        return headline

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        # Simple sentence splitting (could be enhanced with NLTK)
        sentences = re.split(r'[.!?]+', text)

        # Clean and filter sentences
        clean_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20 and len(sentence.split()) >= 5:  # Minimum viable sentence
                clean_sentences.append(sentence)

        return clean_sentences

    def _score_sentences(self, sentences: List[str], title: str) -> List[SentenceScore]:
        """Score sentences for importance."""
        scored_sentences = []

        for i, sentence in enumerate(sentences):
            # Calculate individual scores
            keyword_score = self._calculate_keyword_score(sentence)
            position_score = self._calculate_position_score(i, len(sentences))
            similarity_score = self._calculate_title_similarity(sentence, title)

            # Calculate overall score
            overall_score = (
                keyword_score * 0.5 +
                position_score * 0.3 +
                similarity_score * 0.2
            )

            scored_sentences.append(SentenceScore(
                sentence=sentence,
                position=i,
                length=len(sentence),
                keyword_score=keyword_score,
                position_score=position_score,
                similarity_score=similarity_score,
                overall_score=overall_score
            ))

        return scored_sentences

    def _calculate_keyword_score(self, sentence: str) -> float:
        """Calculate keyword importance score for a sentence."""
        sentence_lower = sentence.lower()
        score = 0.0

        # Check for high importance keywords
        for keyword in self.importance_keywords['high']:
            if keyword in sentence_lower:
                score += 0.3

        # Check for medium importance keywords
        for keyword in self.importance_keywords['medium']:
            if keyword in sentence_lower:
                score += 0.2

        # Penalty for low importance keywords
        for keyword in self.importance_keywords['low']:
            if keyword in sentence_lower:
                score -= 0.1

        # Check for key patterns
        for pattern in self.compiled_patterns:
            if pattern.search(sentence):
                score += 0.4

        # Check for numbers (often indicate specific findings)
        if re.search(r'\d+', sentence):
            score += 0.1

        return max(0.0, min(1.0, score))

    def _calculate_position_score(self, position: int, total_sentences: int) -> float:
        """Calculate position-based importance score."""
        if total_sentences <= 1:
            return 1.0

        # First and last sentences are often more important
        if position == 0:
            return 1.0  # First sentence
        elif position == total_sentences - 1:
            return 0.8  # Last sentence
        elif position < total_sentences * 0.2:
            return 0.9  # Early sentences
        elif position > total_sentences * 0.8:
            return 0.7  # Late sentences
        else:
            return 0.5  # Middle sentences

    def _calculate_title_similarity(self, sentence: str, title: str) -> float:
        """Calculate similarity between sentence and title."""
        if not title:
            return 0.0

        # Simple word overlap similarity
        sentence_words = set(re.findall(r'\b\w+\b', sentence.lower()))
        title_words = set(re.findall(r'\b\w+\b', title.lower()))

        # Remove stop words
        sentence_words -= self.stop_words
        title_words -= self.stop_words

        if not title_words:
            return 0.0

        overlap = len(sentence_words & title_words)
        similarity = overlap / len(title_words)

        return min(1.0, similarity)

    def _get_target_sentence_count(self, length: SummaryLength) -> int:
        """Get target number of sentences for summary length."""
        if length == SummaryLength.SHORT:
            return 2
        elif length == SummaryLength.MEDIUM:
            return 4
        else:  # LONG
            return 7

    def _select_top_sentences(self, sentence_scores: List[SentenceScore],
                             target_count: int) -> List[str]:
        """Select top sentences maintaining original order."""
        # Sort by score and take top sentences
        top_scored = sorted(sentence_scores, key=lambda s: s.overall_score, reverse=True)
        selected_scores = top_scored[:target_count]

        # Sort selected sentences by original position to maintain flow
        selected_scores.sort(key=lambda s: s.position)

        return [s.sentence for s in selected_scores]

    def _extract_key_points(self, sentences: List[str]) -> List[str]:
        """Extract key points from selected sentences."""
        key_points = []

        for sentence in sentences:
            # Look for sentences with specific patterns that indicate key information
            for pattern in self.compiled_patterns:
                if pattern.search(sentence):
                    # Extract the key part of the sentence
                    key_point = self._extract_key_phrase(sentence)
                    if key_point and key_point not in key_points:
                        key_points.append(key_point)
                    break

        return key_points[:5]  # Limit to 5 key points

    def _extract_key_phrase(self, sentence: str) -> str:
        """Extract key phrase from a sentence."""
        # Simple extraction - take first clause or up to first comma
        parts = sentence.split(',')
        key_phrase = parts[0].strip()

        # Ensure it's substantial
        if len(key_phrase.split()) >= 3:
            return key_phrase
        elif len(parts) > 1 and len(parts[1].strip().split()) >= 3:
            return parts[1].strip()
        else:
            return sentence[:100] + '...' if len(sentence) > 100 else sentence

    def _calculate_summary_confidence(self, sentence_scores: List[SentenceScore],
                                    selected_sentences: List[str]) -> float:
        """Calculate confidence in the summary quality."""
        if not sentence_scores or not selected_sentences:
            return 0.0

        # Get scores of selected sentences
        selected_scores = []
        for sentence in selected_sentences:
            for score_obj in sentence_scores:
                if score_obj.sentence == sentence:
                    selected_scores.append(score_obj.overall_score)
                    break

        if not selected_scores:
            return 0.0

        # Confidence based on average score of selected sentences
        avg_score = sum(selected_scores) / len(selected_scores)

        # Bonus for having high-scoring sentences
        high_score_count = sum(1 for score in selected_scores if score > 0.7)
        high_score_bonus = (high_score_count / len(selected_scores)) * 0.2

        confidence = min(1.0, avg_score + high_score_bonus)

        return confidence

    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for summarization."""
        if not super().can_analyze(article):
            return False

        # Need substantial content for summarization
        content = article.content or ''
        return len(content.split()) >= 50  # Minimum 50 words
