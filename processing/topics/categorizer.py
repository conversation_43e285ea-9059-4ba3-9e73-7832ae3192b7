"""
Topic categorization for AI/LLM news articles.
"""

import re
from typing import Dict, List, Any, Set, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass

from ..base.analyzer import BaseAnalyzer, AnalysisResult, AnalysisType, ConfidenceLevel
from scrapers.base.content_parser import ParsedArticle


@dataclass
class TopicCategory:
    """Represents a topic category with keywords and weights."""
    name: str
    keywords: Set[str]
    weight_multiplier: float = 1.0
    description: str = ""


class TopicCategorizer(BaseAnalyzer):
    """
    Advanced topic categorizer for AI/LLM news content.
    
    Uses hierarchical categorization with weighted keyword matching,
    context analysis, and semantic similarity.
    """
    
    def __init__(self):
        super().__init__("TopicCategorizer", "1.0.0")
        
        # Define AI/ML topic categories with hierarchical structure
        self.categories = {
            # Core AI/ML Technologies
            'machine_learning': TopicCategory(
                name="Machine Learning",
                keywords={
                    'machine learning', 'ml', 'supervised learning', 'unsupervised learning',
                    'reinforcement learning', 'deep learning', 'neural network', 'algorithm',
                    'model training', 'feature engineering', 'data science', 'predictive analytics',
                    'classification', 'regression', 'clustering', 'dimensionality reduction'
                },
                weight_multiplier=1.2,
                description="General machine learning concepts and techniques"
            ),
            
            'deep_learning': TopicCategory(
                name="Deep Learning",
                keywords={
                    'deep learning', 'neural network', 'cnn', 'rnn', 'lstm', 'gru', 'transformer',
                    'attention mechanism', 'backpropagation', 'gradient descent', 'activation function',
                    'convolutional', 'recurrent', 'feedforward', 'autoencoder', 'gan', 'vae',
                    'pytorch', 'tensorflow', 'keras', 'deep neural network'
                },
                weight_multiplier=1.3,
                description="Deep learning architectures and frameworks"
            ),
            
            'natural_language_processing': TopicCategory(
                name="Natural Language Processing",
                keywords={
                    'nlp', 'natural language processing', 'text analysis', 'sentiment analysis',
                    'language model', 'tokenization', 'parsing', 'named entity recognition',
                    'machine translation', 'text generation', 'text classification', 'chatbot',
                    'dialogue system', 'speech recognition', 'text-to-speech', 'language understanding'
                },
                weight_multiplier=1.4,
                description="Natural language processing and understanding"
            ),
            
            'large_language_models': TopicCategory(
                name="Large Language Models",
                keywords={
                    'llm', 'large language model', 'gpt', 'bert', 'transformer', 'chatgpt',
                    'openai', 'anthropic', 'claude', 'palm', 'lamda', 'bard', 'generative ai',
                    'foundation model', 'pre-trained model', 'fine-tuning', 'prompt engineering',
                    'in-context learning', 'few-shot learning', 'zero-shot learning'
                },
                weight_multiplier=1.5,
                description="Large language models and generative AI"
            ),
            
            'computer_vision': TopicCategory(
                name="Computer Vision",
                keywords={
                    'computer vision', 'image recognition', 'object detection', 'image classification',
                    'facial recognition', 'optical character recognition', 'ocr', 'image processing',
                    'video analysis', 'medical imaging', 'autonomous vehicles', 'surveillance',
                    'augmented reality', 'virtual reality', 'image segmentation', 'feature extraction'
                },
                weight_multiplier=1.3,
                description="Computer vision and image processing"
            ),
            
            # AI Applications
            'autonomous_systems': TopicCategory(
                name="Autonomous Systems",
                keywords={
                    'autonomous', 'self-driving', 'robotics', 'drone', 'autonomous vehicle',
                    'robot', 'automation', 'autonomous navigation', 'path planning',
                    'sensor fusion', 'lidar', 'radar', 'autonomous agent'
                },
                weight_multiplier=1.2,
                description="Autonomous systems and robotics"
            ),
            
            'ai_ethics': TopicCategory(
                name="AI Ethics & Safety",
                keywords={
                    'ai ethics', 'ai safety', 'bias', 'fairness', 'transparency', 'explainable ai',
                    'responsible ai', 'ai governance', 'algorithmic bias', 'ai regulation',
                    'privacy', 'data protection', 'ai alignment', 'ai risk', 'ai policy'
                },
                weight_multiplier=1.1,
                description="AI ethics, safety, and governance"
            ),
            
            'ai_business': TopicCategory(
                name="AI in Business",
                keywords={
                    'ai adoption', 'digital transformation', 'ai strategy', 'ai investment',
                    'ai startup', 'ai company', 'ai market', 'ai revenue', 'ai implementation',
                    'enterprise ai', 'ai consulting', 'ai solutions', 'ai platform'
                },
                weight_multiplier=1.0,
                description="AI business applications and market trends"
            ),
            
            'ai_research': TopicCategory(
                name="AI Research",
                keywords={
                    'ai research', 'machine learning research', 'ai paper', 'research paper',
                    'ai conference', 'neurips', 'icml', 'iclr', 'aaai', 'ai breakthrough',
                    'research finding', 'academic research', 'ai lab', 'research institute'
                },
                weight_multiplier=1.2,
                description="AI research and academic developments"
            ),
            
            # Emerging Technologies
            'quantum_ai': TopicCategory(
                name="Quantum AI",
                keywords={
                    'quantum computing', 'quantum machine learning', 'quantum ai', 'quantum algorithm',
                    'quantum neural network', 'quantum advantage', 'quantum supremacy',
                    'quantum computer', 'qml', 'quantum information'
                },
                weight_multiplier=1.1,
                description="Quantum computing and AI intersection"
            ),
            
            'edge_ai': TopicCategory(
                name="Edge AI",
                keywords={
                    'edge ai', 'edge computing', 'mobile ai', 'iot ai', 'embedded ai',
                    'on-device ai', 'federated learning', 'edge inference', 'ai chip',
                    'neural processing unit', 'npu', 'ai accelerator'
                },
                weight_multiplier=1.1,
                description="AI at the edge and mobile devices"
            ),

            'multimodal_ai': TopicCategory(
                name="Multimodal AI",
                keywords={
                    'multimodal', 'vision-language', 'text-to-image', 'image-to-text',
                    'video understanding', 'audio-visual', 'cross-modal', 'dall-e',
                    'midjourney', 'stable diffusion', 'clip', 'flamingo', 'gpt-4v'
                },
                weight_multiplier=1.2,
                description="Multimodal AI systems combining text, image, audio, and video"
            )
        }
        
        # Create keyword to category mapping for efficient lookup
        self.keyword_to_categories = defaultdict(list)
        self._build_keyword_mapping()
        
        # Compile regex patterns for better matching
        self.category_patterns = {}
        self._compile_patterns()
    
    def initialize(self) -> bool:
        """Initialize the topic categorizer."""
        try:
            # Ensure all mappings are built
            self._build_keyword_mapping()
            self._compile_patterns()
            
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize TopicCategorizer: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.TOPIC
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Analyze topics in an article."""
        # Combine title and content for analysis
        text = f"{article.title or ''} {article.content or ''}"
        
        # Perform different types of topic analysis
        keyword_scores = self._keyword_based_analysis(text)
        pattern_scores = self._pattern_based_analysis(text)
        context_scores = self._context_based_analysis(text)
        
        # Combine scores with weights
        final_scores = {}
        for category in self.categories.keys():
            final_scores[category] = (
                keyword_scores.get(category, 0) * 0.5 +
                pattern_scores.get(category, 0) * 0.3 +
                context_scores.get(category, 0) * 0.2
            )
        
        # Find top categories
        sorted_categories = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Calculate confidence based on top score and score distribution
        top_score = sorted_categories[0][1] if sorted_categories else 0
        confidence = min(1.0, top_score * 2)  # Scale confidence
        
        # Get primary and secondary categories
        primary_categories = [cat for cat, score in sorted_categories if score > 0.3][:3]
        secondary_categories = [cat for cat, score in sorted_categories if 0.1 < score <= 0.3][:5]
        
        # Create result data
        result_data = {
            'primary_categories': primary_categories,
            'secondary_categories': secondary_categories,
            'all_scores': dict(sorted_categories),
            'top_score': top_score,
            'category_count': len([s for s in final_scores.values() if s > 0.1]),
            'analysis_method': 'hybrid_categorization'
        }
        
        return AnalysisResult(
            analysis_type=AnalysisType.TOPIC,
            confidence=confidence,
            confidence_level=ConfidenceLevel.LOW,  # Will be updated in __post_init__
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'text_length': len(text),
                'categories_detected': len(primary_categories) + len(secondary_categories)
            }
        )
    
    def _build_keyword_mapping(self):
        """Build mapping from keywords to categories."""
        self.keyword_to_categories.clear()
        
        for category_id, category in self.categories.items():
            for keyword in category.keywords:
                self.keyword_to_categories[keyword.lower()].append(category_id)
    
    def _compile_patterns(self):
        """Compile regex patterns for each category."""
        self.category_patterns.clear()
        
        for category_id, category in self.categories.items():
            # Create patterns for multi-word keywords
            patterns = []
            for keyword in category.keywords:
                if ' ' in keyword:
                    # Multi-word keyword - create exact phrase pattern
                    pattern = r'\b' + re.escape(keyword) + r'\b'
                    patterns.append(pattern)
            
            if patterns:
                self.category_patterns[category_id] = re.compile(
                    '|'.join(patterns), re.IGNORECASE
                )
    
    def _keyword_based_analysis(self, text: str) -> Dict[str, float]:
        """Perform keyword-based topic analysis."""
        words = re.findall(r'\b\w+\b', text.lower())
        word_count = Counter(words)
        
        category_scores = defaultdict(float)
        total_words = len(words)
        
        if total_words == 0:
            return {}
        
        # Single word matching
        for word, count in word_count.items():
            if word in self.keyword_to_categories:
                weight = count / total_words
                for category_id in self.keyword_to_categories[word]:
                    category = self.categories[category_id]
                    category_scores[category_id] += weight * category.weight_multiplier
        
        # Multi-word phrase matching
        text_lower = text.lower()
        for category_id, category in self.categories.items():
            for keyword in category.keywords:
                if ' ' in keyword and keyword in text_lower:
                    # Count occurrences of multi-word phrases
                    count = text_lower.count(keyword)
                    weight = count / (total_words / len(keyword.split()))
                    category_scores[category_id] += weight * category.weight_multiplier
        
        return dict(category_scores)
    
    def _pattern_based_analysis(self, text: str) -> Dict[str, float]:
        """Perform pattern-based topic analysis."""
        category_scores = defaultdict(float)
        
        for category_id, pattern in self.category_patterns.items():
            matches = pattern.findall(text)
            if matches:
                # Score based on number of matches and category weight
                score = len(matches) * self.categories[category_id].weight_multiplier * 0.1
                category_scores[category_id] = min(1.0, score)  # Cap at 1.0
        
        return dict(category_scores)
    
    def _context_based_analysis(self, text: str) -> Dict[str, float]:
        """Perform context-based topic analysis."""
        sentences = re.split(r'[.!?]+', text)
        category_scores = defaultdict(float)
        
        for sentence in sentences:
            if len(sentence.strip()) < 20:  # Skip very short sentences
                continue
            
            sentence_lower = sentence.lower()
            sentence_categories = set()
            
            # Find categories mentioned in this sentence
            for category_id, category in self.categories.items():
                for keyword in category.keywords:
                    if keyword in sentence_lower:
                        sentence_categories.add(category_id)
            
            # If multiple categories in same sentence, boost their scores
            if len(sentence_categories) > 1:
                for category_id in sentence_categories:
                    category_scores[category_id] += 0.1 * len(sentence_categories)
        
        return dict(category_scores)
    
    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for topics."""
        if not super().can_analyze(article):
            return False
        
        # Check if article has enough text for meaningful topic analysis
        text = f"{article.title or ''} {article.content or ''}"
        words = re.findall(r'\b\w+\b', text.lower())
        
        return len(words) >= 30  # Minimum 30 words for topic analysis
    
    def get_category_info(self, category_id: str) -> Dict[str, Any]:
        """Get information about a specific category."""
        if category_id not in self.categories:
            return {}
        
        category = self.categories[category_id]
        return {
            'name': category.name,
            'description': category.description,
            'keyword_count': len(category.keywords),
            'weight_multiplier': category.weight_multiplier,
            'keywords': sorted(list(category.keywords))
        }
    
    def get_all_categories(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all categories."""
        return {
            category_id: self.get_category_info(category_id)
            for category_id in self.categories.keys()
        }
