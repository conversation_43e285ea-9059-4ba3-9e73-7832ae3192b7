"""
Trend detection for AI/LLM news articles.
"""

import re
from typing import Dict, List, Any, Set, Tuple, Optional
from collections import defaultdict, Counter
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from ..base.analyzer import Base<PERSON><PERSON>yzer, AnalysisResult, AnalysisType
from scrapers.base.content_parser import ParsedArticle


class TrendType(Enum):
    """Types of trends that can be detected."""
    EMERGING_TECHNOLOGY = "emerging_technology"
    MARKET_TREND = "market_trend"
    RESEARCH_BREAKTHROUGH = "research_breakthrough"
    INDUSTRY_ADOPTION = "industry_adoption"
    REGULATORY_CHANGE = "regulatory_change"
    COMPETITIVE_LANDSCAPE = "competitive_landscape"


class TrendStrength(Enum):
    """Strength levels for detected trends."""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


@dataclass
class TrendSignal:
    """Represents a detected trend signal."""
    trend_type: TrendType
    keywords: Set[str]
    strength: TrendStrength
    confidence: float
    context: str
    indicators: List[str] = field(default_factory=list)


class TrendDetector(BaseAnalyzer):
    """
    Advanced trend detector for AI/LLM news content.
    
    Detects emerging trends, market movements, research breakthroughs,
    and industry changes in AI/ML space.
    """
    
    def __init__(self):
        super().__init__("TrendDetector", "1.0.0")
        
        # Define trend indicators and patterns
        self.trend_indicators = {
            TrendType.EMERGING_TECHNOLOGY: {
                'keywords': {
                    'breakthrough', 'new', 'novel', 'innovative', 'cutting-edge',
                    'first-of-its-kind', 'revolutionary', 'game-changing', 'disruptive',
                    'emerging', 'next-generation', 'advanced', 'state-of-the-art'
                },
                'patterns': [
                    r'introduces? (?:a )?new \w+',
                    r'breakthrough in \w+',
                    r'first \w+ to \w+',
                    r'revolutionary \w+',
                    r'next generation \w+',
                    r'cutting-edge \w+',
                    r'emerging \w+ technology'
                ],
                'context_words': {
                    'technology', 'algorithm', 'model', 'architecture', 'framework',
                    'platform', 'system', 'approach', 'method', 'technique'
                }
            },
            
            TrendType.MARKET_TREND: {
                'keywords': {
                    'market', 'growth', 'adoption', 'investment', 'funding', 'valuation',
                    'revenue', 'sales', 'demand', 'expansion', 'scale', 'commercial',
                    'enterprise', 'business', 'industry', 'sector', 'economy'
                },
                'patterns': [
                    r'market (?:growth|expansion|adoption)',
                    r'investment in \w+',
                    r'funding for \w+',
                    r'revenue (?:growth|increase)',
                    r'demand for \w+',
                    r'adoption of \w+',
                    r'enterprise \w+ market'
                ],
                'context_words': {
                    'billion', 'million', 'percent', 'growth', 'increase', 'rise',
                    'surge', 'boom', 'expansion', 'scale', 'commercial'
                }
            },
            
            TrendType.RESEARCH_BREAKTHROUGH: {
                'keywords': {
                    'research', 'study', 'paper', 'publication', 'findings', 'discovery',
                    'breakthrough', 'advance', 'progress', 'achievement', 'result',
                    'experiment', 'analysis', 'investigation', 'academic'
                },
                'patterns': [
                    r'research (?:shows|reveals|finds)',
                    r'study (?:demonstrates|proves|shows)',
                    r'breakthrough in \w+ research',
                    r'new research on \w+',
                    r'academic study on \w+',
                    r'research paper on \w+',
                    r'scientific breakthrough'
                ],
                'context_words': {
                    'university', 'lab', 'laboratory', 'institute', 'academic',
                    'scientist', 'researcher', 'professor', 'phd', 'conference'
                }
            },
            
            TrendType.INDUSTRY_ADOPTION: {
                'keywords': {
                    'adoption', 'implementation', 'deployment', 'integration', 'rollout',
                    'launch', 'release', 'enterprise', 'corporate', 'business',
                    'company', 'organization', 'industry', 'sector'
                },
                'patterns': [
                    r'companies? (?:adopt|implement|deploy)',
                    r'enterprise adoption of \w+',
                    r'industry (?:adopts|embraces|implements)',
                    r'widespread adoption',
                    r'corporate implementation',
                    r'business integration',
                    r'industrial deployment'
                ],
                'context_words': {
                    'enterprise', 'corporate', 'business', 'industrial', 'commercial',
                    'organization', 'company', 'firm', 'sector', 'industry'
                }
            },
            
            TrendType.REGULATORY_CHANGE: {
                'keywords': {
                    'regulation', 'policy', 'law', 'legislation', 'compliance', 'governance',
                    'regulatory', 'legal', 'government', 'authority', 'agency',
                    'rule', 'guideline', 'standard', 'framework'
                },
                'patterns': [
                    r'new (?:regulation|policy|law)',
                    r'regulatory (?:framework|guidelines)',
                    r'government (?:policy|regulation)',
                    r'legal (?:framework|requirements)',
                    r'compliance (?:requirements|standards)',
                    r'regulatory (?:approval|oversight)'
                ],
                'context_words': {
                    'government', 'federal', 'state', 'national', 'international',
                    'agency', 'authority', 'commission', 'department', 'ministry'
                }
            },
            
            TrendType.COMPETITIVE_LANDSCAPE: {
                'keywords': {
                    'competition', 'competitor', 'rival', 'market share', 'leadership',
                    'dominance', 'challenge', 'threat', 'advantage', 'differentiation',
                    'partnership', 'acquisition', 'merger', 'collaboration'
                },
                'patterns': [
                    r'competes? with \w+',
                    r'market (?:leader|leadership)',
                    r'competitive (?:advantage|landscape)',
                    r'partnership (?:with|between)',
                    r'acquisition of \w+',
                    r'merger (?:with|between)',
                    r'strategic (?:partnership|alliance)'
                ],
                'context_words': {
                    'openai', 'google', 'microsoft', 'amazon', 'meta', 'anthropic',
                    'nvidia', 'intel', 'apple', 'tesla', 'startup', 'unicorn'
                }
            }
        }
        
        # Compile regex patterns for efficiency
        self.compiled_patterns = {}
        self._compile_patterns()
        
        # Trend strength thresholds
        self.strength_thresholds = {
            TrendStrength.WEAK: 0.3,
            TrendStrength.MODERATE: 0.5,
            TrendStrength.STRONG: 0.7,
            TrendStrength.VERY_STRONG: 0.9
        }
    
    def initialize(self) -> bool:
        """Initialize the trend detector."""
        try:
            self._compile_patterns()
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"Failed to initialize TrendDetector: {e}")
            return False
    
    def get_analysis_type(self) -> AnalysisType:
        """Get the analysis type."""
        return AnalysisType.TREND
    
    def analyze(self, article: ParsedArticle) -> AnalysisResult:
        """Analyze trends in an article."""
        # Combine title and content for analysis
        text = f"{article.title or ''} {article.content or ''}"
        
        # Detect trend signals
        trend_signals = self._detect_trend_signals(text)
        
        # Calculate overall trend strength and confidence
        overall_strength, overall_confidence = self._calculate_overall_metrics(trend_signals)
        
        # Identify primary trends
        primary_trends = self._identify_primary_trends(trend_signals)
        
        # Extract trend keywords and context
        trend_keywords = self._extract_trend_keywords(text, trend_signals)
        trend_context = self._extract_trend_context(text, trend_signals)
        
        # Create result data
        result_data = {
            'trend_signals': [self._signal_to_dict(signal) for signal in trend_signals],
            'primary_trends': [trend.value for trend in primary_trends],
            'overall_strength': overall_strength.value if overall_strength else None,
            'trend_keywords': list(trend_keywords),
            'trend_context': trend_context,
            'signal_count': len(trend_signals),
            'analysis_method': 'pattern_and_keyword_analysis'
        }
        
        return AnalysisResult(
            analysis_type=AnalysisType.TREND,
            confidence=overall_confidence,
            confidence_level=None,  # Will be set in __post_init__
            data=result_data,
            metadata={
                'analyzer_version': self.version,
                'text_length': len(text),
                'trends_detected': len(primary_trends)
            }
        )
    
    def _compile_patterns(self):
        """Compile regex patterns for each trend type."""
        self.compiled_patterns.clear()
        
        for trend_type, indicators in self.trend_indicators.items():
            patterns = []
            for pattern in indicators['patterns']:
                patterns.append(re.compile(pattern, re.IGNORECASE))
            self.compiled_patterns[trend_type] = patterns
    
    def _detect_trend_signals(self, text: str) -> List[TrendSignal]:
        """Detect trend signals in the text."""
        signals = []
        text_lower = text.lower()
        
        for trend_type, indicators in self.trend_indicators.items():
            # Keyword-based detection
            keyword_score = self._calculate_keyword_score(text_lower, indicators['keywords'])
            
            # Pattern-based detection
            pattern_score = self._calculate_pattern_score(text, self.compiled_patterns[trend_type])
            
            # Context-based detection
            context_score = self._calculate_context_score(text_lower, indicators['context_words'])
            
            # Combined score
            combined_score = (keyword_score * 0.4 + pattern_score * 0.4 + context_score * 0.2)
            
            if combined_score > 0.2:  # Minimum threshold for trend detection
                strength = self._score_to_strength(combined_score)
                confidence = min(1.0, combined_score * 1.5)
                
                # Extract relevant keywords and context
                relevant_keywords = self._extract_relevant_keywords(
                    text_lower, indicators['keywords']
                )
                context = self._extract_signal_context(text, trend_type)
                
                signal = TrendSignal(
                    trend_type=trend_type,
                    keywords=relevant_keywords,
                    strength=strength,
                    confidence=confidence,
                    context=context,
                    indicators=[f"keyword_score: {keyword_score:.2f}",
                              f"pattern_score: {pattern_score:.2f}",
                              f"context_score: {context_score:.2f}"]
                )
                signals.append(signal)
        
        return signals
    
    def _calculate_keyword_score(self, text: str, keywords: Set[str]) -> float:
        """Calculate keyword-based trend score."""
        words = re.findall(r'\b\w+\b', text)
        word_count = Counter(words)
        total_words = len(words)
        
        if total_words == 0:
            return 0.0
        
        keyword_hits = 0
        for keyword in keywords:
            if ' ' in keyword:
                # Multi-word keyword
                keyword_hits += text.count(keyword)
            else:
                # Single word keyword
                keyword_hits += word_count.get(keyword, 0)
        
        return min(1.0, keyword_hits / (total_words * 0.01))  # Normalize
    
    def _calculate_pattern_score(self, text: str, patterns: List[re.Pattern]) -> float:
        """Calculate pattern-based trend score."""
        total_matches = 0
        for pattern in patterns:
            matches = pattern.findall(text)
            total_matches += len(matches)
        
        return min(1.0, total_matches * 0.2)  # Each match contributes 0.2
    
    def _calculate_context_score(self, text: str, context_words: Set[str]) -> float:
        """Calculate context-based trend score."""
        words = re.findall(r'\b\w+\b', text)
        word_count = Counter(words)
        total_words = len(words)
        
        if total_words == 0:
            return 0.0
        
        context_hits = sum(word_count.get(word, 0) for word in context_words)
        return min(1.0, context_hits / (total_words * 0.02))  # Normalize
    
    def _score_to_strength(self, score: float) -> TrendStrength:
        """Convert numerical score to trend strength."""
        if score >= self.strength_thresholds[TrendStrength.VERY_STRONG]:
            return TrendStrength.VERY_STRONG
        elif score >= self.strength_thresholds[TrendStrength.STRONG]:
            return TrendStrength.STRONG
        elif score >= self.strength_thresholds[TrendStrength.MODERATE]:
            return TrendStrength.MODERATE
        else:
            return TrendStrength.WEAK
    
    def _calculate_overall_metrics(self, signals: List[TrendSignal]) -> Tuple[Optional[TrendStrength], float]:
        """Calculate overall trend strength and confidence."""
        if not signals:
            return None, 0.0
        
        # Calculate weighted average confidence
        total_confidence = sum(signal.confidence for signal in signals)
        avg_confidence = total_confidence / len(signals)
        
        # Find strongest trend
        strongest_signal = max(signals, key=lambda s: s.confidence)
        overall_strength = strongest_signal.strength
        
        return overall_strength, avg_confidence
    
    def _identify_primary_trends(self, signals: List[TrendSignal]) -> List[TrendType]:
        """Identify primary trend types from signals."""
        # Group signals by trend type and find the strongest ones
        trend_strengths = defaultdict(float)
        
        for signal in signals:
            trend_strengths[signal.trend_type] = max(
                trend_strengths[signal.trend_type],
                signal.confidence
            )
        
        # Return trends with confidence > 0.5, sorted by strength
        primary_trends = [
            trend_type for trend_type, confidence in trend_strengths.items()
            if confidence > 0.5
        ]
        
        return sorted(primary_trends, key=lambda t: trend_strengths[t], reverse=True)
    
    def _extract_trend_keywords(self, text: str, signals: List[TrendSignal]) -> Set[str]:
        """Extract trend-related keywords from the text."""
        all_keywords = set()
        for signal in signals:
            all_keywords.update(signal.keywords)
        return all_keywords
    
    def _extract_trend_context(self, text: str, signals: List[TrendSignal]) -> str:
        """Extract trend context from the text."""
        contexts = []
        for signal in signals:
            if signal.context and len(signal.context) > 20:
                contexts.append(signal.context[:200])  # Limit context length
        
        return " | ".join(contexts[:3])  # Combine top 3 contexts
    
    def _extract_relevant_keywords(self, text: str, keywords: Set[str]) -> Set[str]:
        """Extract keywords that actually appear in the text."""
        relevant = set()
        for keyword in keywords:
            if keyword in text:
                relevant.add(keyword)
        return relevant
    
    def _extract_signal_context(self, text: str, trend_type: TrendType) -> str:
        """Extract context for a specific trend signal."""
        sentences = re.split(r'[.!?]+', text)
        
        # Find sentences that contain trend-related keywords
        indicators = self.trend_indicators[trend_type]
        relevant_sentences = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            for keyword in indicators['keywords']:
                if keyword in sentence_lower:
                    relevant_sentences.append(sentence.strip())
                    break
        
        # Return the first relevant sentence as context
        return relevant_sentences[0] if relevant_sentences else ""
    
    def _signal_to_dict(self, signal: TrendSignal) -> Dict[str, Any]:
        """Convert TrendSignal to dictionary."""
        return {
            'trend_type': signal.trend_type.value,
            'keywords': list(signal.keywords),
            'strength': signal.strength.value,
            'confidence': signal.confidence,
            'context': signal.context,
            'indicators': signal.indicators
        }
    
    def can_analyze(self, article: ParsedArticle) -> bool:
        """Check if article can be analyzed for trends."""
        if not super().can_analyze(article):
            return False
        
        # Check if article has enough text for meaningful trend analysis
        text = f"{article.title or ''} {article.content or ''}"
        words = re.findall(r'\b\w+\b', text.lower())
        
        return len(words) >= 50  # Minimum 50 words for trend analysis
