# AI News Scraping Framework

A comprehensive, production-ready web scraping framework specifically designed for collecting AI/ML news and content from various sources.

## Features

### 🚀 Core Framework
- **Modular Architecture**: Extensible base classes for different scraper types
- **Rate Limiting**: Intelligent rate limiting with robots.txt compliance
- **Error Handling**: Comprehensive exception hierarchy and retry mechanisms
- **Content Parsing**: Advanced HTML parsing with AI/ML keyword detection
- **Duplicate Detection**: Multi-strategy duplicate detection (URL, content hash, similarity)

### 🛡️ Ethical Scraping
- **Robots.txt Compliance**: Automatic robots.txt checking and respect
- **Configurable Delays**: Domain-specific rate limiting
- **User Agent Rotation**: Realistic browser user agent rotation
- **Proxy Support**: Built-in proxy rotation and health checking

### 🎯 AI/ML Focus
- **Keyword Detection**: Comprehensive AI/ML keyword database
- **Content Filtering**: Automatic relevance filtering for AI content
- **Source Prioritization**: Configurable source priority and tagging

### 📊 Monitoring & Analytics
- **Statistics Tracking**: Request counts, success rates, error tracking
- **Logging**: Structured logging with configurable levels
- **Health Checks**: Proxy and source health monitoring

## Quick Start

### Basic Usage

```python
from scrapers import NewsScraper, ScraperConfig, RateLimit

# Configure a scraper
config = ScraperConfig(
    name="techcrunch",
    base_url="https://techcrunch.com",
    rate_limit=RateLimit(delay_between_requests=2.0)
)

# Scrape articles
with NewsScraper(config) as scraper:
    for article in scraper.scrape_all():
        if article:
            print(f"Found: {article.title}")
            print(f"URL: {article.url}")
            print(f"AI-related: {scraper.parser.is_ai_related(article)}")
```

### Using Configuration Manager

```python
from config.scraper_config import ScrapingConfigManager

# Initialize configuration manager
config_manager = ScrapingConfigManager()

# Get all configured sources
sources = config_manager.get_all_sources()

# Get sources by type
news_sources = config_manager.get_sources_by_type("news")
reddit_sources = config_manager.get_sources_by_type("reddit")
```

### Duplicate Detection

```python
from scrapers.utils.duplicate_detector import DuplicateDetector

detector = DuplicateDetector(similarity_threshold=0.85)

# Check for duplicates
if not detector.is_duplicate(article):
    detector.add_article(article)
    # Process unique article
```

## Architecture

### Base Components

#### `BaseScraper`
Abstract base class providing common functionality:
- HTTP session management
- Rate limiting integration
- Error handling and retries
- Statistics tracking
- Context manager support

#### `ContentParser`
Advanced HTML content parsing:
- Title, content, author, date extraction
- AI/ML keyword detection
- Content validation and filtering
- Structured data extraction

#### `RateLimiter`
Intelligent rate limiting:
- Domain-specific rate limits
- Robots.txt compliance
- Thread-safe operation
- Configurable delays

### Utility Components

#### `DuplicateDetector`
Multi-strategy duplicate detection:
- URL normalization and comparison
- Content hash comparison
- Text similarity analysis
- Configurable similarity thresholds

#### `UserAgentManager`
Realistic user agent rotation:
- Browser market share weighting
- Device type filtering
- Regular updates

#### `ProxyManager`
Proxy rotation and health checking:
- Automatic proxy testing
- Performance monitoring
- Failure tracking and recovery

### Source-Specific Scrapers

#### `NewsScraper`
Generic news website scraper:
- Sitemap and RSS feed support
- Article link discovery
- Content extraction
- URL validation

#### `RedditScraper`
Reddit-specific scraper:
- Subreddit monitoring
- Post relevance filtering
- Comment extraction
- API integration

## Configuration

### Environment Variables

```bash
# Rate limiting
SCRAPER_DEFAULT_REQUESTS_PER_SECOND=1.0
SCRAPER_DEFAULT_DELAY_BETWEEN_REQUESTS=1.0
SCRAPER_RESPECT_ROBOTS_TXT=true

# Content filtering
SCRAPER_MIN_CONTENT_LENGTH=100
SCRAPER_MAX_CONTENT_LENGTH=50000
SCRAPER_SIMILARITY_THRESHOLD=0.85

# Request settings
SCRAPER_REQUEST_TIMEOUT=30
SCRAPER_MAX_RETRIES=3
SCRAPER_VERIFY_SSL=true

# Proxy settings
SCRAPER_ENABLE_PROXY_ROTATION=false
SCRAPER_PROXY_LIST=proxy1:8080,proxy2:8080
```

### Source Configuration

```python
from scrapers.base.rate_limiter import RateLimit
from config.scraper_config import SourceConfig

# Define a custom source
custom_source = SourceConfig(
    name="my_news_site",
    base_url="https://example.com",
    scraper_type="news",
    rate_limit=RateLimit(delay_between_requests=3.0),
    priority=5,
    tags=["ai", "tech"],
    custom_selectors={
        "article_links": [".article-link", ".story-headline a"],
        "title": ["h1.title", ".article-title"],
        "content": [".article-content", ".story-body"]
    }
)
```

## Testing

### Running Tests

```bash
# Run all scraper tests
python -m pytest tests/test_scrapers.py -v

# Run integration tests
python -m pytest tests/test_integration.py -v

# Run specific test
python -m pytest tests/test_scrapers.py::TestRateLimiter -v
```

### Example Test

```python
def test_ai_keyword_detection():
    from scrapers.base.content_parser import ContentParser
    
    parser = ContentParser()
    
    article = ParsedArticle(
        title="ChatGPT and Large Language Models",
        content="Discussion about OpenAI's ChatGPT and other LLMs.",
        url="https://example.com/chatgpt"
    )
    
    assert parser.is_ai_related(article) == True
```

## Examples

See the `examples/` directory for complete usage examples:

- `basic_scraping_example.py`: Complete scraping workflow
- `config_example.py`: Configuration management
- `integration_example.py`: Database integration

## Error Handling

The framework provides comprehensive error handling:

```python
from scrapers.base.exceptions import (
    ScrapingError, RateLimitError, ContentParsingError,
    ProxyError, NetworkError
)

try:
    articles = list(scraper.scrape_all())
except RateLimitError:
    # Handle rate limiting
    pass
except ContentParsingError:
    # Handle parsing errors
    pass
except NetworkError:
    # Handle network issues
    pass
```

## Best Practices

1. **Respect Rate Limits**: Always configure appropriate delays
2. **Monitor Resources**: Track request counts and error rates
3. **Handle Failures Gracefully**: Implement proper error handling
4. **Use Duplicate Detection**: Avoid processing duplicate content
5. **Rotate User Agents**: Use realistic browser user agents
6. **Check Robots.txt**: Respect website scraping policies
7. **Monitor Performance**: Track scraping statistics

## Contributing

1. Follow the existing code structure
2. Add comprehensive tests for new features
3. Update documentation
4. Ensure ethical scraping practices
5. Test with multiple sources

## License

This project is part of the AI News Scraper application.
