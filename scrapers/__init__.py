"""
Web scraping framework for AI/LLM news collection.

This package provides a comprehensive framework for scraping AI/LLM related
news from various sources including news websites, Reddit, Twitter, and more.
"""

# Base framework components
from .base.scraper import BaseScraper, ScraperConfig
from .base.rate_limiter import RateLimiter, RateLimit
from .base.content_parser import ContentParser, ParsedArticle
from .base.exceptions import (
    ScrapingError, RateLimitError, ContentParsingError,
    ProxyError, DuplicateContentError, ConfigurationError, NetworkError
)

# Utility components
from .utils.proxy_manager import ProxyManager
from .utils.user_agent_manager import UserAgentManager
from .utils.duplicate_detector import DuplicateDetector

# Source-specific scrapers
from .sources.news_scraper import NewsScraper
from .sources.reddit_scraper import RedditScraper

__version__ = "0.1.0"
__all__ = [
    # Base framework
    "BaseScraper",
    "ScraperConfig",
    "RateLimiter",
    "RateLimit",
    "ContentParser",
    "ParsedArticle",

    # Exceptions
    "ScrapingError",
    "RateLimitError",
    "ContentParsingError",
    "ProxyError",
    "DuplicateContentError",
    "ConfigurationError",
    "NetworkError",

    # Utilities
    "ProxyManager",
    "UserAgentManager",
    "DuplicateDetector",

    # Source scrapers
    "NewsScraper",
    "RedditScraper"
]
