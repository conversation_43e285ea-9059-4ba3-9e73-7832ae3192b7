"""
Advanced Reddit comment analysis module.

This module provides sophisticated analysis of Reddit comments including:
- Comment thread structure analysis
- Sentiment analysis of comment chains
- Expert identification based on comment quality
- Discussion topic extraction
"""

import re
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timezone
from collections import defaultdict

import praw
from praw.models import Comment, MoreComments


@dataclass
class CommentAnalysis:
    """Analysis results for a Reddit comment."""
    
    comment_id: str
    author: str
    score: int
    depth: int
    word_count: int
    has_code: bool
    has_links: bool
    reply_count: int
    sentiment_indicators: Dict[str, int]
    technical_terms: List[str]
    quality_score: float


@dataclass
class ThreadAnalysis:
    """Analysis results for a comment thread."""
    
    total_comments: int
    max_depth: int
    avg_score: float
    top_contributors: List[Tuple[str, int]]  # (author, comment_count)
    discussion_topics: List[str]
    sentiment_distribution: Dict[str, int]
    technical_depth: float
    controversy_score: float


class RedditCommentAnalyzer:
    """Advanced analyzer for Reddit comments and discussion threads."""
    
    def __init__(self):
        # Technical terms commonly used in AI/ML discussions
        self.ai_ml_terms = {
            'neural', 'network', 'deep', 'learning', 'machine', 'algorithm',
            'model', 'training', 'dataset', 'feature', 'classification',
            'regression', 'clustering', 'supervised', 'unsupervised',
            'reinforcement', 'gradient', 'backpropagation', 'optimization',
            'tensorflow', 'pytorch', 'keras', 'scikit', 'pandas', 'numpy',
            'transformer', 'attention', 'bert', 'gpt', 'llm', 'nlp',
            'computer', 'vision', 'cnn', 'rnn', 'lstm', 'gan', 'vae'
        }
        
        # Sentiment indicators
        self.positive_indicators = {
            'great', 'excellent', 'amazing', 'fantastic', 'brilliant',
            'impressive', 'outstanding', 'wonderful', 'perfect', 'love',
            'awesome', 'incredible', 'remarkable', 'superb', 'magnificent'
        }
        
        self.negative_indicators = {
            'terrible', 'awful', 'horrible', 'bad', 'worst', 'hate',
            'disappointing', 'useless', 'broken', 'failed', 'wrong',
            'stupid', 'ridiculous', 'nonsense', 'garbage', 'trash'
        }
        
        self.question_indicators = {
            'how', 'what', 'why', 'when', 'where', 'which', 'who',
            'can', 'could', 'would', 'should', 'is', 'are', 'does'
        }
    
    def analyze_comment(self, comment: Comment) -> CommentAnalysis:
        """Analyze a single Reddit comment."""
        if not isinstance(comment, Comment):
            raise ValueError("Input must be a Comment object")
        
        body = comment.body.lower() if comment.body else ""
        
        # Basic metrics
        word_count = len(body.split())
        has_code = self._detect_code(comment.body)
        has_links = self._detect_links(comment.body)
        reply_count = len(comment.replies) if hasattr(comment, 'replies') else 0
        
        # Sentiment analysis
        sentiment_indicators = self._analyze_sentiment(body)
        
        # Technical term extraction
        technical_terms = self._extract_technical_terms(body)
        
        # Quality score calculation
        quality_score = self._calculate_quality_score(
            comment, word_count, has_code, has_links, technical_terms
        )
        
        return CommentAnalysis(
            comment_id=comment.id,
            author=str(comment.author) if comment.author else "deleted",
            score=comment.score,
            depth=self._get_comment_depth(comment),
            word_count=word_count,
            has_code=has_code,
            has_links=has_links,
            reply_count=reply_count,
            sentiment_indicators=sentiment_indicators,
            technical_terms=technical_terms,
            quality_score=quality_score
        )
    
    def analyze_thread(self, submission: praw.models.Submission, max_comments: int = 100) -> ThreadAnalysis:
        """Analyze an entire comment thread."""
        # Expand comment tree
        submission.comments.replace_more(limit=0)
        
        comments = []
        comment_count = 0
        
        # Collect comments up to limit
        for comment in submission.comments.list():
            if comment_count >= max_comments:
                break
            
            if isinstance(comment, Comment) and comment.body != '[deleted]':
                comments.append(self.analyze_comment(comment))
                comment_count += 1
        
        if not comments:
            return ThreadAnalysis(
                total_comments=0,
                max_depth=0,
                avg_score=0.0,
                top_contributors=[],
                discussion_topics=[],
                sentiment_distribution={},
                technical_depth=0.0,
                controversy_score=0.0
            )
        
        # Calculate thread-level metrics
        total_comments = len(comments)
        max_depth = max(c.depth for c in comments)
        avg_score = sum(c.score for c in comments) / total_comments
        
        # Top contributors
        author_counts = defaultdict(int)
        for comment in comments:
            if comment.author != "deleted":
                author_counts[comment.author] += 1
        
        top_contributors = sorted(author_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # Discussion topics (based on technical terms)
        all_technical_terms = []
        for comment in comments:
            all_technical_terms.extend(comment.technical_terms)
        
        topic_counts = defaultdict(int)
        for term in all_technical_terms:
            topic_counts[term] += 1
        
        discussion_topics = [term for term, count in 
                           sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)[:10]]
        
        # Sentiment distribution
        sentiment_distribution = {
            'positive': sum(1 for c in comments if c.sentiment_indicators['positive'] > 0),
            'negative': sum(1 for c in comments if c.sentiment_indicators['negative'] > 0),
            'neutral': sum(1 for c in comments if c.sentiment_indicators['positive'] == 0 and 
                          c.sentiment_indicators['negative'] == 0),
            'questions': sum(1 for c in comments if c.sentiment_indicators['questions'] > 0)
        }
        
        # Technical depth (average technical terms per comment)
        technical_depth = sum(len(c.technical_terms) for c in comments) / total_comments
        
        # Controversy score (based on score variance and negative sentiment)
        score_variance = self._calculate_variance([c.score for c in comments])
        negative_ratio = sentiment_distribution['negative'] / total_comments
        controversy_score = (score_variance / 100) + negative_ratio  # Normalized
        
        return ThreadAnalysis(
            total_comments=total_comments,
            max_depth=max_depth,
            avg_score=avg_score,
            top_contributors=top_contributors,
            discussion_topics=discussion_topics,
            sentiment_distribution=sentiment_distribution,
            technical_depth=technical_depth,
            controversy_score=controversy_score
        )
    
    def get_high_quality_comments(self, comments: List[CommentAnalysis], 
                                 top_n: int = 5) -> List[CommentAnalysis]:
        """Get the highest quality comments from a list."""
        return sorted(comments, key=lambda c: c.quality_score, reverse=True)[:top_n]
    
    def get_expert_contributors(self, comments: List[CommentAnalysis], 
                               min_quality: float = 0.7) -> List[str]:
        """Identify expert contributors based on comment quality."""
        author_quality = defaultdict(list)
        
        for comment in comments:
            if comment.author != "deleted":
                author_quality[comment.author].append(comment.quality_score)
        
        experts = []
        for author, scores in author_quality.items():
            if len(scores) >= 2:  # At least 2 comments
                avg_quality = sum(scores) / len(scores)
                if avg_quality >= min_quality:
                    experts.append(author)
        
        return experts
    
    def _detect_code(self, text: str) -> bool:
        """Detect if comment contains code snippets."""
        if not text:
            return False
        
        # Look for code indicators
        code_indicators = [
            '```', '`', 'def ', 'class ', 'import ', 'from ',
            'function', 'var ', 'let ', 'const ', '#!/',
            'print(', 'console.log', 'System.out'
        ]
        
        return any(indicator in text for indicator in code_indicators)
    
    def _detect_links(self, text: str) -> bool:
        """Detect if comment contains links."""
        if not text:
            return False
        
        # Simple URL detection
        url_pattern = r'https?://[^\s]+'
        return bool(re.search(url_pattern, text))
    
    def _analyze_sentiment(self, text: str) -> Dict[str, int]:
        """Analyze sentiment indicators in text."""
        words = text.split()
        
        positive_count = sum(1 for word in words if word in self.positive_indicators)
        negative_count = sum(1 for word in words if word in self.negative_indicators)
        question_count = sum(1 for word in words if word in self.question_indicators)
        
        return {
            'positive': positive_count,
            'negative': negative_count,
            'questions': question_count
        }
    
    def _extract_technical_terms(self, text: str) -> List[str]:
        """Extract technical terms from text."""
        words = text.split()
        technical_terms = []
        
        for word in words:
            # Clean word (remove punctuation)
            clean_word = re.sub(r'[^\w]', '', word.lower())
            if clean_word in self.ai_ml_terms:
                technical_terms.append(clean_word)
        
        return list(set(technical_terms))  # Remove duplicates
    
    def _calculate_quality_score(self, comment: Comment, word_count: int, 
                                has_code: bool, has_links: bool, 
                                technical_terms: List[str]) -> float:
        """Calculate a quality score for a comment."""
        score = 0.0
        
        # Base score from Reddit score (normalized)
        reddit_score = max(0, comment.score)
        score += min(reddit_score / 10, 1.0)  # Cap at 1.0
        
        # Word count factor (sweet spot around 50-200 words)
        if 20 <= word_count <= 200:
            score += 0.3
        elif word_count > 200:
            score += 0.2
        
        # Code and links bonus
        if has_code:
            score += 0.2
        if has_links:
            score += 0.1
        
        # Technical terms bonus
        score += min(len(technical_terms) * 0.1, 0.3)
        
        # Penalize very short comments
        if word_count < 5:
            score *= 0.5
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _get_comment_depth(self, comment: Comment) -> int:
        """Get the depth of a comment in the thread."""
        depth = 0
        current = comment
        
        while hasattr(current, 'parent') and current.parent:
            try:
                parent = current.parent()
                if isinstance(parent, Comment):
                    depth += 1
                    current = parent
                else:
                    break
            except:
                break
        
        return depth
    
    def _calculate_variance(self, values: List[float]) -> float:
        """Calculate variance of a list of values."""
        if not values:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance
