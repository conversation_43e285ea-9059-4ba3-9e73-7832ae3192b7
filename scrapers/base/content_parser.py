"""
Content parsing utilities for extracting structured data from HTML.
"""

import re
import hashlib
from typing import Dict, List, Optional, Any
from datetime import datetime
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass

from bs4 import BeautifulSoup, Tag
import dateutil.parser

from .exceptions import ContentParsingError


@dataclass
class ParsedArticle:
    """Structured representation of a parsed article."""
    title: str
    content: str
    url: str
    author: Optional[str] = None
    published_at: Optional[datetime] = None
    tags: List[str] = None
    summary: Optional[str] = None
    image_url: Optional[str] = None
    source_name: Optional[str] = None
    content_hash: Optional[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.content_hash is None:
            self.content_hash = self.generate_content_hash()
    
    def generate_content_hash(self) -> str:
        """Generate a hash of the content for duplicate detection."""
        content_for_hash = f"{self.title}{self.content}".lower().strip()
        return hashlib.md5(content_for_hash.encode()).hexdigest()


class ContentParser:
    """HTML content parser for extracting article data."""
    
    def __init__(self):
        self.ai_keywords = {
            'artificial intelligence', 'machine learning', 'deep learning',
            'neural network', 'llm', 'large language model', 'gpt', 'bert',
            'transformer', 'nlp', 'natural language processing', 'computer vision',
            'reinforcement learning', 'ai', 'ml', 'chatgpt', 'openai', 'anthropic',
            'google ai', 'deepmind', 'tensorflow', 'pytorch', 'hugging face'
        }
    
    def parse_article(self, html: str, url: str, source_name: str = None) -> ParsedArticle:
        """Parse HTML content and extract article data."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Extract basic article data
            title = self._extract_title(soup)
            content = self._extract_content(soup)
            author = self._extract_author(soup)
            published_at = self._extract_published_date(soup)
            image_url = self._extract_image_url(soup, url)
            tags = self._extract_tags(soup)
            summary = self._extract_summary(soup)
            
            # Validate required fields
            if not title or not content:
                raise ContentParsingError(
                    "Failed to extract required fields (title or content)",
                    source_url=url,
                    parsing_stage="basic_extraction"
                )
            
            return ParsedArticle(
                title=title.strip(),
                content=content.strip(),
                url=url,
                author=author,
                published_at=published_at,
                tags=tags,
                summary=summary,
                image_url=image_url,
                source_name=source_name
            )
            
        except Exception as e:
            if isinstance(e, ContentParsingError):
                raise
            raise ContentParsingError(
                f"Failed to parse content: {str(e)}",
                source_url=url,
                parsing_stage="html_parsing"
            )
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract article title from HTML."""
        # Try multiple selectors in order of preference
        selectors = [
            'h1.entry-title',
            'h1.post-title', 
            'h1.article-title',
            'h1[class*="title"]',
            'h1',
            'title',
            '[property="og:title"]',
            '[name="twitter:title"]'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                if element.name == 'title':
                    return element.get_text()
                elif element.get('content'):
                    return element.get('content')
                else:
                    return element.get_text()
        
        return None
    
    def _extract_content(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract main article content from HTML."""
        # Remove unwanted elements
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
            element.decompose()
        
        # Try multiple content selectors
        content_selectors = [
            '.entry-content',
            '.post-content',
            '.article-content',
            '.content',
            '[class*="content"]',
            'article',
            '.main'
        ]
        
        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                # Extract text and clean it
                text = element.get_text(separator=' ', strip=True)
                if len(text) > 100:  # Minimum content length
                    return self._clean_text(text)
        
        # Fallback: try to extract from body
        body = soup.find('body')
        if body:
            text = body.get_text(separator=' ', strip=True)
            if len(text) > 100:
                return self._clean_text(text)
        
        return None
    
    def _extract_author(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract author information."""
        selectors = [
            '[rel="author"]',
            '.author',
            '.byline',
            '[class*="author"]',
            '[property="article:author"]',
            '[name="author"]'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                if element.get('content'):
                    return element.get('content')
                else:
                    return element.get_text(strip=True)
        
        return None
    
    def _extract_published_date(self, soup: BeautifulSoup) -> Optional[datetime]:
        """Extract publication date."""
        selectors = [
            '[property="article:published_time"]',
            '[property="article:modified_time"]',
            'time[datetime]',
            '.published',
            '.date',
            '[class*="date"]'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                date_str = element.get('datetime') or element.get('content') or element.get_text(strip=True)
                if date_str:
                    try:
                        return dateutil.parser.parse(date_str)
                    except:
                        continue
        
        return None
    
    def _extract_image_url(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """Extract main article image URL."""
        selectors = [
            '[property="og:image"]',
            '[name="twitter:image"]',
            '.featured-image img',
            '.article-image img',
            'article img'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                img_url = element.get('content') or element.get('src')
                if img_url:
                    return urljoin(base_url, img_url)
        
        return None
    
    def _extract_tags(self, soup: BeautifulSoup) -> List[str]:
        """Extract article tags/categories."""
        tags = []
        
        # Look for tag elements
        tag_selectors = [
            '.tags a',
            '.categories a', 
            '[rel="tag"]',
            '.tag',
            '[class*="tag"]'
        ]
        
        for selector in tag_selectors:
            elements = soup.select(selector)
            for element in elements:
                tag_text = element.get_text(strip=True)
                if tag_text and tag_text not in tags:
                    tags.append(tag_text)
        
        return tags
    
    def _extract_summary(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract article summary/description."""
        selectors = [
            '[property="og:description"]',
            '[name="description"]',
            '[name="twitter:description"]',
            '.excerpt',
            '.summary'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                summary = element.get('content') or element.get_text(strip=True)
                if summary and len(summary) > 50:
                    return summary
        
        return None
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', '', text)
        return text.strip()
    
    def is_ai_related(self, article: ParsedArticle) -> bool:
        """Check if article is AI/LLM related based on content."""
        text_to_check = f"{article.title} {article.content} {' '.join(article.tags)}".lower()
        
        for keyword in self.ai_keywords:
            if keyword in text_to_check:
                return True
        
        return False
