"""
Custom exceptions for the scraping framework.
"""


class ScrapingError(Exception):
    """Base exception for all scraping-related errors."""
    
    def __init__(self, message: str, source_url: str = None, status_code: int = None):
        self.message = message
        self.source_url = source_url
        self.status_code = status_code
        super().__init__(self.message)


class RateLimitError(ScrapingError):
    """Raised when rate limiting is triggered."""
    
    def __init__(self, message: str, retry_after: int = None, source_url: str = None):
        self.retry_after = retry_after
        super().__init__(message, source_url)


class ContentParsingError(ScrapingError):
    """Raised when content parsing fails."""
    
    def __init__(self, message: str, source_url: str = None, parsing_stage: str = None):
        self.parsing_stage = parsing_stage
        super().__init__(message, source_url)


class ProxyError(ScrapingError):
    """Raised when proxy-related issues occur."""
    
    def __init__(self, message: str, proxy_url: str = None, source_url: str = None):
        self.proxy_url = proxy_url
        super().__init__(message, source_url)


class DuplicateContentError(ScrapingError):
    """Raised when duplicate content is detected."""
    
    def __init__(self, message: str, duplicate_url: str = None, original_url: str = None):
        self.duplicate_url = duplicate_url
        self.original_url = original_url
        super().__init__(message, duplicate_url)


class ConfigurationError(ScrapingError):
    """Raised when scraper configuration is invalid."""
    
    def __init__(self, message: str, config_key: str = None):
        self.config_key = config_key
        super().__init__(message)


class NetworkError(ScrapingError):
    """Raised when network-related issues occur."""
    
    def __init__(self, message: str, source_url: str = None, timeout: int = None):
        self.timeout = timeout
        super().__init__(message, source_url)
