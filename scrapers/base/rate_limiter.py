"""
Rate limiting functionality for ethical scraping.
"""

import time
import async<PERSON>
from typing import Dict, Optional
from dataclasses import dataclass
from threading import Lock
import urllib.robotparser
from urllib.parse import urljoin, urlparse

from .exceptions import RateLimitError


@dataclass
class RateLimit:
    """Rate limit configuration for a domain."""
    requests_per_second: float = 1.0
    burst_size: int = 5
    delay_between_requests: float = 1.0
    respect_robots_txt: bool = True


class RateLimiter:
    """Thread-safe rate limiter with robots.txt support."""
    
    def __init__(self):
        self._domain_limits: Dict[str, RateLimit] = {}
        self._last_request_times: Dict[str, float] = {}
        self._request_counts: Dict[str, int] = {}
        self._robots_cache: Dict[str, urllib.robotparser.RobotFileParser] = {}
        self._lock = Lock()
    
    def set_rate_limit(self, domain: str, rate_limit: RateLimit):
        """Set rate limit for a specific domain."""
        with self._lock:
            self._domain_limits[domain] = rate_limit
    
    def get_domain_from_url(self, url: str) -> str:
        """Extract domain from URL."""
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    
    def check_robots_txt(self, url: str, user_agent: str = "*") -> bool:
        """Check if URL is allowed by robots.txt."""
        domain = self.get_domain_from_url(url)
        
        if domain not in self._robots_cache:
            robots_url = urljoin(domain, "/robots.txt")
            rp = urllib.robotparser.RobotFileParser()
            rp.set_url(robots_url)
            try:
                rp.read()
                self._robots_cache[domain] = rp
            except Exception:
                # If robots.txt can't be read, assume allowed
                return True
        
        return self._robots_cache[domain].can_fetch(user_agent, url)
    
    def wait_if_needed(self, url: str, user_agent: str = "*") -> None:
        """Wait if rate limiting is needed for the given URL."""
        domain = self.get_domain_from_url(url)
        
        with self._lock:
            # Get or create rate limit for domain
            rate_limit = self._domain_limits.get(domain, RateLimit())
            
            # Check robots.txt if enabled
            if rate_limit.respect_robots_txt and not self.check_robots_txt(url, user_agent):
                raise RateLimitError(
                    f"URL blocked by robots.txt: {url}",
                    source_url=url
                )
            
            current_time = time.time()
            last_request = self._last_request_times.get(domain, 0)
            
            # Calculate required delay
            time_since_last = current_time - last_request
            required_delay = rate_limit.delay_between_requests
            
            if time_since_last < required_delay:
                sleep_time = required_delay - time_since_last
                time.sleep(sleep_time)
            
            # Update tracking
            self._last_request_times[domain] = time.time()
    
    async def async_wait_if_needed(self, url: str, user_agent: str = "*") -> None:
        """Async version of wait_if_needed."""
        domain = self.get_domain_from_url(url)
        
        # Get or create rate limit for domain
        rate_limit = self._domain_limits.get(domain, RateLimit())
        
        # Check robots.txt if enabled
        if rate_limit.respect_robots_txt and not self.check_robots_txt(url, user_agent):
            raise RateLimitError(
                f"URL blocked by robots.txt: {url}",
                source_url=url
            )
        
        current_time = time.time()
        last_request = self._last_request_times.get(domain, 0)
        
        # Calculate required delay
        time_since_last = current_time - last_request
        required_delay = rate_limit.delay_between_requests
        
        if time_since_last < required_delay:
            sleep_time = required_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        # Update tracking
        self._last_request_times[domain] = time.time()
    
    def get_crawl_delay(self, url: str, user_agent: str = "*") -> Optional[float]:
        """Get crawl delay from robots.txt if available."""
        domain = self.get_domain_from_url(url)
        
        if domain in self._robots_cache:
            rp = self._robots_cache[domain]
            return rp.crawl_delay(user_agent)
        
        return None
    
    def clear_cache(self):
        """Clear all cached data."""
        with self._lock:
            self._last_request_times.clear()
            self._request_counts.clear()
            self._robots_cache.clear()
