"""
Base scraper class providing common functionality for all scrapers.
"""

import time
import random
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Iterator
from dataclasses import dataclass, field
from urllib.parse import urlparse
import logging

import requests
import aiohttp
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .exceptions import (
    ScrapingError, RateLimitError, NetworkError, 
    ConfigurationError, DuplicateContentError
)
from .rate_limiter import RateLimiter, RateLimit
from .content_parser import ContentParser, ParsedArticle


@dataclass
class ScraperConfig:
    """Configuration for scraper behavior."""
    name: str
    base_url: str
    rate_limit: RateLimit = field(default_factory=RateLimit)
    user_agents: List[str] = field(default_factory=lambda: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ])
    proxies: List[str] = field(default_factory=list)
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    headers: Dict[str, str] = field(default_factory=dict)
    cookies: Dict[str, str] = field(default_factory=dict)
    verify_ssl: bool = True
    follow_redirects: bool = True


class BaseScraper(ABC):
    """Abstract base class for all scrapers."""
    
    def __init__(self, config: ScraperConfig):
        self.config = config
        self.logger = logging.getLogger(f"scraper.{config.name}")
        self.rate_limiter = RateLimiter()
        self.content_parser = ContentParser()
        self.session = None
        self._setup_session()
        self._setup_rate_limiting()
        
        # Statistics tracking
        self.stats = {
            'requests_made': 0,
            'articles_scraped': 0,
            'errors_encountered': 0,
            'duplicates_found': 0,
            'start_time': time.time()
        }
    
    def _setup_session(self):
        """Setup HTTP session with retry strategy."""
        self.session = requests.Session()
        
        # Setup retry strategy
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Add custom headers
        if self.config.headers:
            self.session.headers.update(self.config.headers)
        
        # Set cookies
        if self.config.cookies:
            self.session.cookies.update(self.config.cookies)
    
    def _setup_rate_limiting(self):
        """Setup rate limiting for the scraper's domain."""
        domain = self._get_domain(self.config.base_url)
        self.rate_limiter.set_rate_limit(domain, self.config.rate_limit)
    
    def _get_domain(self, url: str) -> str:
        """Extract domain from URL."""
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    
    def _get_random_user_agent(self) -> str:
        """Get a random user agent from the configured list."""
        return random.choice(self.config.user_agents)
    
    def _get_proxy(self) -> Optional[Dict[str, str]]:
        """Get a random proxy if configured."""
        if not self.config.proxies:
            return None
        
        proxy_url = random.choice(self.config.proxies)
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def make_request(self, url: str, **kwargs) -> requests.Response:
        """Make an HTTP request with rate limiting and error handling."""
        try:
            # Apply rate limiting
            user_agent = self._get_random_user_agent()
            self.rate_limiter.wait_if_needed(url, user_agent)
            
            # Set user agent for this request
            headers = kwargs.get('headers', {})
            headers['User-Agent'] = user_agent
            kwargs['headers'] = headers
            
            # Set proxy if configured
            proxy = self._get_proxy()
            if proxy:
                kwargs['proxies'] = proxy
            
            # Set timeout
            kwargs.setdefault('timeout', self.config.timeout)
            kwargs.setdefault('verify', self.config.verify_ssl)
            kwargs.setdefault('allow_redirects', self.config.follow_redirects)
            
            self.logger.debug(f"Making request to: {url}")
            response = self.session.get(url, **kwargs)
            
            # Update statistics
            self.stats['requests_made'] += 1
            
            # Check for rate limiting
            if response.status_code == 429:
                retry_after = response.headers.get('Retry-After')
                raise RateLimitError(
                    f"Rate limited by server: {url}",
                    retry_after=int(retry_after) if retry_after else None,
                    source_url=url
                )
            
            # Raise for other HTTP errors
            response.raise_for_status()
            
            return response
            
        except requests.exceptions.RequestException as e:
            self.stats['errors_encountered'] += 1
            self.logger.error(f"Request failed for {url}: {e}")
            raise NetworkError(
                f"Network error: {str(e)}",
                source_url=url,
                timeout=self.config.timeout
            )
    
    def parse_content(self, html: str, url: str) -> ParsedArticle:
        """Parse HTML content into structured article data."""
        try:
            article = self.content_parser.parse_article(html, url, self.config.name)
            
            # Check if AI-related
            if not self.content_parser.is_ai_related(article):
                self.logger.debug(f"Article not AI-related, skipping: {url}")
                return None
            
            self.stats['articles_scraped'] += 1
            return article
            
        except Exception as e:
            self.stats['errors_encountered'] += 1
            self.logger.error(f"Content parsing failed for {url}: {e}")
            raise
    
    def is_duplicate(self, article: ParsedArticle) -> bool:
        """Check if article is a duplicate (to be implemented by subclasses)."""
        # Base implementation - subclasses should override with database checks
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get scraper statistics."""
        runtime = time.time() - self.stats['start_time']
        return {
            **self.stats,
            'runtime_seconds': runtime,
            'requests_per_minute': (self.stats['requests_made'] / runtime) * 60 if runtime > 0 else 0,
            'articles_per_minute': (self.stats['articles_scraped'] / runtime) * 60 if runtime > 0 else 0
        }
    
    def reset_stats(self):
        """Reset scraper statistics."""
        self.stats = {
            'requests_made': 0,
            'articles_scraped': 0,
            'errors_encountered': 0,
            'duplicates_found': 0,
            'start_time': time.time()
        }
    
    def close(self):
        """Clean up resources."""
        if self.session:
            self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    @abstractmethod
    def get_article_urls(self) -> Iterator[str]:
        """Get URLs of articles to scrape. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single article. Must be implemented by subclasses."""
        pass
    
    def scrape_all(self) -> Iterator[ParsedArticle]:
        """Scrape all articles from the source."""
        self.logger.info(f"Starting scrape for {self.config.name}")
        
        try:
            for url in self.get_article_urls():
                try:
                    article = self.scrape_article(url)
                    if article:
                        if self.is_duplicate(article):
                            self.stats['duplicates_found'] += 1
                            self.logger.debug(f"Duplicate article found: {url}")
                            continue
                        
                        yield article
                        
                except Exception as e:
                    self.logger.error(f"Failed to scrape article {url}: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"Scraping failed: {e}")
            raise ScrapingError(f"Scraping failed: {str(e)}")
        
        finally:
            stats = self.get_stats()
            self.logger.info(f"Scraping completed. Stats: {stats}")
