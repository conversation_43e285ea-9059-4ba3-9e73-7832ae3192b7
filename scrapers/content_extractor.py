"""
Enhanced content extractor for full-text article extraction.
Implements intelligent content parsing and metadata extraction.
"""

import logging
import requests
from typing import Optional, Dict, Any, List
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
import time

try:
    from bs4 import BeautifulSoup
    from readability import Document
    HAS_READABILITY = True
except ImportError:
    HAS_READABILITY = False

from models.database import Article

logger = logging.getLogger(__name__)


class ContentExtractor:
    """Enhanced content extractor for news articles."""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Common content selectors for different news sites
        self.content_selectors = {
            'techcrunch.com': ['.article-content', '.entry-content', '.post-content'],
            'wired.com': ['.article__chunks', '.content', '.post-content'],
            'arstechnica.com': ['.article-content', '.post-content'],
            'theverge.com': ['.duet--article--article-body-component', '.c-entry-content'],
            'venturebeat.com': ['.article-content', '.entry-content'],
            'zdnet.com': ['.storyBody', '.article-content'],
            'engadget.com': ['.article-text', '.o-article_block'],
            'reuters.com': ['.StandardArticleBody_body', '.ArticleBodyWrapper'],
            'bloomberg.com': ['.body-copy', '.fence-body'],
            'wsj.com': ['.wsj-snippet-body', '.article-content'],
            'ft.com': ['.article__content-body', '.n-content-body'],
            'guardian.com': ['.content__article-body', '.article-body-commercial-selector'],
            'bbc.com': ['.story-body__inner', '.gel-body-copy'],
            'default': [
                'article', '.article', '.content', '.post-content', 
                '.entry-content', '.article-content', '.story-content',
                '.post-body', '.article-body', '.main-content'
            ]
        }
        
        # Patterns for metadata extraction
        self.author_patterns = [
            re.compile(r'by\s+([^,\n]+)', re.IGNORECASE),
            re.compile(r'author[:\s]+([^,\n]+)', re.IGNORECASE),
            re.compile(r'written\s+by\s+([^,\n]+)', re.IGNORECASE)
        ]
        
        self.date_patterns = [
            re.compile(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})', re.IGNORECASE),
            re.compile(r'(\d{4}-\d{2}-\d{2})', re.IGNORECASE),
            re.compile(r'(\w+\s+\d{1,2},\s+\d{4})', re.IGNORECASE)
        ]
    
    def extract_full_content(self, url: str) -> Optional[Dict[str, Any]]:
        """Extract full content from a URL."""
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Extracting content from {url} (attempt {attempt + 1})")
                
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                # Parse with BeautifulSoup
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Try readability first if available
                if HAS_READABILITY:
                    content_data = self._extract_with_readability(response.text, url)
                    if content_data and content_data.get('content'):
                        # Enhance with additional metadata
                        enhanced_data = self._extract_metadata(soup, url)
                        content_data.update(enhanced_data)
                        return content_data
                
                # Fallback to manual extraction
                return self._extract_manually(soup, url)
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request error extracting {url} (attempt {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                continue
            except Exception as e:
                logger.error(f"Error extracting content from {url}: {e}")
                break
        
        return None
    
    def _extract_with_readability(self, html: str, url: str) -> Optional[Dict[str, Any]]:
        """Extract content using readability library."""
        try:
            doc = Document(html)
            
            # Get clean content
            content_html = doc.summary()
            content_soup = BeautifulSoup(content_html, 'html.parser')
            content_text = content_soup.get_text(strip=True, separator=' ')
            
            if len(content_text) < 100:  # Too short, probably failed
                return None
            
            return {
                'content': content_text,
                'title': doc.title(),
                'content_html': content_html,
                'extraction_method': 'readability'
            }
            
        except Exception as e:
            logger.debug(f"Readability extraction failed for {url}: {e}")
            return None
    
    def _extract_manually(self, soup: BeautifulSoup, url: str) -> Optional[Dict[str, Any]]:
        """Extract content manually using CSS selectors."""
        try:
            domain = urlparse(url).netloc.lower()
            
            # Get appropriate selectors for this domain
            selectors = self.content_selectors.get(domain, self.content_selectors['default'])
            
            content_text = ""
            content_html = ""
            
            # Try each selector until we find content
            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    for element in elements:
                        text = element.get_text(strip=True, separator=' ')
                        if len(text) > len(content_text):
                            content_text = text
                            content_html = str(element)
                    
                    if len(content_text) > 200:  # Found substantial content
                        break
            
            if len(content_text) < 100:
                # Last resort: try to get any substantial text
                content_text = soup.get_text(strip=True, separator=' ')
                content_html = str(soup)
            
            # Extract additional metadata
            metadata = self._extract_metadata(soup, url)
            
            return {
                'content': content_text,
                'content_html': content_html,
                'extraction_method': 'manual',
                **metadata
            }
            
        except Exception as e:
            logger.error(f"Manual extraction failed for {url}: {e}")
            return None
    
    def _extract_metadata(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Extract metadata from HTML."""
        metadata = {}
        
        try:
            # Extract title
            title_tag = soup.find('title')
            if title_tag:
                metadata['title'] = title_tag.get_text(strip=True)
            
            # Try meta tags for title
            og_title = soup.find('meta', property='og:title')
            if og_title and og_title.get('content'):
                metadata['title'] = og_title['content']
            
            # Extract description
            description_tag = soup.find('meta', attrs={'name': 'description'})
            if description_tag and description_tag.get('content'):
                metadata['description'] = description_tag['content']
            
            og_description = soup.find('meta', property='og:description')
            if og_description and og_description.get('content'):
                metadata['description'] = og_description['content']
            
            # Extract author
            author = self._extract_author(soup)
            if author:
                metadata['author'] = author
            
            # Extract publication date
            pub_date = self._extract_publication_date(soup)
            if pub_date:
                metadata['published_at'] = pub_date
            
            # Extract image
            og_image = soup.find('meta', property='og:image')
            if og_image and og_image.get('content'):
                metadata['image_url'] = urljoin(url, og_image['content'])
            
            # Extract keywords/tags
            keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
            if keywords_tag and keywords_tag.get('content'):
                metadata['keywords'] = [k.strip() for k in keywords_tag['content'].split(',')]
            
            # Extract word count
            content_text = metadata.get('content', soup.get_text())
            if content_text:
                word_count = len(content_text.split())
                metadata['word_count'] = word_count
            
        except Exception as e:
            logger.warning(f"Error extracting metadata from {url}: {e}")
        
        return metadata
    
    def _extract_author(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract author from HTML."""
        # Try meta tags first
        author_meta = soup.find('meta', attrs={'name': 'author'})
        if author_meta and author_meta.get('content'):
            return author_meta['content'].strip()
        
        # Try structured data
        author_span = soup.find('span', class_=re.compile(r'author', re.I))
        if author_span:
            return author_span.get_text(strip=True)
        
        # Try byline patterns
        text = soup.get_text()
        for pattern in self.author_patterns:
            match = pattern.search(text)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_publication_date(self, soup: BeautifulSoup) -> Optional[datetime]:
        """Extract publication date from HTML."""
        # Try meta tags
        date_meta = soup.find('meta', property='article:published_time')
        if date_meta and date_meta.get('content'):
            return self._parse_date(date_meta['content'])
        
        # Try time tags
        time_tag = soup.find('time')
        if time_tag:
            datetime_attr = time_tag.get('datetime')
            if datetime_attr:
                return self._parse_date(datetime_attr)
            
            time_text = time_tag.get_text(strip=True)
            if time_text:
                return self._parse_date(time_text)
        
        # Try date patterns in text
        text = soup.get_text()
        for pattern in self.date_patterns:
            match = pattern.search(text)
            if match:
                return self._parse_date(match.group(1))
        
        return None
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string into datetime object."""
        try:
            # Try ISO format first
            if 'T' in date_str:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            
            # Try date only
            if re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                return datetime.strptime(date_str, '%Y-%m-%d')
            
            # Try other common formats
            formats = [
                '%B %d, %Y',
                '%b %d, %Y',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            
        except Exception as e:
            logger.debug(f"Could not parse date '{date_str}': {e}")
        
        return None
    
    def enhance_article(self, article: Article) -> Article:
        """Enhance an existing article with full content extraction."""
        if not article.url:
            return article
        
        try:
            extracted_data = self.extract_full_content(article.url)
            if not extracted_data:
                return article
            
            # Update article with extracted data
            if extracted_data.get('content') and len(extracted_data['content']) > len(article.content or ""):
                article.content = extracted_data['content']
            
            if extracted_data.get('title') and not article.title:
                article.title = extracted_data['title']
            
            # Note: Article model doesn't have summary field, skip this
            
            if extracted_data.get('author') and not article.author:
                article.author = extracted_data['author']
            
            if extracted_data.get('published_at') and not article.published_at:
                article.published_at = extracted_data['published_at']
            
            # Update engagement_metrics
            if not article.engagement_metrics:
                article.engagement_metrics = {}

            article.engagement_metrics.update({
                'extraction_method': extracted_data.get('extraction_method'),
                'word_count': extracted_data.get('word_count'),
                'image_url': extracted_data.get('image_url'),
                'keywords': extracted_data.get('keywords', []),
                'full_text_extracted': True
            })
            
        except Exception as e:
            logger.error(f"Error enhancing article {article.url}: {e}")
        
        return article
