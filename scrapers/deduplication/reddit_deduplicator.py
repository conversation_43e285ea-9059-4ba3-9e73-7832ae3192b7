"""
Reddit-specific duplicate detection system.

This module provides comprehensive duplicate detection for Reddit posts including:
- Post ID-based exact duplicate detection
- Content similarity detection for cross-posts
- URL-based duplicate detection for link posts
- Title similarity detection
- Temporal clustering for related discussions
"""

import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict
import re

from difflib import SequenceMatcher
from urllib.parse import urlparse, parse_qs


@dataclass
class DuplicateMatch:
    """Represents a duplicate match between Reddit posts."""
    
    original_post_id: str
    duplicate_post_id: str
    match_type: str  # 'exact_id', 'content_similarity', 'url_match', 'title_similarity'
    confidence: float  # 0.0 to 1.0
    similarity_score: float
    details: Dict[str, Any]


@dataclass
class PostFingerprint:
    """Fingerprint data for a Reddit post used in duplicate detection."""
    
    post_id: str
    reddit_id: str
    title: str
    content_hash: str
    url_hash: Optional[str]
    subreddit: str
    author: str
    created_utc: datetime
    title_normalized: str
    content_normalized: str


class RedditDeduplicator:
    """Advanced duplicate detection system for Reddit posts."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Storage for post fingerprints
        self.post_fingerprints: Dict[str, PostFingerprint] = {}
        self.reddit_id_index: Dict[str, str] = {}  # reddit_id -> post_id
        self.url_hash_index: Dict[str, List[str]] = defaultdict(list)  # url_hash -> [post_ids]
        self.content_hash_index: Dict[str, List[str]] = defaultdict(list)  # content_hash -> [post_ids]
        
        # Similarity thresholds
        self.title_similarity_threshold = 0.85
        self.content_similarity_threshold = 0.75  # Lowered from 0.90 to 0.75 for better detection
        self.url_similarity_threshold = 0.95
        
        # Temporal clustering settings
        self.temporal_window_hours = 24  # Consider posts within 24 hours for clustering
        
        # Common Reddit URL patterns for normalization
        self.reddit_url_patterns = [
            r'reddit\.com/r/\w+/comments/(\w+)',
            r'redd\.it/(\w+)',
            r'reddit\.com/(\w+)'
        ]
    
    def add_post(self, post_data: Dict[str, Any]) -> Optional[PostFingerprint]:
        """Add a post to the duplicate detection system."""
        try:
            # Extract required fields
            post_id = post_data.get('id') or post_data.get('post_id')
            reddit_id = post_data.get('reddit_id')
            title = post_data.get('title', '')
            content = post_data.get('content', '')
            url = post_data.get('url') or post_data.get('external_url')
            subreddit = post_data.get('subreddit', '')
            author = post_data.get('author', '')
            
            if not post_id or not reddit_id:
                self.logger.warning("Post missing required ID fields")
                return None
            
            # Handle datetime
            created_utc = post_data.get('created_utc') or post_data.get('published_at')
            if isinstance(created_utc, str):
                created_utc = datetime.fromisoformat(created_utc.replace('Z', '+00:00'))
            elif not isinstance(created_utc, datetime):
                created_utc = datetime.now()
            
            # Get external URL
            external_url = post_data.get('external_url')

            # Create fingerprint
            fingerprint = self._create_fingerprint(
                post_id, reddit_id, title, content, url, subreddit, author, created_utc, external_url
            )
            
            # Store fingerprint and update indices
            self.post_fingerprints[post_id] = fingerprint
            self.reddit_id_index[reddit_id] = post_id
            
            if fingerprint.url_hash:
                self.url_hash_index[fingerprint.url_hash].append(post_id)
            
            self.content_hash_index[fingerprint.content_hash].append(post_id)
            
            return fingerprint
            
        except Exception as e:
            self.logger.error(f"Failed to add post to deduplicator: {e}")
            return None
    
    def find_duplicates(self, post_data: Dict[str, Any]) -> List[DuplicateMatch]:
        """Find all duplicates for a given post."""
        duplicates = []

        # Create temporary fingerprint for comparison (don't add to indices yet)
        post_id = post_data.get('id') or post_data.get('post_id')
        reddit_id = post_data.get('reddit_id')
        title = post_data.get('title', '')
        content = post_data.get('content', '')
        url = post_data.get('url') or post_data.get('external_url')
        subreddit = post_data.get('subreddit', '')
        author = post_data.get('author', '')

        if not post_id or not reddit_id:
            return duplicates

        # Handle datetime
        created_utc = post_data.get('created_utc') or post_data.get('published_at')
        if isinstance(created_utc, str):
            created_utc = datetime.fromisoformat(created_utc.replace('Z', '+00:00'))
        elif not isinstance(created_utc, datetime):
            created_utc = datetime.now()

        # Get external URL for better duplicate detection
        external_url = post_data.get('external_url')

        # Create temporary fingerprint for comparison
        temp_fingerprint = self._create_fingerprint(
            post_id, reddit_id, title, content, url, subreddit, author, created_utc, external_url
        )
        
        # 1. Exact Reddit ID match
        exact_matches = self._find_exact_id_duplicates(temp_fingerprint)
        duplicates.extend(exact_matches)
        
        # 2. URL-based duplicates
        url_matches = self._find_url_duplicates(temp_fingerprint)
        duplicates.extend(url_matches)
        
        # 3. Content similarity duplicates
        content_matches = self._find_content_duplicates(temp_fingerprint)
        duplicates.extend(content_matches)
        
        # 4. Title similarity duplicates
        title_matches = self._find_title_duplicates(temp_fingerprint)
        duplicates.extend(title_matches)
        
        # Remove duplicates from results and exclude true self-matches
        seen_pairs = set()
        unique_duplicates = []

        for match in duplicates:
            # Skip if both IDs are the same (true self-match)
            if match.original_post_id == match.duplicate_post_id:
                continue

            pair_key = tuple(sorted([match.original_post_id, match.duplicate_post_id]))
            if pair_key not in seen_pairs:
                seen_pairs.add(pair_key)
                unique_duplicates.append(match)

        # Now add the post to our indices after checking for duplicates
        # Only add if it's not already there (to avoid overwriting during duplicate detection)
        if post_id not in self.post_fingerprints:
            self.post_fingerprints[post_id] = temp_fingerprint

            # Only update reddit_id_index if this reddit_id isn't already mapped
            if reddit_id not in self.reddit_id_index:
                self.reddit_id_index[reddit_id] = post_id

            if temp_fingerprint.url_hash:
                self.url_hash_index[temp_fingerprint.url_hash].append(post_id)

            self.content_hash_index[temp_fingerprint.content_hash].append(post_id)

        return unique_duplicates
    
    def _create_fingerprint(self, post_id: str, reddit_id: str, title: str,
                           content: str, url: Optional[str], subreddit: str,
                           author: str, created_utc: datetime,
                           external_url: Optional[str] = None) -> PostFingerprint:
        """Create a fingerprint for duplicate detection."""
        
        # Normalize text for comparison
        title_normalized = self._normalize_text(title)
        content_normalized = self._normalize_text(content)
        
        # Create content hash
        content_for_hash = f"{title_normalized}|{content_normalized}"
        content_hash = hashlib.md5(content_for_hash.encode()).hexdigest()
        
        # Create URL hash - prioritize external_url over reddit url for duplicate detection
        url_hash = None

        # First try external_url (for link posts)
        if external_url:
            normalized_url = self._normalize_url(external_url)
            if normalized_url:
                url_hash = hashlib.md5(normalized_url.encode()).hexdigest()

        # If no external_url, use the reddit url
        if not url_hash and url:
            normalized_url = self._normalize_url(url)
            if normalized_url:
                url_hash = hashlib.md5(normalized_url.encode()).hexdigest()
        
        return PostFingerprint(
            post_id=post_id,
            reddit_id=reddit_id,
            title=title,
            content_hash=content_hash,
            url_hash=url_hash,
            subreddit=subreddit,
            author=author,
            created_utc=created_utc,
            title_normalized=title_normalized,
            content_normalized=content_normalized
        )
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove common Reddit formatting
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic
        text = re.sub(r'~~([^~]+)~~', r'\1', text)      # Strikethrough
        text = re.sub(r'\^([^\s]+)', r'\1', text)       # Superscript
        
        # Remove URLs
        text = re.sub(r'https?://[^\s]+', '', text)
        
        # Remove Reddit-specific patterns
        text = re.sub(r'/u/\w+', '', text)              # User mentions
        text = re.sub(r'/r/\w+', '', text)              # Subreddit mentions
        text = re.sub(r'r/\w+', '', text)               # Subreddit mentions without /
        
        # Remove punctuation for better matching
        text = re.sub(r'[^\w\s]', '', text)
        
        return text.strip()
    
    def _normalize_url(self, url: str) -> Optional[str]:
        """Normalize URL for comparison."""
        if not url:
            return None
        
        try:
            # Parse URL
            parsed = urlparse(url)
            
            # Handle Reddit URLs specially
            for pattern in self.reddit_url_patterns:
                match = re.search(pattern, url)
                if match:
                    return f"reddit:{match.group(1)}"
            
            # For other URLs, normalize domain and path
            domain = parsed.netloc.lower()
            path = parsed.path.lower()
            
            # Remove common tracking parameters
            if parsed.query:
                query_params = parse_qs(parsed.query)
                # Keep only essential parameters, remove tracking
                essential_params = {}
                for key, values in query_params.items():
                    if key not in ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term']:
                        essential_params[key] = values[0] if values else ''
                
                if essential_params:
                    query_string = '&'.join(f"{k}={v}" for k, v in essential_params.items())
                    return f"{domain}{path}?{query_string}"
            
            return f"{domain}{path}"
            
        except Exception:
            return url.lower()
    
    def _find_exact_id_duplicates(self, fingerprint: PostFingerprint) -> List[DuplicateMatch]:
        """Find exact duplicates based on Reddit ID."""
        duplicates = []

        if fingerprint.reddit_id in self.reddit_id_index:
            existing_post_id = self.reddit_id_index[fingerprint.reddit_id]
            if existing_post_id != fingerprint.post_id:
                duplicates.append(DuplicateMatch(
                    original_post_id=existing_post_id,
                    duplicate_post_id=fingerprint.post_id,
                    match_type='exact_id',
                    confidence=1.0,
                    similarity_score=1.0,
                    details={'reddit_id': fingerprint.reddit_id}
                ))

        return duplicates
    
    def _find_url_duplicates(self, fingerprint: PostFingerprint) -> List[DuplicateMatch]:
        """Find duplicates based on URL similarity."""
        duplicates = []
        
        if not fingerprint.url_hash:
            return duplicates
        
        if fingerprint.url_hash in self.url_hash_index:
            for existing_post_id in self.url_hash_index[fingerprint.url_hash]:
                if existing_post_id != fingerprint.post_id:
                    duplicates.append(DuplicateMatch(
                        original_post_id=existing_post_id,
                        duplicate_post_id=fingerprint.post_id,
                        match_type='url_match',
                        confidence=0.95,
                        similarity_score=1.0,
                        details={'url_hash': fingerprint.url_hash}
                    ))
        
        return duplicates
    
    def _find_content_duplicates(self, fingerprint: PostFingerprint) -> List[DuplicateMatch]:
        """Find duplicates based on content similarity."""
        duplicates = []
        
        # Check exact content hash matches first
        if fingerprint.content_hash in self.content_hash_index:
            for existing_post_id in self.content_hash_index[fingerprint.content_hash]:
                if existing_post_id != fingerprint.post_id:
                    duplicates.append(DuplicateMatch(
                        original_post_id=existing_post_id,
                        duplicate_post_id=fingerprint.post_id,
                        match_type='content_similarity',
                        confidence=0.90,
                        similarity_score=1.0,
                        details={'content_hash': fingerprint.content_hash}
                    ))
        
        # Check fuzzy content similarity for posts in temporal window
        temporal_candidates = self._get_temporal_candidates(fingerprint)
        
        for candidate_id in temporal_candidates:
            if candidate_id == fingerprint.post_id:
                continue
            
            candidate = self.post_fingerprints[candidate_id]
            
            # Calculate content similarity
            content_similarity = SequenceMatcher(
                None, 
                fingerprint.content_normalized, 
                candidate.content_normalized
            ).ratio()
            
            if content_similarity >= self.content_similarity_threshold:
                duplicates.append(DuplicateMatch(
                    original_post_id=candidate_id,
                    duplicate_post_id=fingerprint.post_id,
                    match_type='content_similarity',
                    confidence=content_similarity * 0.8,  # Slightly lower confidence for fuzzy matches
                    similarity_score=content_similarity,
                    details={'content_similarity': content_similarity}
                ))
        
        return duplicates
    
    def _find_title_duplicates(self, fingerprint: PostFingerprint) -> List[DuplicateMatch]:
        """Find duplicates based on title similarity."""
        duplicates = []
        
        # Check title similarity for posts in temporal window
        temporal_candidates = self._get_temporal_candidates(fingerprint)
        
        for candidate_id in temporal_candidates:
            if candidate_id == fingerprint.post_id:
                continue
            
            candidate = self.post_fingerprints[candidate_id]
            
            # Calculate title similarity
            title_similarity = SequenceMatcher(
                None, 
                fingerprint.title_normalized, 
                candidate.title_normalized
            ).ratio()
            
            if title_similarity >= self.title_similarity_threshold:
                duplicates.append(DuplicateMatch(
                    original_post_id=candidate_id,
                    duplicate_post_id=fingerprint.post_id,
                    match_type='title_similarity',
                    confidence=title_similarity * 0.7,  # Lower confidence for title-only matches
                    similarity_score=title_similarity,
                    details={'title_similarity': title_similarity}
                ))
        
        return duplicates
    
    def _get_temporal_candidates(self, fingerprint: PostFingerprint) -> List[str]:
        """Get candidate posts within temporal window for similarity comparison."""
        candidates = []
        
        time_window_start = fingerprint.created_utc - timedelta(hours=self.temporal_window_hours)
        time_window_end = fingerprint.created_utc + timedelta(hours=self.temporal_window_hours)
        
        for post_id, candidate in self.post_fingerprints.items():
            if time_window_start <= candidate.created_utc <= time_window_end:
                candidates.append(post_id)
        
        return candidates
    
    def get_duplicate_clusters(self) -> List[List[str]]:
        """Get clusters of duplicate posts."""
        clusters = []
        processed = set()
        
        for post_id in self.post_fingerprints:
            if post_id in processed:
                continue
            
            # Find all duplicates for this post
            post_data = {
                'id': post_id,
                'reddit_id': self.post_fingerprints[post_id].reddit_id,
                'title': self.post_fingerprints[post_id].title,
                'content': '',  # We already have the fingerprint
                'subreddit': self.post_fingerprints[post_id].subreddit,
                'author': self.post_fingerprints[post_id].author,
                'created_utc': self.post_fingerprints[post_id].created_utc
            }
            
            duplicates = self.find_duplicates(post_data)
            
            if duplicates:
                cluster = {post_id}
                for dup in duplicates:
                    cluster.add(dup.original_post_id)
                    cluster.add(dup.duplicate_post_id)
                
                cluster_list = list(cluster)
                clusters.append(cluster_list)
                processed.update(cluster)
        
        return clusters
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get deduplication statistics."""
        total_posts = len(self.post_fingerprints)
        clusters = self.get_duplicate_clusters()
        
        duplicate_posts = sum(len(cluster) for cluster in clusters)
        unique_posts = total_posts - duplicate_posts + len(clusters)
        
        return {
            'total_posts': total_posts,
            'unique_posts': unique_posts,
            'duplicate_posts': duplicate_posts,
            'duplicate_clusters': len(clusters),
            'deduplication_ratio': (duplicate_posts / total_posts) if total_posts > 0 else 0.0,
            'largest_cluster_size': max(len(cluster) for cluster in clusters) if clusters else 0
        }
