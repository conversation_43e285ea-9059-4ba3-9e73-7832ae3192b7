"""
Twitter-specific duplicate detection system.

This module provides comprehensive duplicate detection for Twitter posts including:
- Exact tweet ID matching
- Content similarity detection
- URL-based duplicate detection
- Temporal clustering for related discussions
- Retweet and quote tweet handling
"""

import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import defaultdict
import re
from difflib import SequenceMatcher


@dataclass
class TweetFingerprint:
    """Fingerprint for a Twitter post."""
    post_id: str
    tweet_id: str
    title: str
    content_hash: str
    url_hash: Optional[str]
    author: str
    created_utc: datetime
    title_normalized: str
    content_normalized: str


@dataclass
class DuplicateMatch:
    """Represents a duplicate match between two posts."""
    original_post_id: str
    duplicate_post_id: str
    match_type: str  # 'exact_id', 'content_similarity', 'url_match', 'title_similarity'
    confidence: float
    similarity_score: float
    details: Dict[str, Any]


class TwitterDeduplicator:
    """Twitter-specific duplicate detection system."""
    
    def __init__(self):
        self.logger = logging.getLogger("twitter.deduplicator")
        
        # Storage for post fingerprints
        self.post_fingerprints: Dict[str, TweetFingerprint] = {}
        
        # Indices for fast lookup
        self.tweet_id_index: Dict[str, str] = {}  # tweet_id -> post_id
        self.url_hash_index: Dict[str, List[str]] = defaultdict(list)  # url_hash -> [post_ids]
        self.content_hash_index: Dict[str, List[str]] = defaultdict(list)  # content_hash -> [post_ids]
        
        # Similarity thresholds
        self.title_similarity_threshold = 0.85
        self.content_similarity_threshold = 0.75
        self.url_similarity_threshold = 0.95
        
        # Temporal clustering settings
        self.temporal_window_hours = 24  # Consider posts within 24 hours for clustering
        
        # Twitter-specific patterns
        self.twitter_url_patterns = [
            r'twitter\.com/\w+/status/(\d+)',
            r'x\.com/\w+/status/(\d+)'
        ]
    
    def add_post(self, post_data: Dict[str, Any]) -> TweetFingerprint:
        """Add a post to the deduplication system."""
        fingerprint = self._create_fingerprint(
            post_data['id'],
            post_data.get('tweet_id', ''),
            post_data.get('title', ''),
            post_data.get('content', ''),
            post_data.get('url', ''),
            post_data.get('author', ''),
            post_data.get('created_utc', datetime.now())
        )
        
        # Store fingerprint
        self.post_fingerprints[fingerprint.post_id] = fingerprint
        
        # Update indices
        if fingerprint.tweet_id:
            self.tweet_id_index[fingerprint.tweet_id] = fingerprint.post_id
        
        if fingerprint.url_hash:
            self.url_hash_index[fingerprint.url_hash].append(fingerprint.post_id)
        
        self.content_hash_index[fingerprint.content_hash].append(fingerprint.post_id)
        
        return fingerprint
    
    def find_duplicates(self, post_data: Dict[str, Any]) -> List[DuplicateMatch]:
        """Find duplicates for a given post."""
        post_id = post_data['id']
        tweet_id = post_data.get('tweet_id', '')
        
        # Create temporary fingerprint for comparison
        temp_fingerprint = self._create_fingerprint(
            post_id, tweet_id, post_data.get('title', ''),
            post_data.get('content', ''), post_data.get('url', ''),
            post_data.get('author', ''), post_data.get('created_utc', datetime.now())
        )
        
        duplicates = []
        
        # 1. Exact tweet ID match
        exact_matches = self._find_exact_id_duplicates(temp_fingerprint)
        duplicates.extend(exact_matches)
        
        # 2. URL-based duplicates
        url_matches = self._find_url_duplicates(temp_fingerprint)
        duplicates.extend(url_matches)
        
        # 3. Content similarity duplicates
        content_matches = self._find_content_duplicates(temp_fingerprint)
        duplicates.extend(content_matches)
        
        # 4. Title similarity duplicates
        title_matches = self._find_title_duplicates(temp_fingerprint)
        duplicates.extend(title_matches)
        
        # Remove duplicates from results and exclude true self-matches
        seen_pairs = set()
        unique_duplicates = []
        
        for match in duplicates:
            # Skip if both IDs are the same (true self-match)
            if match.original_post_id == match.duplicate_post_id:
                continue
            
            pair_key = tuple(sorted([match.original_post_id, match.duplicate_post_id]))
            if pair_key not in seen_pairs:
                seen_pairs.add(pair_key)
                unique_duplicates.append(match)
        
        # Now add the post to our indices after checking for duplicates
        # Only add if it's not already there (to avoid overwriting during duplicate detection)
        if post_id not in self.post_fingerprints:
            self.post_fingerprints[post_id] = temp_fingerprint
            
            # Only update tweet_id_index if this tweet_id isn't already mapped
            if tweet_id and tweet_id not in self.tweet_id_index:
                self.tweet_id_index[tweet_id] = post_id
            
            if temp_fingerprint.url_hash:
                self.url_hash_index[temp_fingerprint.url_hash].append(post_id)
            
            self.content_hash_index[temp_fingerprint.content_hash].append(post_id)
        
        return unique_duplicates
    
    def _create_fingerprint(self, post_id: str, tweet_id: str, title: str, content: str, 
                          url: str, author: str, created_utc: datetime) -> TweetFingerprint:
        """Create a fingerprint for a Twitter post."""
        
        # Normalize content for comparison
        content_normalized = self._normalize_text(content)
        title_normalized = self._normalize_text(title)
        
        # Create content hash
        content_for_hash = f"{content_normalized}|{author}".lower()
        content_hash = hashlib.md5(content_for_hash.encode()).hexdigest()
        
        # Create URL hash if URL exists
        url_hash = None
        if url:
            normalized_url = self._normalize_url(url)
            if normalized_url:
                url_hash = hashlib.md5(normalized_url.encode()).hexdigest()
        
        return TweetFingerprint(
            post_id=post_id,
            tweet_id=tweet_id,
            title=title,
            content_hash=content_hash,
            url_hash=url_hash,
            author=author,
            created_utc=created_utc,
            title_normalized=title_normalized,
            content_normalized=content_normalized
        )
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove Twitter-specific elements
        text = re.sub(r'@\w+', '', text)  # Remove mentions
        text = re.sub(r'#\w+', '', text)  # Remove hashtags
        text = re.sub(r'http[s]?://\S+', '', text)  # Remove URLs
        text = re.sub(r'pic\.twitter\.com/\S+', '', text)  # Remove Twitter pic URLs
        
        # Remove extra whitespace and punctuation
        text = re.sub(r'[^\w\s]', '', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _normalize_url(self, url: str) -> Optional[str]:
        """Normalize URL for comparison."""
        if not url:
            return None
        
        try:
            from urllib.parse import urlparse, parse_qs
            
            # Parse URL
            parsed = urlparse(url)
            
            # Handle Twitter URLs specially
            for pattern in self.twitter_url_patterns:
                match = re.search(pattern, url)
                if match:
                    return f"twitter:{match.group(1)}"
            
            # For other URLs, normalize domain and path
            domain = parsed.netloc.lower()
            path = parsed.path.lower()
            
            # Remove common tracking parameters
            if parsed.query:
                query_params = parse_qs(parsed.query)
                # Keep only essential parameters, remove tracking
                essential_params = {}
                for key, values in query_params.items():
                    if key not in ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term']:
                        essential_params[key] = values[0] if values else ''
                
                if essential_params:
                    query_string = '&'.join([f"{k}={v}" for k, v in sorted(essential_params.items())])
                    return f"{domain}{path}?{query_string}"
            
            return f"{domain}{path}"
            
        except Exception as e:
            self.logger.warning(f"Failed to normalize URL {url}: {e}")
            return url.lower()
    
    def _find_exact_id_duplicates(self, fingerprint: TweetFingerprint) -> List[DuplicateMatch]:
        """Find exact duplicates based on tweet ID."""
        duplicates = []
        
        if fingerprint.tweet_id in self.tweet_id_index:
            existing_post_id = self.tweet_id_index[fingerprint.tweet_id]
            if existing_post_id != fingerprint.post_id:
                duplicates.append(DuplicateMatch(
                    original_post_id=existing_post_id,
                    duplicate_post_id=fingerprint.post_id,
                    match_type='exact_id',
                    confidence=1.0,
                    similarity_score=1.0,
                    details={'tweet_id': fingerprint.tweet_id}
                ))
        
        return duplicates
    
    def _find_url_duplicates(self, fingerprint: TweetFingerprint) -> List[DuplicateMatch]:
        """Find duplicates based on URL similarity."""
        duplicates = []
        
        if not fingerprint.url_hash:
            return duplicates
        
        if fingerprint.url_hash in self.url_hash_index:
            for existing_post_id in self.url_hash_index[fingerprint.url_hash]:
                if existing_post_id != fingerprint.post_id:
                    duplicates.append(DuplicateMatch(
                        original_post_id=existing_post_id,
                        duplicate_post_id=fingerprint.post_id,
                        match_type='url_match',
                        confidence=0.95,
                        similarity_score=1.0,
                        details={'url_hash': fingerprint.url_hash}
                    ))
        
        return duplicates

    def _find_content_duplicates(self, fingerprint: TweetFingerprint) -> List[DuplicateMatch]:
        """Find duplicates based on content similarity."""
        duplicates = []

        # Check exact content hash matches first
        if fingerprint.content_hash in self.content_hash_index:
            for existing_post_id in self.content_hash_index[fingerprint.content_hash]:
                if existing_post_id != fingerprint.post_id:
                    duplicates.append(DuplicateMatch(
                        original_post_id=existing_post_id,
                        duplicate_post_id=fingerprint.post_id,
                        match_type='content_similarity',
                        confidence=0.90,
                        similarity_score=1.0,
                        details={'content_hash': fingerprint.content_hash}
                    ))

        # Check fuzzy content similarity
        for existing_post_id, existing_fingerprint in self.post_fingerprints.items():
            if existing_post_id == fingerprint.post_id:
                continue

            # Skip if we already found an exact match
            if any(d.original_post_id == existing_post_id and d.match_type == 'content_similarity'
                   for d in duplicates):
                continue

            # Calculate content similarity
            similarity = SequenceMatcher(
                None,
                fingerprint.content_normalized,
                existing_fingerprint.content_normalized
            ).ratio()

            if similarity >= self.content_similarity_threshold:
                duplicates.append(DuplicateMatch(
                    original_post_id=existing_post_id,
                    duplicate_post_id=fingerprint.post_id,
                    match_type='content_similarity',
                    confidence=similarity,
                    similarity_score=similarity,
                    details={'similarity_score': similarity}
                ))

        return duplicates

    def _find_title_duplicates(self, fingerprint: TweetFingerprint) -> List[DuplicateMatch]:
        """Find duplicates based on title similarity."""
        duplicates = []

        for existing_post_id, existing_fingerprint in self.post_fingerprints.items():
            if existing_post_id == fingerprint.post_id:
                continue

            # Skip if we already found other types of matches
            if any(d.original_post_id == existing_post_id for d in duplicates):
                continue

            # Calculate title similarity
            similarity = SequenceMatcher(
                None,
                fingerprint.title_normalized,
                existing_fingerprint.title_normalized
            ).ratio()

            if similarity >= self.title_similarity_threshold:
                duplicates.append(DuplicateMatch(
                    original_post_id=existing_post_id,
                    duplicate_post_id=fingerprint.post_id,
                    match_type='title_similarity',
                    confidence=similarity,
                    similarity_score=similarity,
                    details={'similarity_score': similarity}
                ))

        return duplicates

    def get_temporal_clusters(self, time_window_hours: int = 24) -> List[List[str]]:
        """Get clusters of posts that are temporally related."""
        clusters = []
        processed_posts = set()

        # Sort posts by creation time
        sorted_posts = sorted(
            self.post_fingerprints.items(),
            key=lambda x: x[1].created_utc
        )

        for post_id, fingerprint in sorted_posts:
            if post_id in processed_posts:
                continue

            # Start a new cluster
            cluster = [post_id]
            processed_posts.add(post_id)

            # Find posts within time window
            time_window = timedelta(hours=time_window_hours)

            for other_post_id, other_fingerprint in sorted_posts:
                if other_post_id in processed_posts:
                    continue

                # Check if within time window
                time_diff = abs(fingerprint.created_utc - other_fingerprint.created_utc)
                if time_diff <= time_window:
                    # Check if content is related (basic similarity check)
                    similarity = SequenceMatcher(
                        None,
                        fingerprint.content_normalized,
                        other_fingerprint.content_normalized
                    ).ratio()

                    if similarity >= 0.3:  # Lower threshold for temporal clustering
                        cluster.append(other_post_id)
                        processed_posts.add(other_post_id)

            # Only include clusters with more than one post
            if len(cluster) > 1:
                clusters.append(cluster)

        return clusters

    def get_deduplication_stats(self) -> Dict[str, Any]:
        """Get statistics about the deduplication process."""
        total_posts = len(self.post_fingerprints)

        # Get temporal clusters
        clusters = self.get_temporal_clusters()

        return {
            'total_posts': total_posts,
            'unique_posts': total_posts,
            'duplicate_clusters': len(clusters),
            'deduplication_ratio': 0.0,  # Simplified for now
            'temporal_clusters': len(clusters),
            'avg_cluster_size': sum(len(cluster) for cluster in clusters) / max(len(clusters), 1) if clusters else 0
        }

    def get_duplicate_clusters(self) -> List[List[str]]:
        """Get all duplicate clusters."""
        clusters = []
        processed = set()

        for post_id in self.post_fingerprints:
            if post_id in processed:
                continue

            temp_fingerprint = self.post_fingerprints[post_id]
            cluster = [post_id]

            # Find direct duplicates
            for other_post_id, other_fingerprint in self.post_fingerprints.items():
                if other_post_id == post_id or other_post_id in processed:
                    continue

                # Check various similarity criteria
                is_duplicate = (
                    temp_fingerprint.tweet_id == other_fingerprint.tweet_id or
                    temp_fingerprint.content_hash == other_fingerprint.content_hash or
                    (temp_fingerprint.url_hash and temp_fingerprint.url_hash == other_fingerprint.url_hash)
                )

                if is_duplicate:
                    cluster.append(other_post_id)

            if len(cluster) > 1:
                clusters.append(cluster)
                processed.update(cluster)
            else:
                processed.add(post_id)

        return clusters
