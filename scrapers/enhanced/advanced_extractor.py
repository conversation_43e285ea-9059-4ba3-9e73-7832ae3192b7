"""
Advanced content extraction framework for Phase 10 web scraping enhancement.

This module provides sophisticated content extraction capabilities including:
- Multiple parsing strategies with fallback mechanisms
- JavaScript rendering support via Selenium
- Content quality assessment and scoring
- Advanced metadata extraction
- PDF processing for research papers
- Image and media content analysis
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from urllib.parse import urljoin, urlparse
import hashlib
import re

import requests
from bs4 import BeautifulSoup, Tag
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

try:
    from readability import Document
    HAS_READABILITY = True
except ImportError:
    HAS_READABILITY = False

try:
    import PyPDF2
    import fitz  # PyMuPDF
    HAS_PDF_SUPPORT = True
except ImportError:
    HAS_PDF_SUPPORT = False

try:
    from newspaper import Article as NewspaperArticle
    HAS_NEWSPAPER = True
except ImportError:
    HAS_NEWSPAPER = False


logger = logging.getLogger(__name__)


class ExtractionStrategy(Enum):
    """Content extraction strategies in order of preference."""
    READABILITY = "readability"
    NEWSPAPER = "newspaper"
    CUSTOM_SELECTORS = "custom_selectors"
    SELENIUM_RENDERED = "selenium_rendered"
    FALLBACK_HEURISTICS = "fallback_heuristics"


class ContentType(Enum):
    """Types of content that can be extracted."""
    ARTICLE = "article"
    RESEARCH_PAPER = "research_paper"
    BLOG_POST = "blog_post"
    FORUM_POST = "forum_post"
    NEWSLETTER = "newsletter"
    ACADEMIC_PAPER = "academic_paper"
    PRESS_RELEASE = "press_release"


@dataclass
class ExtractionResult:
    """Result of content extraction with quality metrics."""
    title: str
    content: str
    author: Optional[str] = None
    published_at: Optional[str] = None
    summary: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    images: List[str] = field(default_factory=list)
    links: List[str] = field(default_factory=list)
    
    # Quality metrics
    content_length: int = 0
    quality_score: float = 0.0
    confidence_score: float = 0.0
    extraction_method: str = ""
    
    # Metadata
    language: Optional[str] = None
    content_type: Optional[ContentType] = None
    source_domain: Optional[str] = None
    
    # Raw data
    raw_html: Optional[str] = None
    structured_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExtractorConfig:
    """Configuration for the advanced content extractor."""
    # Selenium settings
    use_selenium: bool = False
    selenium_timeout: int = 30
    headless: bool = True
    user_agent: Optional[str] = None
    
    # Content extraction settings
    min_content_length: int = 100
    max_content_length: int = 50000
    extract_images: bool = True
    extract_links: bool = True
    
    # Quality thresholds
    min_quality_score: float = 0.3
    min_confidence_score: float = 0.5
    
    # PDF processing
    process_pdfs: bool = True
    pdf_max_pages: int = 50
    
    # Retry settings
    max_retries: int = 3
    retry_delay: float = 1.0


class AdvancedContentExtractor:
    """Advanced content extraction with multiple strategies and quality assessment."""
    
    def __init__(self, config: ExtractorConfig = None):
        self.config = config or ExtractorConfig()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.session = requests.Session()
        self.driver = None
        
        # Content selectors for different site types
        self.content_selectors = {
            'article': [
                'article', '[role="main"]', '.content', '.post-content',
                '.entry-content', '.article-content', '.story-body'
            ],
            'title': [
                'h1', '.title', '.headline', '.post-title', '.article-title',
                '[property="og:title"]', 'title'
            ],
            'author': [
                '.author', '.byline', '[rel="author"]', '.post-author',
                '[property="article:author"]', '.writer'
            ],
            'date': [
                'time', '.date', '.published', '.post-date',
                '[property="article:published_time"]', '.timestamp'
            ]
        }
        
        # Quality indicators
        self.quality_indicators = {
            'positive': [
                'article', 'main', 'content', 'post', 'story', 'text',
                'paragraph', 'section', 'chapter'
            ],
            'negative': [
                'nav', 'sidebar', 'footer', 'header', 'menu', 'ad',
                'advertisement', 'popup', 'modal', 'comment'
            ]
        }
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def close(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                self.logger.warning(f"Error closing Selenium driver: {e}")
        
        if self.session:
            self.session.close()
    
    def _setup_selenium(self) -> webdriver.Chrome:
        """Set up Selenium WebDriver with optimal settings."""
        if self.driver:
            return self.driver
        
        try:
            options = Options()
            if self.config.headless:
                options.add_argument('--headless')
            
            # Performance optimizations
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript')  # Can be enabled per request
            
            # User agent
            if self.config.user_agent:
                options.add_argument(f'--user-agent={self.config.user_agent}')
            
            # Memory optimization
            options.add_argument('--memory-pressure-off')
            options.add_argument('--max_old_space_size=4096')
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.set_page_load_timeout(self.config.selenium_timeout)
            
            return self.driver
            
        except Exception as e:
            self.logger.error(f"Failed to setup Selenium driver: {e}")
            raise
    
    def extract_content(self, url: str, html: str = None) -> ExtractionResult:
        """
        Extract content using multiple strategies with quality assessment.
        
        Args:
            url: URL of the content to extract
            html: Optional pre-fetched HTML content
            
        Returns:
            ExtractionResult with extracted content and quality metrics
        """
        self.logger.info(f"Extracting content from: {url}")
        
        # Determine content type from URL
        content_type = self._detect_content_type(url)
        
        # Get HTML if not provided
        if not html:
            html = self._fetch_html(url)
        
        if not html:
            raise ValueError(f"Could not fetch HTML content from {url}")
        
        # Try extraction strategies in order of preference
        strategies = [
            ExtractionStrategy.READABILITY,
            ExtractionStrategy.NEWSPAPER,
            ExtractionStrategy.CUSTOM_SELECTORS,
        ]
        
        # Add Selenium strategy if enabled
        if self.config.use_selenium:
            strategies.insert(-1, ExtractionStrategy.SELENIUM_RENDERED)
        
        best_result = None
        best_score = 0.0
        
        for strategy in strategies:
            try:
                result = self._extract_with_strategy(strategy, url, html)
                if result and result.quality_score > best_score:
                    best_result = result
                    best_score = result.quality_score
                    
                    # If we have a high-quality result, use it
                    if result.quality_score > 0.8:
                        break
                        
            except Exception as e:
                self.logger.warning(f"Strategy {strategy.value} failed for {url}: {e}")
                continue
        
        if not best_result:
            # Fallback to heuristic extraction
            best_result = self._extract_with_strategy(
                ExtractionStrategy.FALLBACK_HEURISTICS, url, html
            )
        
        if best_result:
            # Enhance with additional metadata
            best_result = self._enhance_result(best_result, url, html)
            best_result.content_type = content_type
            
        return best_result

    def _fetch_html(self, url: str) -> Optional[str]:
        """Fetch HTML content from URL with retries."""
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.get(
                    url,
                    timeout=30,
                    headers={
                        'User-Agent': self.config.user_agent or
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                )
                response.raise_for_status()
                return response.text

            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))
                continue

        return None

    def _detect_content_type(self, url: str) -> ContentType:
        """Detect content type based on URL patterns."""
        url_lower = url.lower()

        if 'arxiv.org' in url_lower:
            return ContentType.RESEARCH_PAPER
        elif any(domain in url_lower for domain in ['reddit.com', 'news.ycombinator.com']):
            return ContentType.FORUM_POST
        elif 'blog' in url_lower or any(domain in url_lower for domain in [
            'openai.com', 'ai.googleblog.com', 'ai.facebook.com'
        ]):
            return ContentType.BLOG_POST
        elif '.pdf' in url_lower:
            return ContentType.ACADEMIC_PAPER
        else:
            return ContentType.ARTICLE

    def _extract_with_strategy(self, strategy: ExtractionStrategy, url: str, html: str) -> Optional[ExtractionResult]:
        """Extract content using a specific strategy."""
        try:
            if strategy == ExtractionStrategy.READABILITY and HAS_READABILITY:
                return self._extract_with_readability(url, html)
            elif strategy == ExtractionStrategy.NEWSPAPER and HAS_NEWSPAPER:
                return self._extract_with_newspaper(url, html)
            elif strategy == ExtractionStrategy.CUSTOM_SELECTORS:
                return self._extract_with_selectors(url, html)
            elif strategy == ExtractionStrategy.SELENIUM_RENDERED:
                return self._extract_with_selenium(url)
            elif strategy == ExtractionStrategy.FALLBACK_HEURISTICS:
                return self._extract_with_heuristics(url, html)
            else:
                return None

        except Exception as e:
            self.logger.error(f"Strategy {strategy.value} failed: {e}")
            return None

    def _extract_with_readability(self, url: str, html: str) -> Optional[ExtractionResult]:
        """Extract content using python-readability."""
        try:
            doc = Document(html)

            # Extract content
            content_html = doc.summary()
            content_soup = BeautifulSoup(content_html, 'html.parser')
            content_text = content_soup.get_text(strip=True, separator=' ')

            if len(content_text) < self.config.min_content_length:
                return None

            # Create result
            result = ExtractionResult(
                title=doc.title() or "",
                content=content_text,
                content_length=len(content_text),
                extraction_method="readability",
                raw_html=content_html
            )

            # Calculate quality score
            result.quality_score = self._calculate_quality_score(result, html)
            result.confidence_score = 0.8  # Readability is generally reliable

            return result

        except Exception as e:
            self.logger.debug(f"Readability extraction failed: {e}")
            return None

    def _extract_with_newspaper(self, url: str, html: str) -> Optional[ExtractionResult]:
        """Extract content using newspaper3k."""
        try:
            article = NewspaperArticle(url)
            article.set_html(html)
            article.parse()

            if len(article.text) < self.config.min_content_length:
                return None

            result = ExtractionResult(
                title=article.title or "",
                content=article.text,
                author=", ".join(article.authors) if article.authors else None,
                published_at=article.publish_date.isoformat() if article.publish_date else None,
                summary=article.summary or None,
                images=list(article.images) if self.config.extract_images else [],
                content_length=len(article.text),
                extraction_method="newspaper",
                language=article.meta_lang
            )

            # Calculate quality score
            result.quality_score = self._calculate_quality_score(result, html)
            result.confidence_score = 0.7  # Newspaper is fairly reliable

            return result

        except Exception as e:
            self.logger.debug(f"Newspaper extraction failed: {e}")
            return None

    def _extract_with_selectors(self, url: str, html: str) -> Optional[ExtractionResult]:
        """Extract content using CSS selectors."""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Extract title
            title = self._extract_with_selectors_list(soup, self.content_selectors['title'])

            # Extract content
            content_elements = []
            for selector in self.content_selectors['article']:
                elements = soup.select(selector)
                if elements:
                    content_elements.extend(elements)
                    break

            if not content_elements:
                return None

            # Combine content from all elements
            content_text = ""
            for element in content_elements:
                text = element.get_text(strip=True, separator=' ')
                if text:
                    content_text += text + "\n\n"

            content_text = content_text.strip()

            if len(content_text) < self.config.min_content_length:
                return None

            # Extract other metadata
            author = self._extract_with_selectors_list(soup, self.content_selectors['author'])
            date = self._extract_with_selectors_list(soup, self.content_selectors['date'])

            result = ExtractionResult(
                title=title or "",
                content=content_text,
                author=author,
                published_at=date,
                content_length=len(content_text),
                extraction_method="custom_selectors"
            )

            # Calculate quality score
            result.quality_score = self._calculate_quality_score(result, html)
            result.confidence_score = 0.6  # Selector-based extraction is moderately reliable

            return result

        except Exception as e:
            self.logger.debug(f"Selector extraction failed: {e}")
            return None

    def _extract_with_selenium(self, url: str) -> Optional[ExtractionResult]:
        """Extract content using Selenium for JavaScript-rendered pages."""
        try:
            driver = self._setup_selenium()
            driver.get(url)

            # Wait for content to load
            WebDriverWait(driver, self.config.selenium_timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Additional wait for dynamic content
            time.sleep(2)

            # Get rendered HTML
            html = driver.page_source

            # Use selector-based extraction on rendered HTML
            result = self._extract_with_selectors(url, html)
            if result:
                result.extraction_method = "selenium_rendered"
                result.confidence_score = 0.7  # Selenium is reliable but slower

            return result

        except Exception as e:
            self.logger.debug(f"Selenium extraction failed: {e}")
            return None

    def _extract_with_heuristics(self, url: str, html: str) -> Optional[ExtractionResult]:
        """Fallback extraction using content heuristics."""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Remove unwanted elements
            for element in soup(['script', 'style', 'nav', 'footer', 'header']):
                element.decompose()

            # Find title
            title = ""
            title_element = soup.find('h1') or soup.find('title')
            if title_element:
                title = title_element.get_text(strip=True)

            # Find content by looking for largest text blocks
            text_blocks = []
            for element in soup.find_all(['p', 'div', 'article', 'section']):
                text = element.get_text(strip=True)
                if len(text) > 50:  # Minimum text length
                    text_blocks.append((len(text), text))

            # Sort by length and combine top blocks
            text_blocks.sort(reverse=True)
            content_parts = [block[1] for block in text_blocks[:10]]  # Top 10 blocks
            content = "\n\n".join(content_parts)

            if len(content) < self.config.min_content_length:
                return None

            result = ExtractionResult(
                title=title,
                content=content,
                content_length=len(content),
                extraction_method="fallback_heuristics"
            )

            # Lower quality score for heuristic extraction
            result.quality_score = self._calculate_quality_score(result, html) * 0.5
            result.confidence_score = 0.3  # Low confidence for heuristic extraction

            return result

        except Exception as e:
            self.logger.debug(f"Heuristic extraction failed: {e}")
            return None

    def _extract_with_selectors_list(self, soup: BeautifulSoup, selectors: List[str]) -> Optional[str]:
        """Extract text using a list of CSS selectors."""
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if text:
                    return text
        return None

    def _calculate_quality_score(self, result: ExtractionResult, html: str) -> float:
        """Calculate content quality score based on multiple factors."""
        score = 0.0

        # Content length score (0-0.3)
        length_score = min(result.content_length / 2000, 1.0) * 0.3
        score += length_score

        # Title quality score (0-0.2)
        if result.title:
            title_score = min(len(result.title) / 100, 1.0) * 0.2
            score += title_score

        # Content structure score (0-0.3)
        structure_score = self._assess_content_structure(result.content) * 0.3
        score += structure_score

        # HTML quality score (0-0.2)
        html_score = self._assess_html_quality(html) * 0.2
        score += html_score

        return min(score, 1.0)

    def _assess_content_structure(self, content: str) -> float:
        """Assess content structure quality."""
        if not content:
            return 0.0

        score = 0.0

        # Sentence structure
        sentences = content.split('.')
        if len(sentences) > 3:
            score += 0.3

        # Paragraph structure
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 2:
            score += 0.3

        # Word diversity
        words = content.lower().split()
        unique_words = set(words)
        if len(words) > 0:
            diversity = len(unique_words) / len(words)
            score += diversity * 0.4

        return min(score, 1.0)

    def _assess_html_quality(self, html: str) -> float:
        """Assess HTML structure quality."""
        if not html:
            return 0.0

        soup = BeautifulSoup(html, 'html.parser')
        score = 0.0

        # Presence of semantic elements
        semantic_elements = ['article', 'section', 'header', 'main', 'aside']
        for element in semantic_elements:
            if soup.find(element):
                score += 0.1

        # Presence of metadata
        if soup.find('meta', {'property': 'og:title'}):
            score += 0.1
        if soup.find('meta', {'name': 'description'}):
            score += 0.1

        # Content-to-noise ratio
        content_elements = soup.find_all(['p', 'article', 'section'])
        noise_elements = soup.find_all(['script', 'style', 'nav', 'footer'])

        if len(content_elements) > 0:
            ratio = len(content_elements) / (len(content_elements) + len(noise_elements))
            score += ratio * 0.3

        return min(score, 1.0)

    def _enhance_result(self, result: ExtractionResult, url: str, html: str) -> ExtractionResult:
        """Enhance extraction result with additional metadata and processing."""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Extract additional metadata
            result.source_domain = urlparse(url).netloc

            # Extract images if enabled
            if self.config.extract_images and not result.images:
                result.images = self._extract_images(soup, url)

            # Extract links if enabled
            if self.config.extract_links:
                result.links = self._extract_links(soup, url)

            # Extract structured data
            result.structured_data = self._extract_structured_data(soup)

            # Generate summary if not present
            if not result.summary and result.content:
                result.summary = self._generate_summary(result.content)

            # Extract tags/keywords
            if not result.tags:
                result.tags = self._extract_keywords(result.content, html)

            return result

        except Exception as e:
            self.logger.warning(f"Error enhancing result: {e}")
            return result

    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract image URLs from the page."""
        images = []

        # Find all img tags
        for img in soup.find_all('img'):
            src = img.get('src') or img.get('data-src')
            if src:
                # Convert relative URLs to absolute
                full_url = urljoin(base_url, src)
                images.append(full_url)

        # Limit number of images
        return images[:10]

    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract relevant links from the page."""
        links = []

        # Find all anchor tags
        for link in soup.find_all('a', href=True):
            href = link['href']
            # Convert relative URLs to absolute
            full_url = urljoin(base_url, href)

            # Filter out navigation and social links
            if self._is_content_link(full_url, link.get_text(strip=True)):
                links.append(full_url)

        # Remove duplicates and limit
        return list(set(links))[:20]

    def _is_content_link(self, url: str, link_text: str) -> bool:
        """Determine if a link is content-related."""
        # Skip common navigation patterns
        skip_patterns = [
            'javascript:', 'mailto:', '#', 'tel:',
            'facebook.com', 'twitter.com', 'linkedin.com',
            'instagram.com', 'youtube.com'
        ]

        for pattern in skip_patterns:
            if pattern in url.lower():
                return False

        # Skip short link texts that are likely navigation
        if len(link_text) < 3:
            return False

        return True

    def _extract_structured_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract structured data from the page."""
        structured_data = {}

        # JSON-LD structured data
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            try:
                import json
                data = json.loads(script.string)
                structured_data['json_ld'] = data
                break
            except:
                continue

        # Open Graph metadata
        og_data = {}
        for meta in soup.find_all('meta', property=lambda x: x and x.startswith('og:')):
            property_name = meta.get('property', '').replace('og:', '')
            content = meta.get('content', '')
            if property_name and content:
                og_data[property_name] = content

        if og_data:
            structured_data['open_graph'] = og_data

        # Twitter Card metadata
        twitter_data = {}
        for meta in soup.find_all('meta', attrs={'name': lambda x: x and x.startswith('twitter:')}):
            name = meta.get('name', '').replace('twitter:', '')
            content = meta.get('content', '')
            if name and content:
                twitter_data[name] = content

        if twitter_data:
            structured_data['twitter_card'] = twitter_data

        return structured_data

    def _generate_summary(self, content: str, max_length: int = 300) -> str:
        """Generate a summary from content."""
        if not content:
            return ""

        # Simple extractive summarization
        sentences = content.split('.')
        if len(sentences) <= 3:
            return content[:max_length]

        # Take first few sentences up to max_length
        summary = ""
        for sentence in sentences[:5]:
            sentence = sentence.strip()
            if sentence and len(summary + sentence) < max_length:
                summary += sentence + ". "
            else:
                break

        return summary.strip()

    def _extract_keywords(self, content: str, html: str) -> List[str]:
        """Extract keywords/tags from content."""
        keywords = []

        # Extract from meta keywords
        soup = BeautifulSoup(html, 'html.parser')
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            keywords.extend([k.strip() for k in meta_keywords.get('content', '').split(',')])

        # Simple keyword extraction from content
        if content:
            # AI/ML related keywords
            ai_keywords = [
                'artificial intelligence', 'machine learning', 'deep learning',
                'neural network', 'transformer', 'gpt', 'llm', 'nlp',
                'computer vision', 'reinforcement learning', 'ai model'
            ]

            content_lower = content.lower()
            for keyword in ai_keywords:
                if keyword in content_lower:
                    keywords.append(keyword)

        # Remove duplicates and limit
        return list(set(keywords))[:10]

    def extract_pdf_content(self, pdf_url: str) -> Optional[ExtractionResult]:
        """Extract content from PDF documents."""
        if not HAS_PDF_SUPPORT or not self.config.process_pdfs:
            return None

        try:
            # Download PDF
            response = self.session.get(pdf_url, timeout=60)
            response.raise_for_status()

            # Try PyMuPDF first (better for academic papers)
            try:
                pdf_document = fitz.open(stream=response.content, filetype="pdf")
                text_content = ""

                # Extract text from pages (limit to max_pages)
                max_pages = min(len(pdf_document), self.config.pdf_max_pages)
                for page_num in range(max_pages):
                    page = pdf_document[page_num]
                    text_content += page.get_text() + "\n\n"

                pdf_document.close()

            except Exception:
                # Fallback to PyPDF2
                import io
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(response.content))
                text_content = ""

                max_pages = min(len(pdf_reader.pages), self.config.pdf_max_pages)
                for page_num in range(max_pages):
                    page = pdf_reader.pages[page_num]
                    text_content += page.extract_text() + "\n\n"

            if len(text_content) < self.config.min_content_length:
                return None

            # Extract title from first page or filename
            title = self._extract_pdf_title(text_content, pdf_url)

            result = ExtractionResult(
                title=title,
                content=text_content.strip(),
                content_length=len(text_content),
                extraction_method="pdf_extraction",
                content_type=ContentType.ACADEMIC_PAPER
            )

            # Calculate quality score
            result.quality_score = self._calculate_quality_score(result, "")
            result.confidence_score = 0.8  # PDF extraction is generally reliable

            return result

        except Exception as e:
            self.logger.error(f"PDF extraction failed for {pdf_url}: {e}")
            return None

    def _extract_pdf_title(self, content: str, url: str) -> str:
        """Extract title from PDF content or URL."""
        # Try to find title in first few lines
        lines = content.split('\n')[:10]
        for line in lines:
            line = line.strip()
            if len(line) > 10 and len(line) < 200:
                # Check if it looks like a title
                if not line.lower().startswith(('abstract', 'introduction', 'keywords')):
                    return line

        # Fallback to filename
        from urllib.parse import unquote
        filename = unquote(url.split('/')[-1])
        if filename.endswith('.pdf'):
            filename = filename[:-4]

        return filename.replace('_', ' ').replace('-', ' ').title()
