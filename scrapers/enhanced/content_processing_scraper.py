"""
Enhanced scraper with integrated content processing capabilities.

This scraper extends the base scraper functionality to automatically analyze
scraped articles using the Phase 4 content processing framework.
"""

import logging
from typing import Iterator, Optional, Dict, Any
from dataclasses import dataclass

from ..base.scraper import Base<PERSON><PERSON>raper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from processing import ContentProcessor, ProcessingConfig, ContentAnalysis


@dataclass
class EnhancedScraperConfig(ScraperConfig):
    """Enhanced scraper configuration with content processing options."""
    
    # Content processing settings
    enable_content_processing: bool = True
    enable_sentiment_analysis: bool = True
    enable_topic_categorization: bool = True
    enable_trend_detection: bool = True
    enable_keyword_extraction: bool = True
    
    # Processing performance settings
    parallel_processing: bool = True
    max_workers: int = 2  # Conservative for scraping context
    processing_timeout: int = 30
    min_confidence_threshold: float = 0.3
    
    # Content filtering
    skip_processing_on_low_quality: bool = True
    min_content_length_for_processing: int = 200


class ContentProcessingScraper(BaseScraper):
    """
    Enhanced scraper that automatically processes content using AI analysis.
    
    This scraper extends the base scraper to include:
    - Automatic sentiment analysis
    - Topic categorization
    - Trend detection
    - Keyword extraction
    
    All analysis is performed on scraped articles before they are yielded.
    """
    
    def __init__(self, config: EnhancedScraperConfig):
        super().__init__(config)
        self.enhanced_config = config
        self.content_processor = None
        
        # Initialize content processor if enabled
        if config.enable_content_processing:
            self._initialize_content_processor()
        
        # Enhanced statistics tracking
        self.stats.update({
            'articles_processed': 0,
            'processing_errors': 0,
            'processing_skipped': 0,
            'total_processing_time': 0.0
        })
    
    def _initialize_content_processor(self):
        """Initialize the content processing framework."""
        try:
            processing_config = ProcessingConfig(
                enable_sentiment=self.enhanced_config.enable_sentiment_analysis,
                enable_topics=self.enhanced_config.enable_topic_categorization,
                enable_trends=self.enhanced_config.enable_trend_detection,
                enable_keywords=self.enhanced_config.enable_keyword_extraction,
                parallel_processing=self.enhanced_config.parallel_processing,
                max_workers=self.enhanced_config.max_workers,
                timeout_seconds=self.enhanced_config.processing_timeout,
                min_confidence_threshold=self.enhanced_config.min_confidence_threshold
            )
            
            self.content_processor = ContentProcessor(processing_config)
            self.logger.info("Content processor initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize content processor: {e}")
            self.enhanced_config.enable_content_processing = False
    
    def _should_process_content(self, article: ParsedArticle) -> bool:
        """Determine if an article should be processed."""
        if not self.enhanced_config.enable_content_processing:
            return False
        
        if not self.content_processor:
            return False
        
        # Check content length if filtering is enabled
        if self.enhanced_config.skip_processing_on_low_quality:
            content_length = len(article.content) if article.content else 0
            if content_length < self.enhanced_config.min_content_length_for_processing:
                self.logger.debug(f"Skipping processing for short article: {article.url}")
                return False
        
        return True
    
    def _process_article_content(self, article: ParsedArticle) -> Optional[ContentAnalysis]:
        """Process article content and return analysis results."""
        if not self._should_process_content(article):
            self.stats['processing_skipped'] += 1
            return None
        
        try:
            import time
            start_time = time.time()
            
            # Process the article
            analysis = self.content_processor.process_article(article)
            
            # Update statistics
            processing_time = time.time() - start_time
            self.stats['articles_processed'] += 1
            self.stats['total_processing_time'] += processing_time
            
            self.logger.debug(
                f"Processed article '{article.title[:50]}...' in {processing_time:.2f}s "
                f"(confidence: {analysis.overall_confidence:.3f})"
            )
            
            return analysis
            
        except Exception as e:
            self.stats['processing_errors'] += 1
            self.logger.error(f"Content processing failed for {article.url}: {e}")
            return None
    
    def scrape_all(self) -> Iterator[tuple[ParsedArticle, Optional[ContentAnalysis]]]:
        """
        Scrape all articles and return them with their content analysis.
        
        Returns:
            Iterator of tuples containing (article, analysis) where analysis
            may be None if processing was skipped or failed.
        """
        self.logger.info(f"Starting enhanced scrape for {self.config.name}")
        
        try:
            for url in self.get_article_urls():
                try:
                    article = self.scrape_article(url)
                    if article:
                        if self.is_duplicate(article):
                            self.stats['duplicates_found'] += 1
                            self.logger.debug(f"Duplicate article found: {url}")
                            continue
                        
                        # Process content if enabled
                        analysis = self._process_article_content(article)
                        
                        yield article, analysis
                        
                except Exception as e:
                    self.logger.error(f"Failed to scrape article {url}: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"Enhanced scraping failed: {e}")
            raise ScrapingError(f"Enhanced scraping failed: {str(e)}")
        
        finally:
            stats = self.get_enhanced_stats()
            self.logger.info(f"Enhanced scraping completed. Stats: {stats}")
    
    def get_enhanced_stats(self) -> Dict[str, Any]:
        """Get enhanced statistics including content processing metrics."""
        base_stats = self.get_stats()
        
        # Add processing statistics
        processing_stats = {
            'articles_processed': self.stats['articles_processed'],
            'processing_errors': self.stats['processing_errors'],
            'processing_skipped': self.stats['processing_skipped'],
            'total_processing_time': self.stats['total_processing_time'],
            'avg_processing_time': (
                self.stats['total_processing_time'] / max(1, self.stats['articles_processed'])
            ),
            'processing_success_rate': (
                self.stats['articles_processed'] / 
                max(1, self.stats['articles_processed'] + self.stats['processing_errors'])
            ) * 100
        }
        
        return {**base_stats, **processing_stats}
    
    def close(self):
        """Clean up resources including content processor."""
        super().close()
        
        if self.content_processor:
            try:
                self.content_processor.shutdown()
                self.logger.debug("Content processor shut down successfully")
            except Exception as e:
                self.logger.error(f"Error shutting down content processor: {e}")


# Convenience function to create enhanced scraper configurations
def create_enhanced_scraper_config(
    base_url: str,
    name: str,
    enable_all_processing: bool = True,
    **kwargs
) -> EnhancedScraperConfig:
    """
    Create an enhanced scraper configuration with sensible defaults.
    
    Args:
        base_url: Base URL for the scraper
        name: Name identifier for the scraper
        enable_all_processing: Whether to enable all content processing features
        **kwargs: Additional configuration options
    
    Returns:
        EnhancedScraperConfig instance
    """
    config_defaults = {
        'enable_content_processing': enable_all_processing,
        'enable_sentiment_analysis': enable_all_processing,
        'enable_topic_categorization': enable_all_processing,
        'enable_trend_detection': enable_all_processing,
        'enable_keyword_extraction': enable_all_processing,
        'parallel_processing': True,
        'max_workers': 2,
        'processing_timeout': 30,
        'min_confidence_threshold': 0.3,
        'skip_processing_on_low_quality': True,
        'min_content_length_for_processing': 200
    }
    
    # Merge with provided kwargs
    config_defaults.update(kwargs)
    
    return EnhancedScraperConfig(
        name=name,
        base_url=base_url,
        **config_defaults
    )
