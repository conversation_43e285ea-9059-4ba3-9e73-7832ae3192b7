"""
Enhanced news scraper with integrated content processing.

This scraper extends the basic news scraper to include automatic content analysis.
"""

import logging
from typing import Iterator, Optional, List
from urllib.parse import urljoin, urlparse

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from ..sources.news_scraper import NewsScraper
from .content_processing_scraper import ContentProcessingScraper, EnhancedScraperConfig
from ..base.content_parser import ParsedArticle
from processing import ContentAnalysis


class EnhancedNewsScraper(ContentProcessingScraper):
    """
    Enhanced news scraper that combines RSS/sitemap crawling with content processing.
    
    This scraper:
    1. Discovers articles via RSS feeds or sitemaps
    2. Scrapes article content using configurable selectors
    3. Automatically analyzes content for sentiment, topics, trends, and keywords
    4. Returns both the article and its analysis results
    """
    
    def __init__(self, config: EnhancedScraperConfig):
        super().__init__(config)
        
        # Initialize the underlying news scraper for article discovery
        self._news_scraper = NewsScraper(config)
        
        # Copy over the content parser and other components
        self.content_parser = self._news_scraper.content_parser
        self.rate_limiter = self._news_scraper.rate_limiter
        self.session = self._news_scraper.session
        
        self.logger.info(f"Enhanced news scraper initialized for {config.name}")
    
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs using the underlying news scraper."""
        return self._news_scraper.get_article_urls()
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single article using the underlying news scraper."""
        return self._news_scraper.scrape_article(url)
    
    def close(self):
        """Clean up resources including the underlying news scraper."""
        super().close()
        if hasattr(self, '_news_scraper'):
            self._news_scraper.close()


# Convenience functions for creating enhanced news scrapers
def create_enhanced_news_scraper_from_rss(
    name: str,
    rss_url: str,
    base_url: str,
    article_selectors: dict = None,
    enable_all_processing: bool = True,
    **kwargs
) -> EnhancedNewsScraper:
    """
    Create an enhanced news scraper that discovers articles via RSS feed.
    
    Args:
        name: Scraper name identifier
        rss_url: URL of the RSS feed
        base_url: Base URL of the news site
        article_selectors: CSS selectors for article content extraction
        enable_all_processing: Whether to enable all content processing features
        **kwargs: Additional configuration options
    
    Returns:
        EnhancedNewsScraper instance
    """
    # Default article selectors if none provided
    if article_selectors is None:
        article_selectors = {
            'title': 'h1, .article-title, .entry-title',
            'content': '.article-content, .entry-content, .post-content, article',
            'author': '.author, .byline, [rel="author"]',
            'published_date': '.published, .date, time[datetime]',
            'tags': '.tags a, .categories a, .tag'
        }
    
    config = EnhancedScraperConfig(
        name=name,
        base_url=base_url,
        rss_url=rss_url,
        article_selectors=article_selectors,
        enable_content_processing=enable_all_processing,
        enable_sentiment_analysis=enable_all_processing,
        enable_topic_categorization=enable_all_processing,
        enable_trend_detection=enable_all_processing,
        enable_keyword_extraction=enable_all_processing,
        **kwargs
    )
    
    return EnhancedNewsScraper(config)


def create_enhanced_news_scraper_from_sitemap(
    name: str,
    sitemap_url: str,
    base_url: str,
    article_selectors: dict = None,
    enable_all_processing: bool = True,
    **kwargs
) -> EnhancedNewsScraper:
    """
    Create an enhanced news scraper that discovers articles via sitemap.
    
    Args:
        name: Scraper name identifier
        sitemap_url: URL of the sitemap
        base_url: Base URL of the news site
        article_selectors: CSS selectors for article content extraction
        enable_all_processing: Whether to enable all content processing features
        **kwargs: Additional configuration options
    
    Returns:
        EnhancedNewsScraper instance
    """
    # Default article selectors if none provided
    if article_selectors is None:
        article_selectors = {
            'title': 'h1, .article-title, .entry-title',
            'content': '.article-content, .entry-content, .post-content, article',
            'author': '.author, .byline, [rel="author"]',
            'published_date': '.published, .date, time[datetime]',
            'tags': '.tags a, .categories a, .tag'
        }
    
    config = EnhancedScraperConfig(
        name=name,
        base_url=base_url,
        sitemap_url=sitemap_url,
        article_selectors=article_selectors,
        enable_content_processing=enable_all_processing,
        enable_sentiment_analysis=enable_all_processing,
        enable_topic_categorization=enable_all_processing,
        enable_trend_detection=enable_all_processing,
        enable_keyword_extraction=enable_all_processing,
        **kwargs
    )
    
    return EnhancedNewsScraper(config)


# Pre-configured enhanced scrapers for popular AI news sources
def create_enhanced_techcrunch_scraper(enable_all_processing: bool = True) -> EnhancedNewsScraper:
    """Create an enhanced scraper for TechCrunch AI articles."""
    return create_enhanced_news_scraper_from_rss(
        name="TechCrunch AI",
        rss_url="https://techcrunch.com/category/artificial-intelligence/feed/",
        base_url="https://techcrunch.com",
        article_selectors={
            'title': 'h1.article__title',
            'content': '.article-content',
            'author': '.article__byline a',
            'published_date': '.article__byline time',
            'tags': '.tags a'
        },
        enable_all_processing=enable_all_processing,
        delay_range=(2, 4),  # Be respectful to TechCrunch
        timeout=15
    )


def create_enhanced_venturebeat_scraper(enable_all_processing: bool = True) -> EnhancedNewsScraper:
    """Create an enhanced scraper for VentureBeat AI articles."""
    return create_enhanced_news_scraper_from_rss(
        name="VentureBeat AI",
        rss_url="https://venturebeat.com/ai/feed/",
        base_url="https://venturebeat.com",
        article_selectors={
            'title': 'h1.article-title',
            'content': '.article-content',
            'author': '.author-name',
            'published_date': '.the-time',
            'tags': '.tags a'
        },
        enable_all_processing=enable_all_processing,
        delay_range=(2, 4),
        timeout=15
    )


def create_enhanced_theverge_scraper(enable_all_processing: bool = True) -> EnhancedNewsScraper:
    """Create an enhanced scraper for The Verge AI articles."""
    return create_enhanced_news_scraper_from_rss(
        name="The Verge AI",
        rss_url="https://www.theverge.com/ai-artificial-intelligence/rss/index.xml",
        base_url="https://www.theverge.com",
        article_selectors={
            'title': 'h1[data-testid="headline"]',
            'content': '.duet--article--article-body-component',
            'author': '.byline-author',
            'published_date': 'time',
            'tags': '.tags a'
        },
        enable_all_processing=enable_all_processing,
        delay_range=(2, 4),
        timeout=15
    )
