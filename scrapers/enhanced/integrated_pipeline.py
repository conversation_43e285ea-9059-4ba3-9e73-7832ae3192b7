"""
Integrated scraping pipeline with content processing and database storage.

This module provides a comprehensive pipeline that:
1. Scrapes content from multiple sources (Reddit, Twitter)
2. Processes content with sentiment analysis, topic categorization, and trend detection
3. Stores processed content and analysis results in PostgreSQL database
4. Handles duplicate detection and deduplication
5. Provides monitoring and statistics
"""

import logging
import time
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from scrapers.sources.reddit_scraper import RedditScraper, create_reddit_scraper
from scrapers.sources.twitter_scraper import TwitterScraper, create_twitter_scraper
from scrapers.deduplication.reddit_deduplicator import RedditDeduplicator
from scrapers.deduplication.twitter_deduplicator import TwitterDeduplicator
from scrapers.base.content_parser import ParsedArticle

from processing.base.analyzer import AnalyzerPipeline
from processing.sentiment.analyzer import SentimentAnalyzer
from processing.topics.categorizer import TopicCategorizer
from processing.trends.detector import TrendDetector

from models.connection import db_manager
from models.database import Article, SourceType
from config.settings import settings


@dataclass
class PipelineConfig:
    """Configuration for the integrated scraping pipeline."""
    
    # Source configuration
    enable_reddit: bool = True
    enable_twitter: bool = True
    
    # Reddit settings
    reddit_subreddits: List[str] = field(default_factory=lambda: [
        'MachineLearning', 'artificial', 'deeplearning', 'LanguageTechnology',
        'compsci', 'singularity', 'OpenAI', 'ChatGPT'
    ])
    reddit_max_posts_per_subreddit: int = 50
    
    # Twitter settings
    twitter_hashtags: List[str] = field(default_factory=lambda: [
        '#AI', '#MachineLearning', '#DeepLearning', '#LLM', '#OpenAI', '#ChatGPT'
    ])
    twitter_user_accounts: List[str] = field(default_factory=lambda: [
        'OpenAI', 'AnthropicAI', 'GoogleAI', 'DeepMind', 'huggingface'
    ])
    twitter_max_tweets_per_hashtag: int = 30
    
    # Processing settings
    enable_content_processing: bool = True
    enable_sentiment_analysis: bool = True
    enable_topic_categorization: bool = True
    enable_trend_detection: bool = True
    
    # Database settings
    save_to_database: bool = True
    enable_duplicate_detection: bool = True
    min_content_length: int = 50  # Minimum content length to process
    
    # Performance settings
    max_workers: int = 4
    batch_size: int = 10
    processing_timeout_seconds: int = 300


class IntegratedScrapingPipeline:
    """Comprehensive scraping pipeline with content processing and database storage."""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.logger = logging.getLogger("pipeline.integrated")
        
        # Initialize components
        self.scrapers: Dict[str, Union[RedditScraper, TwitterScraper]] = {}
        self.deduplicators: Dict[str, Union[RedditDeduplicator, TwitterDeduplicator]] = {}
        self.content_processor: Optional[AnalyzerPipeline] = None
        
        # Thread pool for concurrent processing
        self.executor = ThreadPoolExecutor(max_workers=config.max_workers)
        
        # Statistics
        self.stats = {
            'pipeline_start_time': None,
            'articles_scraped': 0,
            'articles_processed': 0,
            'articles_saved': 0,
            'duplicates_detected': 0,
            'processing_errors': 0,
            'database_errors': 0,
            'source_stats': {},
            'processing_time_ms': 0,
            'average_processing_time_ms': 0
        }
        
        # Callbacks
        self.article_callbacks: List[Callable[[ParsedArticle], None]] = []
        self.analysis_callbacks: List[Callable[[ParsedArticle, Any], None]] = []
        
        self._initialize_components()
    
    def add_article_callback(self, callback: Callable[[ParsedArticle], None]):
        """Add callback for processed articles."""
        self.article_callbacks.append(callback)
    
    def add_analysis_callback(self, callback: Callable[[ParsedArticle, Any], None]):
        """Add callback for content analysis results."""
        self.analysis_callbacks.append(callback)
    
    def _initialize_components(self):
        """Initialize all pipeline components."""
        try:
            self.logger.info("Initializing integrated scraping pipeline...")
            
            # Initialize scrapers
            if self.config.enable_reddit:
                self.scrapers['reddit'] = create_reddit_scraper(
                    subreddits=self.config.reddit_subreddits,
                    max_posts_per_subreddit=self.config.reddit_max_posts_per_subreddit
                )
                self.deduplicators['reddit'] = RedditDeduplicator()
                self.logger.info("Reddit scraper initialized")
            
            if self.config.enable_twitter:
                self.scrapers['twitter'] = create_twitter_scraper(
                    hashtags=self.config.twitter_hashtags,
                    user_accounts=self.config.twitter_user_accounts,
                    max_tweets_per_hashtag=self.config.twitter_max_tweets_per_hashtag
                )
                self.deduplicators['twitter'] = TwitterDeduplicator()
                self.logger.info("Twitter scraper initialized")
            
            # Initialize content processing pipeline
            if self.config.enable_content_processing:
                self.content_processor = AnalyzerPipeline([])
                
                if self.config.enable_sentiment_analysis:
                    sentiment_analyzer = SentimentAnalyzer()
                    sentiment_analyzer.initialize()
                    self.content_processor.add_analyzer(sentiment_analyzer)
                
                if self.config.enable_topic_categorization:
                    topic_categorizer = TopicCategorizer()
                    topic_categorizer.initialize()
                    self.content_processor.add_analyzer(topic_categorizer)
                
                if self.config.enable_trend_detection:
                    trend_detector = TrendDetector()
                    trend_detector.initialize()
                    self.content_processor.add_analyzer(trend_detector)
                
                self.logger.info("Content processing pipeline initialized")
            
            self.logger.info("Pipeline initialization completed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize pipeline: {e}")
            raise
    
    def run_pipeline(self) -> Dict[str, Any]:
        """Run the complete scraping and processing pipeline."""
        self.logger.info("Starting integrated scraping pipeline...")
        self.stats['pipeline_start_time'] = time.time()
        
        try:
            # Step 1: Scrape content from all sources
            all_articles = self._scrape_all_sources()
            self.logger.info(f"Scraped {len(all_articles)} articles from all sources")
            
            # Step 2: Detect and remove duplicates
            if self.config.enable_duplicate_detection:
                unique_articles = self._deduplicate_articles(all_articles)
                self.logger.info(f"After deduplication: {len(unique_articles)} unique articles")
            else:
                unique_articles = all_articles
            
            # Step 3: Process content in batches
            processed_articles = []
            if self.config.enable_content_processing and self.content_processor:
                processed_articles = self._process_articles_batch(unique_articles)
                self.logger.info(f"Processed {len(processed_articles)} articles")
            else:
                processed_articles = [(article, None) for article in unique_articles]
            
            # Step 4: Save to database
            if self.config.save_to_database:
                saved_count = self._save_articles_to_database(processed_articles)
                self.logger.info(f"Saved {saved_count} articles to database")
            
            # Update statistics
            self._update_final_stats()
            
            self.logger.info("Pipeline execution completed successfully")
            return self.get_pipeline_stats()
            
        except Exception as e:
            self.logger.error(f"Pipeline execution failed: {e}")
            raise
    
    def _scrape_all_sources(self) -> List[ParsedArticle]:
        """Scrape content from all configured sources."""
        all_articles = []
        
        # Use thread pool for concurrent scraping
        futures = []
        
        for source_name, scraper in self.scrapers.items():
            future = self.executor.submit(self._scrape_source, source_name, scraper)
            futures.append(future)
        
        # Collect results
        for future in as_completed(futures):
            try:
                source_articles = future.result(timeout=self.config.processing_timeout_seconds)
                all_articles.extend(source_articles)
            except Exception as e:
                self.logger.error(f"Error scraping source: {e}")
                self.stats['processing_errors'] += 1
        
        self.stats['articles_scraped'] = len(all_articles)
        return all_articles
    
    def _scrape_source(self, source_name: str, scraper: Union[RedditScraper, TwitterScraper]) -> List[ParsedArticle]:
        """Scrape content from a single source."""
        self.logger.info(f"Scraping {source_name}...")
        articles = []
        
        try:
            for article in scraper.scrape_all():
                # Filter by minimum content length
                if len(article.content or '') >= self.config.min_content_length:
                    articles.append(article)
                    
                    # Call article callbacks
                    for callback in self.article_callbacks:
                        try:
                            callback(article)
                        except Exception as e:
                            self.logger.error(f"Error in article callback: {e}")
            
            self.stats['source_stats'][source_name] = {
                'articles_scraped': len(articles),
                'scraper_stats': scraper.get_stats()
            }
            
        except Exception as e:
            self.logger.error(f"Error scraping {source_name}: {e}")
            self.stats['processing_errors'] += 1
        
        return articles
    
    def _deduplicate_articles(self, articles: List[ParsedArticle]) -> List[ParsedArticle]:
        """Remove duplicate articles using source-specific deduplicators."""
        unique_articles = []
        
        # Group articles by source type
        articles_by_source = {}
        for article in articles:
            source_type = self._get_source_type(article)
            if source_type not in articles_by_source:
                articles_by_source[source_type] = []
            articles_by_source[source_type].append(article)
        
        # Deduplicate each source type separately
        for source_type, source_articles in articles_by_source.items():
            if source_type in self.deduplicators:
                deduplicator = self.deduplicators[source_type]
                
                # Convert articles to format expected by deduplicator
                article_dicts = []
                for article in source_articles:
                    article_dict = {
                        'id': article.url,
                        'title': article.title,
                        'content': article.content,
                        'url': article.url,
                        'author': article.author,
                        'created_utc': article.published_at or datetime.now(timezone.utc)
                    }
                    article_dicts.append(article_dict)
                
                # Find duplicates
                duplicates_found = 0
                for article_dict in article_dicts:
                    duplicates = deduplicator.find_duplicates(article_dict)
                    if not duplicates:  # No duplicates found, keep the article
                        # Find corresponding ParsedArticle
                        for orig_article in source_articles:
                            if orig_article.url == article_dict['url']:
                                unique_articles.append(orig_article)
                                break
                    else:
                        duplicates_found += 1
                
                self.stats['duplicates_detected'] += duplicates_found
                self.logger.info(f"Removed {duplicates_found} duplicates from {source_type}")
            else:
                # No deduplicator available, keep all articles
                unique_articles.extend(source_articles)
        
        return unique_articles
    
    def _get_source_type(self, article: ParsedArticle) -> str:
        """Determine source type from article URL."""
        if 'reddit.com' in article.url:
            return 'reddit'
        elif 'twitter.com' in article.url or 'x.com' in article.url:
            return 'twitter'
        else:
            return 'unknown'
    
    def _process_articles_batch(self, articles: List[ParsedArticle]) -> List[tuple]:
        """Process articles in batches with content analysis."""
        processed_articles = []
        
        # Process in batches
        for i in range(0, len(articles), self.config.batch_size):
            batch = articles[i:i + self.config.batch_size]
            batch_results = self._process_batch(batch)
            processed_articles.extend(batch_results)
        
        self.stats['articles_processed'] = len(processed_articles)
        return processed_articles
    
    def _process_batch(self, articles: List[ParsedArticle]) -> List[tuple]:
        """Process a batch of articles."""
        batch_results = []
        
        # Use thread pool for concurrent processing
        futures = []
        for article in articles:
            future = self.executor.submit(self._process_single_article, article)
            futures.append((future, article))
        
        # Collect results
        for future, article in futures:
            try:
                analysis = future.result(timeout=30)  # 30 second timeout per article
                batch_results.append((article, analysis))
                
                # Call analysis callbacks
                for callback in self.analysis_callbacks:
                    try:
                        callback(article, analysis)
                    except Exception as e:
                        self.logger.error(f"Error in analysis callback: {e}")
                        
            except Exception as e:
                self.logger.error(f"Error processing article {article.url}: {e}")
                self.stats['processing_errors'] += 1
                batch_results.append((article, None))
        
        return batch_results
    
    def _process_single_article(self, article: ParsedArticle):
        """Process a single article with content analysis."""
        start_time = time.time()
        
        try:
            analysis = self.content_processor.analyze(article)
            
            # Update processing time statistics
            processing_time = (time.time() - start_time) * 1000  # Convert to ms
            self.stats['processing_time_ms'] += processing_time
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing article {article.url}: {e}")
            raise
    
    def _save_articles_to_database(self, processed_articles: List[tuple]) -> int:
        """Save processed articles to database."""
        saved_count = 0
        
        try:
            with db_manager.get_session() as session:
                for article, analysis in processed_articles:
                    try:
                        # Check if article already exists
                        existing = session.query(Article).filter_by(url=article.url).first()
                        if existing:
                            self.logger.debug(f"Article already exists: {article.url}")
                            continue
                        
                        # Create new article record
                        db_article = self._create_db_article(article, analysis)
                        session.add(db_article)
                        saved_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"Error saving article {article.url}: {e}")
                        self.stats['database_errors'] += 1
                
                session.commit()
                self.logger.info(f"Committed {saved_count} articles to database")
                
        except Exception as e:
            self.logger.error(f"Database transaction failed: {e}")
            self.stats['database_errors'] += 1
            raise
        
        self.stats['articles_saved'] = saved_count
        return saved_count
    
    def _create_db_article(self, article: ParsedArticle, analysis) -> Article:
        """Create a database Article instance from parsed article and analysis."""
        # Determine source type
        source_type = SourceType.REDDIT if 'reddit.com' in article.url else SourceType.TWITTER
        
        db_article = Article(
            title=article.title,
            content=article.content,
            url=article.url,
            source_type=source_type,
            author=article.author,
            published_at=article.published_at,
            scraped_at=datetime.now(timezone.utc)
        )
        
        # Add analysis results if available
        if analysis:
            self._add_analysis_to_db_article(db_article, analysis)
        
        return db_article
    
    def _add_analysis_to_db_article(self, db_article: Article, analysis):
        """Add content analysis results to database article."""
        try:
            from processing.base.analyzer import AnalysisType
            
            # Add sentiment analysis
            sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
            if sentiment_result:
                db_article.sentiment_score = sentiment_result.data.get('score', 0)
                db_article.sentiment_label = sentiment_result.data.get('label', 'neutral')
                db_article.sentiment_confidence = sentiment_result.confidence
            
            # Add topic categorization
            topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
            if topic_results:
                db_article.topics_analysis = [
                    {
                        'category': result.data.get('category', 'unknown'),
                        'confidence': result.confidence,
                        'subcategory': result.data.get('subcategory', '')
                    }
                    for result in topic_results
                ]
            
            # Add trend detection
            trend_results = analysis.get_results_by_type(AnalysisType.TREND)
            if trend_results:
                db_article.trends_analysis = [
                    {
                        'trend_type': result.data.get('trend_type', 'unknown'),
                        'description': result.data.get('description', ''),
                        'confidence': result.confidence,
                        'timeframe': result.data.get('timeframe', '')
                    }
                    for result in trend_results
                ]
            
            # Set overall analysis metadata
            db_article.analysis_confidence = analysis.get_overall_confidence()
            db_article.analysis_timestamp = datetime.now(timezone.utc)
            
        except Exception as e:
            self.logger.error(f"Error adding analysis to article: {e}")
    
    def _update_final_stats(self):
        """Update final pipeline statistics."""
        if self.stats['articles_processed'] > 0:
            self.stats['average_processing_time_ms'] = (
                self.stats['processing_time_ms'] / self.stats['articles_processed']
            )
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        return {
            **self.stats,
            'config': {
                'enable_reddit': self.config.enable_reddit,
                'enable_twitter': self.config.enable_twitter,
                'enable_content_processing': self.config.enable_content_processing,
                'save_to_database': self.config.save_to_database
            },
            'content_processor_stats': (
                self.content_processor.get_pipeline_stats() 
                if self.content_processor else None
            )
        }
    
    def shutdown(self):
        """Shutdown the pipeline and cleanup resources."""
        self.logger.info("Shutting down integrated pipeline...")
        self.executor.shutdown(wait=True)
        self.logger.info("Pipeline shutdown completed")


def create_integrated_pipeline(
    enable_reddit: bool = True,
    enable_twitter: bool = True,
    enable_content_processing: bool = True,
    save_to_database: bool = True
) -> IntegratedScrapingPipeline:
    """Factory function to create an integrated scraping pipeline."""
    
    config = PipelineConfig(
        enable_reddit=enable_reddit,
        enable_twitter=enable_twitter,
        enable_content_processing=enable_content_processing,
        save_to_database=save_to_database
    )
    
    return IntegratedScrapingPipeline(config)
