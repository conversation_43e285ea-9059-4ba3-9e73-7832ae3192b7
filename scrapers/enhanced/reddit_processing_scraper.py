"""
Enhanced Reddit scraper with integrated content processing.

This module extends the base Reddit scraper with automatic content analysis
and processing capabilities, providing comprehensive Reddit post analysis.
"""

import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

# Add project root to path for processing imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from scrapers.sources.reddit_scraper import RedditScraper, RedditScraperConfig
from scrapers.base.content_parser import ParsedArticle
from scrapers.base.exceptions import ScrapingError
from processing import ContentProcessor, ProcessingConfig, ContentAnalysis
from models.database import Article, SourceType
from models.connection import db_manager
from config.settings import settings


class EnhancedRedditScraperConfig(RedditScraperConfig):
    """Configuration for enhanced Reddit scraper with processing."""
    
    # Processing settings
    enable_processing: bool = True
    processing_timeout: int = 30
    min_content_length: int = 50
    
    # Quality filtering
    min_score_for_processing: int = 5
    skip_deleted_posts: bool = True
    skip_nsfw_posts: bool = True


class RedditProcessingScraper(RedditScraper):
    """Enhanced Reddit scraper with automatic content processing."""
    
    def __init__(self, config: EnhancedRedditScraperConfig):
        super().__init__(config)
        self.config: EnhancedRedditScraperConfig = config
        
        # Initialize content processor if enabled
        self.processor = None
        if config.enable_processing:
            self._setup_content_processor()
        
        # Enhanced statistics
        self.processing_stats = {
            'articles_processed': 0,
            'processing_successes': 0,
            'processing_failures': 0,
            'articles_saved': 0,
            'duplicate_articles': 0
        }
    
    def _setup_content_processor(self):
        """Initialize the content processor."""
        try:
            processing_config = ProcessingConfig(
                timeout=self.config.processing_timeout,
                min_content_length=self.config.min_content_length
            )
            self.processor = ContentProcessor(processing_config)
            self.logger.info("Content processor initialized for Reddit scraper")
        except Exception as e:
            self.logger.error(f"Failed to initialize content processor: {e}")
            self.config.enable_processing = False
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape Reddit post with enhanced processing."""
        # Get the base article
        article = super().scrape_article(url)
        
        if not article:
            return None
        
        # Apply quality filters
        if not self._passes_quality_filters(article):
            return None
        
        # Process the article if enabled
        if self.config.enable_processing and self.processor:
            try:
                analysis = self.processor.process_content(article)
                if analysis:
                    # Store the analysis in the article metadata
                    if not hasattr(article, 'metadata'):
                        article.metadata = {}
                    article.metadata['content_analysis'] = analysis
                    self.processing_stats['processing_successes'] += 1
                else:
                    self.processing_stats['processing_failures'] += 1
                
                self.processing_stats['articles_processed'] += 1
                
            except Exception as e:
                self.logger.error(f"Content processing failed for {url}: {e}")
                self.processing_stats['processing_failures'] += 1
        
        return article
    
    def _passes_quality_filters(self, article: ParsedArticle) -> bool:
        """Check if article passes quality filters."""
        # Check content length
        if len(article.content) < self.config.min_content_length:
            return False
        
        # Check Reddit-specific metadata
        if hasattr(article, 'metadata') and article.metadata:
            metadata = article.metadata
            
            # Check score threshold
            score = metadata.get('score', 0)
            if score < self.config.min_score_for_processing:
                return False
            
            # Skip deleted posts
            if self.config.skip_deleted_posts and metadata.get('author') == 'deleted':
                return False
            
            # Skip NSFW posts
            if self.config.skip_nsfw_posts and metadata.get('over_18', False):
                return False
        
        return True
    
    def scrape_and_save(self, url: str) -> Optional[str]:
        """Scrape Reddit post and save to database with analysis."""
        try:
            # Scrape the article
            article = self.scrape_article(url)
            if not article:
                return None
            
            # Check for duplicates
            with db_manager.get_session_sync() as session:
                existing = session.query(Article).filter_by(url=article.url).first()
                if existing:
                    self.logger.debug(f"Duplicate article found: {article.url}")
                    self.processing_stats['duplicate_articles'] += 1
                    return existing.id
            
            # Create database article
            db_article = Article(
                title=article.title,
                content=article.content,
                url=article.url,
                source_type=SourceType.REDDIT,
                author=article.author,
                published_at=article.published_at,
                scraped_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Add analysis results if available
            if (hasattr(article, 'metadata') and 
                article.metadata and 
                'content_analysis' in article.metadata):
                
                analysis = article.metadata['content_analysis']
                self._add_analysis_to_article(db_article, analysis)
            
            # Add Reddit-specific metadata
            if hasattr(article, 'metadata') and article.metadata:
                reddit_metadata = {k: v for k, v in article.metadata.items() 
                                 if k != 'content_analysis'}
                # Store Reddit metadata in a JSON field if available
                if hasattr(db_article, 'source_metadata'):
                    db_article.source_metadata = reddit_metadata
            
            # Save to database
            with db_manager.get_session_sync() as session:
                session.add(db_article)
                session.commit()
                session.refresh(db_article)
                
                self.processing_stats['articles_saved'] += 1
                self.logger.info(f"Saved Reddit article: {db_article.id}")
                
                return db_article.id
                
        except Exception as e:
            self.logger.error(f"Failed to scrape and save {url}: {e}")
            return None
    
    def _add_analysis_to_article(self, db_article: Article, analysis: ContentAnalysis):
        """Add content analysis results to database article."""
        try:
            from processing.base.analyzer import AnalysisType
            
            # Add sentiment analysis
            sentiment_result = analysis.get_result(AnalysisType.SENTIMENT)
            if sentiment_result:
                db_article.sentiment_score = sentiment_result.data.get('score', 0)
                db_article.sentiment_label = sentiment_result.data.get('label', 'neutral')
                db_article.sentiment_confidence = sentiment_result.confidence
            
            # Add topic analysis
            topic_results = analysis.get_results_by_type(AnalysisType.TOPIC)
            if topic_results:
                db_article.topics_analysis = [
                    {
                        'category': result.data.get('category', 'unknown'),
                        'confidence': result.confidence,
                        'subcategory': result.data.get('subcategory', '')
                    }
                    for result in topic_results
                ]
            
            # Add trend analysis
            trend_results = analysis.get_results_by_type(AnalysisType.TREND)
            if trend_results:
                db_article.trends_analysis = [
                    {
                        'trend_type': result.data.get('trend_type', 'unknown'),
                        'description': result.data.get('description', ''),
                        'confidence': result.confidence,
                        'timeframe': result.data.get('timeframe', '')
                    }
                    for result in trend_results
                ]
            
            # Add keyword analysis
            keyword_results = analysis.get_results_by_type(AnalysisType.KEYWORD)
            if keyword_results:
                db_article.keywords_analysis = [
                    {
                        'keyword': result.data.get('keyword', 'unknown'),
                        'relevance': result.data.get('relevance', 0),
                        'category': result.data.get('category', '')
                    }
                    for result in keyword_results
                ]
            
            # Set overall analysis metadata
            db_article.analysis_confidence = analysis.overall_confidence
            db_article.analysis_timestamp = datetime.utcnow()
            
        except Exception as e:
            self.logger.error(f"Failed to add analysis to article: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive scraping and processing statistics."""
        base_stats = super().get_stats()
        return {
            **base_stats,
            **self.processing_stats,
            'processing_enabled': self.config.enable_processing,
            'quality_filters': {
                'min_score': self.config.min_score_for_processing,
                'min_content_length': self.config.min_content_length,
                'skip_deleted': self.config.skip_deleted_posts,
                'skip_nsfw': self.config.skip_nsfw_posts
            }
        }
    
    def shutdown(self):
        """Clean up resources."""
        super().shutdown()
        if self.processor:
            # Content processor doesn't need explicit cleanup
            self.processor = None


def create_enhanced_reddit_scraper(
    subreddits: Optional[List[str]] = None,
    max_posts_per_subreddit: int = 50,
    include_comments: bool = True,
    enable_processing: bool = True
) -> RedditProcessingScraper:
    """Factory function to create an enhanced Reddit scraper."""
    
    config = EnhancedRedditScraperConfig(
        name="Enhanced Reddit AI/ML Scraper",
        base_url="https://reddit.com",
        subreddits=subreddits or [
            'MachineLearning', 'artificial', 'ArtificialIntelligence',
            'deeplearning', 'MLQuestions', 'OpenAI', 'ChatGPT', 'LocalLLaMA'
        ],
        max_posts_per_subreddit=max_posts_per_subreddit,
        include_comments=include_comments,
        enable_processing=enable_processing,
        min_score_for_processing=5,
        min_content_length=100
    )
    
    return RedditProcessingScraper(config)
