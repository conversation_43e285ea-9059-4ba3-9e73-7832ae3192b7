"""
Reddit monitoring system for AI/ML subreddits.

This module provides comprehensive monitoring capabilities for Reddit,
including real-time monitoring, scheduled scraping, and trend detection.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import threading

from scrapers.enhanced.reddit_processing_scraper import (
    RedditProcessingScraper, 
    EnhancedRedditScraperConfig,
    create_enhanced_reddit_scraper
)
from models.connection import db_manager
from models.database import Article
from config.settings import settings


@dataclass
class SubredditMonitorConfig:
    """Configuration for subreddit monitoring."""
    
    # Target subreddits with priorities
    subreddits: Dict[str, int] = field(default_factory=lambda: {
        'MachineLearning': 1,      # Highest priority
        'artificial': 1,
        'ArtificialIntelligence': 1,
        'deeplearning': 1,
        'MLQuestions': 2,          # Medium priority
        'OpenAI': 2,
        'ChatGPT': 2,
        'LocalLLaMA': 2,
        'compsci': 3,              # Lower priority
        'programming': 3,
        'technology': 3,
        'singularity': 3
    })
    
    # Monitoring intervals (in minutes)
    high_priority_interval: int = 15    # Every 15 minutes
    medium_priority_interval: int = 30  # Every 30 minutes
    low_priority_interval: int = 60     # Every hour
    
    # Scraping limits per interval
    max_posts_per_high_priority: int = 25
    max_posts_per_medium_priority: int = 15
    max_posts_per_low_priority: int = 10
    
    # Quality thresholds
    min_score_threshold: int = 5
    max_age_hours: int = 24
    
    # Processing settings
    enable_real_time_processing: bool = True
    max_concurrent_scrapers: int = 3
    
    # Monitoring control
    enable_monitoring: bool = True
    log_stats_interval: int = 300  # Log stats every 5 minutes


class RedditMonitor:
    """Comprehensive Reddit monitoring system."""
    
    def __init__(self, config: SubredditMonitorConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Monitoring state
        self.is_running = False
        self.monitor_thread = None
        self.scrapers: Dict[int, RedditProcessingScraper] = {}
        self.last_check_times: Dict[str, datetime] = {}
        self.seen_posts: Set[str] = set()
        
        # Statistics
        self.stats = {
            'monitoring_started': None,
            'total_posts_found': 0,
            'total_posts_processed': 0,
            'total_articles_saved': 0,
            'subreddit_stats': {},
            'errors': 0,
            'last_stats_log': datetime.now()
        }
        
        # Thread pool for concurrent scraping
        self.executor = ThreadPoolExecutor(max_workers=config.max_concurrent_scrapers)
        
        # Initialize scrapers for different priority levels
        self._setup_scrapers()
    
    def _setup_scrapers(self):
        """Initialize scrapers for different priority levels."""
        try:
            # High priority scraper
            self.scrapers[1] = create_enhanced_reddit_scraper(
                subreddits=self._get_subreddits_by_priority(1),
                max_posts_per_subreddit=self.config.max_posts_per_high_priority,
                include_comments=True,
                enable_processing=self.config.enable_real_time_processing
            )
            
            # Medium priority scraper
            self.scrapers[2] = create_enhanced_reddit_scraper(
                subreddits=self._get_subreddits_by_priority(2),
                max_posts_per_subreddit=self.config.max_posts_per_medium_priority,
                include_comments=True,
                enable_processing=self.config.enable_real_time_processing
            )
            
            # Low priority scraper
            self.scrapers[3] = create_enhanced_reddit_scraper(
                subreddits=self._get_subreddits_by_priority(3),
                max_posts_per_subreddit=self.config.max_posts_per_low_priority,
                include_comments=False,  # Skip comments for low priority
                enable_processing=self.config.enable_real_time_processing
            )
            
            self.logger.info("Reddit scrapers initialized for all priority levels")
            
        except Exception as e:
            self.logger.error(f"Failed to setup Reddit scrapers: {e}")
            raise
    
    def _get_subreddits_by_priority(self, priority: int) -> List[str]:
        """Get subreddits by priority level."""
        return [subreddit for subreddit, prio in self.config.subreddits.items() 
                if prio == priority]
    
    def start_monitoring(self):
        """Start the Reddit monitoring system."""
        if self.is_running:
            self.logger.warning("Reddit monitoring is already running")
            return
        
        self.logger.info("Starting Reddit monitoring system")
        self.is_running = True
        self.stats['monitoring_started'] = datetime.now()
        
        # Start monitoring in a separate thread
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Reddit monitoring started successfully")
    
    def stop_monitoring(self):
        """Stop the Reddit monitoring system."""
        if not self.is_running:
            return
        
        self.logger.info("Stopping Reddit monitoring system")
        self.is_running = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        
        # Shutdown scrapers
        for scraper in self.scrapers.values():
            scraper.shutdown()
        
        # Shutdown thread pool
        self.executor.shutdown(wait=True)
        
        self.logger.info("Reddit monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Check each priority level
                for priority in [1, 2, 3]:
                    if self._should_check_priority(priority, current_time):
                        self._monitor_priority_subreddits(priority)
                
                # Log statistics periodically
                if (current_time - self.stats['last_stats_log']).seconds >= self.config.log_stats_interval:
                    self._log_statistics()
                    self.stats['last_stats_log'] = current_time
                
                # Sleep for a short interval
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                self.stats['errors'] += 1
                time.sleep(60)  # Wait longer on error
    
    def _should_check_priority(self, priority: int, current_time: datetime) -> bool:
        """Check if it's time to monitor subreddits of given priority."""
        last_check = self.last_check_times.get(f"priority_{priority}")
        
        if not last_check:
            return True
        
        intervals = {
            1: self.config.high_priority_interval,
            2: self.config.medium_priority_interval,
            3: self.config.low_priority_interval
        }
        
        interval_minutes = intervals.get(priority, 60)
        time_since_last = current_time - last_check
        
        return time_since_last >= timedelta(minutes=interval_minutes)
    
    def _monitor_priority_subreddits(self, priority: int):
        """Monitor subreddits of a specific priority level."""
        try:
            scraper = self.scrapers.get(priority)
            if not scraper:
                return
            
            subreddits = self._get_subreddits_by_priority(priority)
            self.logger.info(f"Monitoring priority {priority} subreddits: {subreddits}")
            
            # Submit scraping tasks to thread pool
            futures = []
            for url in scraper.get_article_urls():
                if url not in self.seen_posts:
                    self.seen_posts.add(url)
                    future = self.executor.submit(self._process_post, scraper, url, priority)
                    futures.append(future)
                    self.stats['total_posts_found'] += 1
            
            # Update last check time
            self.last_check_times[f"priority_{priority}"] = datetime.now()
            
            self.logger.debug(f"Submitted {len(futures)} posts for processing from priority {priority}")
            
        except Exception as e:
            self.logger.error(f"Error monitoring priority {priority} subreddits: {e}")
            self.stats['errors'] += 1
    
    def _process_post(self, scraper: RedditProcessingScraper, url: str, priority: int) -> Optional[str]:
        """Process a single Reddit post."""
        try:
            article_id = scraper.scrape_and_save(url)
            if article_id:
                self.stats['total_articles_saved'] += 1
                self.logger.debug(f"Saved article {article_id} from {url}")
            
            self.stats['total_posts_processed'] += 1
            return article_id
            
        except Exception as e:
            self.logger.error(f"Error processing post {url}: {e}")
            self.stats['errors'] += 1
            return None
    
    def _log_statistics(self):
        """Log monitoring statistics."""
        uptime = datetime.now() - self.stats['monitoring_started'] if self.stats['monitoring_started'] else timedelta(0)
        
        self.logger.info("Reddit Monitoring Statistics:")
        self.logger.info(f"  Uptime: {uptime}")
        self.logger.info(f"  Posts found: {self.stats['total_posts_found']}")
        self.logger.info(f"  Posts processed: {self.stats['total_posts_processed']}")
        self.logger.info(f"  Articles saved: {self.stats['total_articles_saved']}")
        self.logger.info(f"  Errors: {self.stats['errors']}")
        self.logger.info(f"  Unique posts seen: {len(self.seen_posts)}")
        
        # Log scraper statistics
        for priority, scraper in self.scrapers.items():
            scraper_stats = scraper.get_stats()
            self.logger.info(f"  Priority {priority} scraper: {scraper_stats.get('articles_saved', 0)} saved")
    
    def get_monitoring_stats(self) -> Dict:
        """Get comprehensive monitoring statistics."""
        stats = dict(self.stats)
        
        # Add scraper statistics
        stats['scraper_stats'] = {}
        for priority, scraper in self.scrapers.items():
            stats['scraper_stats'][f'priority_{priority}'] = scraper.get_stats()
        
        # Add configuration info
        stats['config'] = {
            'subreddits': self.config.subreddits,
            'intervals': {
                'high_priority': self.config.high_priority_interval,
                'medium_priority': self.config.medium_priority_interval,
                'low_priority': self.config.low_priority_interval
            },
            'processing_enabled': self.config.enable_real_time_processing
        }
        
        return stats
    
    def force_check_all(self):
        """Force an immediate check of all subreddits."""
        self.logger.info("Forcing immediate check of all subreddits")
        
        for priority in [1, 2, 3]:
            self._monitor_priority_subreddits(priority)
    
    def add_subreddit(self, subreddit: str, priority: int = 2):
        """Add a new subreddit to monitor."""
        self.config.subreddits[subreddit] = priority
        self.logger.info(f"Added subreddit r/{subreddit} with priority {priority}")
        
        # Recreate scrapers to include new subreddit
        self._setup_scrapers()
    
    def remove_subreddit(self, subreddit: str):
        """Remove a subreddit from monitoring."""
        if subreddit in self.config.subreddits:
            del self.config.subreddits[subreddit]
            self.logger.info(f"Removed subreddit r/{subreddit} from monitoring")
            
            # Recreate scrapers
            self._setup_scrapers()


def create_reddit_monitor(
    custom_subreddits: Optional[Dict[str, int]] = None,
    enable_processing: bool = True
) -> RedditMonitor:
    """Factory function to create a Reddit monitor."""
    
    config = SubredditMonitorConfig(
        subreddits=custom_subreddits or SubredditMonitorConfig().subreddits,
        enable_real_time_processing=enable_processing
    )
    
    return RedditMonitor(config)
