"""
Twitter monitoring system for AI/ML content.

This module provides comprehensive monitoring capabilities for Twitter,
including hashtag monitoring, user timeline tracking, and trend detection.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import threading

from ..sources.twitter_scraper import <PERSON><PERSON><PERSON>raper, TwitterScraperConfig, create_twitter_scraper
from ..streaming.twitter_stream import TwitterStreamManager, TwitterStreamConfig, create_twitter_stream
from models.connection import db_manager
from models.database import Article
from config.settings import settings


@dataclass
class TwitterMonitorConfig:
    """Configuration for Twitter monitoring system."""
    
    # Hashtags to monitor by priority
    hashtags: Dict[str, int] = field(default_factory=lambda: {
        '#AI': 1,
        '#MachineLearning': 1,
        '#ArtificialIntelligence': 1,
        '#DeepLearning': 1,
        '#LLM': 1,
        '#OpenAI': 1,
        '#ChatGPT': 1,
        '#GPT4': 2,
        '#NLP': 2,
        '#DataScience': 2,
        '#MLOps': 2,
        '#NeuralNetwork': 2,
        '#Transformer': 3,
        '#BERT': 3,
        '#ComputerVision': 3,
        '#ReinforcementLearning': 3
    })
    
    # User accounts to monitor by priority
    user_accounts: Dict[str, int] = field(default_factory=lambda: {
        'OpenAI': 1,
        'AnthropicAI': 1,
        'GoogleAI': 1,
        'DeepMind': 1,
        'huggingface': 1,
        'ylecun': 2,
        'karpathy': 2,
        'goodfellow_ian': 2,
        'jeffdean': 2,
        'fchollet': 2,
        'PyTorch': 3,
        'TensorFlow': 3,
        'nvidia': 3
    })
    
    # Monitoring intervals (in minutes)
    high_priority_interval: int = 15  # Every 15 minutes
    medium_priority_interval: int = 60  # Every hour
    low_priority_interval: int = 240  # Every 4 hours
    
    # Scraping limits
    max_tweets_per_high_priority: int = 50
    max_tweets_per_medium_priority: int = 30
    max_tweets_per_low_priority: int = 20
    
    # Real-time streaming
    enable_streaming: bool = True
    stream_keywords: List[str] = field(default_factory=lambda: [
        'artificial intelligence', 'machine learning', 'large language model',
        'neural network', 'deep learning', 'OpenAI', 'ChatGPT'
    ])
    
    # Processing settings
    enable_real_time_processing: bool = True
    save_to_database: bool = True
    min_engagement_threshold: int = 5  # Minimum likes/retweets to save
    
    # Thread pool settings
    max_workers: int = 4


class TwitterMonitor:
    """Comprehensive Twitter monitoring system."""
    
    def __init__(self, config: TwitterMonitorConfig):
        self.config = config
        self.logger = logging.getLogger("twitter.monitor")
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_threads: Dict[int, threading.Thread] = {}
        self.scrapers: Dict[int, TwitterScraper] = {}
        
        # Streaming components
        self.stream_manager: Optional[TwitterStreamManager] = None
        
        # Thread pool for concurrent operations
        self.executor = ThreadPoolExecutor(max_workers=config.max_workers)
        
        # Statistics
        self.stats = {
            'monitoring_start_time': None,
            'tweets_monitored': 0,
            'tweets_saved': 0,
            'monitoring_cycles': 0,
            'errors': 0,
            'last_monitoring_time': {},
            'hashtag_stats': {},
            'user_stats': {}
        }
        
        # Callbacks
        self.tweet_callbacks: List[Callable] = []
        
        self._setup_scrapers()
        if config.enable_streaming:
            self._setup_streaming()
    
    def add_tweet_callback(self, callback: Callable):
        """Add a callback function to be called for each processed tweet."""
        self.tweet_callbacks.append(callback)
    
    def _setup_scrapers(self):
        """Setup Twitter scrapers for different priority levels."""
        try:
            # High priority scraper
            self.scrapers[1] = create_twitter_scraper(
                hashtags=self._get_hashtags_by_priority(1),
                user_accounts=self._get_users_by_priority(1),
                max_tweets_per_hashtag=self.config.max_tweets_per_high_priority
            )
            
            # Medium priority scraper
            self.scrapers[2] = create_twitter_scraper(
                hashtags=self._get_hashtags_by_priority(2),
                user_accounts=self._get_users_by_priority(2),
                max_tweets_per_hashtag=self.config.max_tweets_per_medium_priority
            )
            
            # Low priority scraper
            self.scrapers[3] = create_twitter_scraper(
                hashtags=self._get_hashtags_by_priority(3),
                user_accounts=self._get_users_by_priority(3),
                max_tweets_per_hashtag=self.config.max_tweets_per_low_priority
            )
            
            self.logger.info("Twitter scrapers initialized for all priority levels")
            
        except Exception as e:
            self.logger.error(f"Failed to setup Twitter scrapers: {e}")
            raise
    
    def _setup_streaming(self):
        """Setup real-time Twitter streaming."""
        try:
            self.stream_manager = create_twitter_stream(
                track_keywords=self.config.stream_keywords,
                track_hashtags=self._get_hashtags_by_priority(1),  # Use high priority hashtags
                enable_processing=self.config.enable_real_time_processing
            )
            
            # Set callback for processed tweets
            self.stream_manager.set_tweet_callback(self._handle_streamed_tweet)
            
            self.logger.info("Twitter streaming setup completed")
            
        except Exception as e:
            self.logger.error(f"Failed to setup Twitter streaming: {e}")
            # Continue without streaming
            self.stream_manager = None
    
    def _get_hashtags_by_priority(self, priority: int) -> List[str]:
        """Get hashtags by priority level."""
        return [hashtag for hashtag, prio in self.config.hashtags.items() 
                if prio == priority]
    
    def _get_users_by_priority(self, priority: int) -> List[str]:
        """Get user accounts by priority level."""
        return [user for user, prio in self.config.user_accounts.items() 
                if prio == priority]
    
    def start_monitoring(self):
        """Start the Twitter monitoring system."""
        if self.is_monitoring:
            self.logger.warning("Monitoring is already running")
            return
        
        self.logger.info("Starting Twitter monitoring system...")
        self.is_monitoring = True
        self.stats['monitoring_start_time'] = time.time()
        
        # Start monitoring threads for different priorities
        intervals = {
            1: self.config.high_priority_interval,
            2: self.config.medium_priority_interval,
            3: self.config.low_priority_interval
        }
        
        for priority, interval in intervals.items():
            thread = threading.Thread(
                target=self._monitoring_loop,
                args=(priority, interval),
                daemon=True,
                name=f"TwitterMonitor-Priority{priority}"
            )
            thread.start()
            self.monitoring_threads[priority] = thread
            self.logger.info(f"Started monitoring thread for priority {priority} (interval: {interval}min)")
        
        # Start streaming if enabled
        if self.stream_manager:
            try:
                self.stream_manager.start_streaming()
                self.logger.info("Real-time Twitter streaming started")
            except Exception as e:
                self.logger.error(f"Failed to start streaming: {e}")
        
        self.logger.info("Twitter monitoring system started successfully")
    
    def _monitoring_loop(self, priority: int, interval_minutes: int):
        """Main monitoring loop for a specific priority level."""
        interval_seconds = interval_minutes * 60
        scraper = self.scrapers[priority]
        
        self.logger.info(f"Starting monitoring loop for priority {priority}")
        
        while self.is_monitoring:
            try:
                start_time = time.time()
                self.logger.info(f"Starting monitoring cycle for priority {priority}")
                
                # Perform monitoring
                tweets_found = self._monitor_priority_level(priority, scraper)
                
                # Update statistics
                self.stats['monitoring_cycles'] += 1
                self.stats['tweets_monitored'] += tweets_found
                self.stats['last_monitoring_time'][priority] = datetime.now()
                
                # Calculate sleep time
                elapsed = time.time() - start_time
                sleep_time = max(0, interval_seconds - elapsed)
                
                self.logger.info(
                    f"Priority {priority} monitoring cycle completed. "
                    f"Found {tweets_found} tweets in {elapsed:.1f}s. "
                    f"Sleeping for {sleep_time:.1f}s"
                )
                
                # Sleep until next cycle
                if sleep_time > 0 and self.is_monitoring:
                    time.sleep(sleep_time)
                    
            except Exception as e:
                self.logger.error(f"Error in monitoring loop for priority {priority}: {e}")
                self.stats['errors'] += 1
                time.sleep(60)  # Wait before retrying
    
    def _monitor_priority_level(self, priority: int, scraper: TwitterScraper) -> int:
        """Monitor a specific priority level."""
        tweets_found = 0
        
        try:
            # Scrape tweets
            for article in scraper.scrape_all():
                if not self.is_monitoring:
                    break
                
                tweets_found += 1
                
                # Process the tweet
                self._process_monitored_tweet(article, priority)
                
                # Call registered callbacks
                for callback in self.tweet_callbacks:
                    try:
                        callback(article)
                    except Exception as e:
                        self.logger.error(f"Error in tweet callback: {e}")
                
        except Exception as e:
            self.logger.error(f"Error monitoring priority {priority}: {e}")
            self.stats['errors'] += 1
        
        return tweets_found
    
    def _process_monitored_tweet(self, article, priority: int):
        """Process a monitored tweet."""
        try:
            # Check engagement threshold
            engagement_metrics = article.metadata.get('engagement_metrics', {})
            total_engagement = engagement_metrics.get('total_engagement', 0)
            
            if total_engagement < self.config.min_engagement_threshold:
                self.logger.debug(f"Tweet below engagement threshold: {total_engagement}")
                return
            
            # Save to database if enabled
            if self.config.save_to_database:
                self._save_tweet_to_database(article, priority)
            
            # Update hashtag/user statistics
            self._update_monitoring_stats(article, priority)
            
        except Exception as e:
            self.logger.error(f"Error processing monitored tweet: {e}")
    
    def _handle_streamed_tweet(self, article):
        """Handle a tweet from the real-time stream."""
        try:
            self.logger.debug(f"Processing streamed tweet: {article.url}")
            
            # Process similar to monitored tweets
            self._process_monitored_tweet(article, priority=0)  # Stream priority = 0
            
            # Call registered callbacks
            for callback in self.tweet_callbacks:
                try:
                    callback(article)
                except Exception as e:
                    self.logger.error(f"Error in stream callback: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error handling streamed tweet: {e}")
    
    def _save_tweet_to_database(self, article, priority: int):
        """Save tweet to database."""
        try:
            with db_manager.get_session() as session:
                # Check if article already exists
                existing = session.query(Article).filter_by(url=article.url).first()
                if existing:
                    self.logger.debug(f"Tweet already exists in database: {article.url}")
                    return
                
                # Create new article record
                db_article = Article(
                    title=article.title,
                    content=article.content,
                    url=article.url,
                    source_type='twitter',
                    source_id=article.metadata.get('tweet_id'),
                    author=article.author,
                    published_at=article.published_at,
                    engagement_metrics=article.metadata.get('engagement_metrics', {}),
                    metadata={
                        **article.metadata,
                        'monitoring_priority': priority,
                        'monitoring_timestamp': datetime.now().isoformat()
                    }
                )
                
                session.add(db_article)
                session.commit()
                
                self.stats['tweets_saved'] += 1
                self.logger.debug(f"Saved tweet to database: {article.url}")
                
        except Exception as e:
            self.logger.error(f"Error saving tweet to database: {e}")
    
    def _update_monitoring_stats(self, article, priority: int):
        """Update monitoring statistics."""
        # Update hashtag stats
        hashtags = article.metadata.get('entities', {}).get('hashtags', [])
        for hashtag in hashtags:
            tag = hashtag.get('tag', '').lower()
            if tag:
                if tag not in self.stats['hashtag_stats']:
                    self.stats['hashtag_stats'][tag] = {'count': 0, 'last_seen': None}
                self.stats['hashtag_stats'][tag]['count'] += 1
                self.stats['hashtag_stats'][tag]['last_seen'] = datetime.now()
        
        # Update user stats
        author = article.author
        if author:
            if author not in self.stats['user_stats']:
                self.stats['user_stats'][author] = {'count': 0, 'last_seen': None}
            self.stats['user_stats'][author]['count'] += 1
            self.stats['user_stats'][author]['last_seen'] = datetime.now()
    
    def stop_monitoring(self):
        """Stop the Twitter monitoring system."""
        self.logger.info("Stopping Twitter monitoring system...")
        
        self.is_monitoring = False
        
        # Stop streaming
        if self.stream_manager:
            try:
                self.stream_manager.stop_streaming()
                self.logger.info("Twitter streaming stopped")
            except Exception as e:
                self.logger.error(f"Error stopping streaming: {e}")
        
        # Wait for monitoring threads to finish
        for priority, thread in self.monitoring_threads.items():
            if thread.is_alive():
                self.logger.info(f"Waiting for priority {priority} thread to finish...")
                thread.join(timeout=10)
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        self.logger.info("Twitter monitoring system stopped")
    
    def get_monitoring_stats(self) -> Dict:
        """Get comprehensive monitoring statistics."""
        base_stats = {
            **self.stats,
            'is_monitoring': self.is_monitoring,
            'active_threads': len([t for t in self.monitoring_threads.values() if t.is_alive()]),
            'scrapers_initialized': len(self.scrapers)
        }
        
        # Add streaming stats if available
        if self.stream_manager:
            stream_stats = self.stream_manager.get_stats()
            base_stats['streaming'] = stream_stats
        
        # Add scraper stats
        scraper_stats = {}
        for priority, scraper in self.scrapers.items():
            scraper_stats[f'priority_{priority}'] = scraper.get_stats()
        base_stats['scrapers'] = scraper_stats
        
        return base_stats


def create_twitter_monitor(
    hashtags: Optional[Dict[str, int]] = None,
    user_accounts: Optional[Dict[str, int]] = None,
    enable_streaming: bool = True,
    save_to_database: bool = True
) -> TwitterMonitor:
    """Factory function to create a Twitter monitor with common settings."""
    
    config = TwitterMonitorConfig(
        hashtags=hashtags or {
            '#AI': 1, '#MachineLearning': 1, '#ArtificialIntelligence': 1,
            '#DeepLearning': 1, '#LLM': 1, '#OpenAI': 1, '#ChatGPT': 1
        },
        user_accounts=user_accounts or {
            'OpenAI': 1, 'AnthropicAI': 1, 'GoogleAI': 1, 'DeepMind': 1, 'huggingface': 1
        },
        enable_streaming=enable_streaming,
        save_to_database=save_to_database
    )
    
    return TwitterMonitor(config)
