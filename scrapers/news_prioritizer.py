"""
News source prioritization system for ranking and filtering news articles.
Implements source credibility scoring and content quality metrics.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import re

from models.database import Article

logger = logging.getLogger(__name__)


@dataclass
class SourceCredibilityConfig:
    """Configuration for source credibility scoring."""
    
    # Tier 1: Highest credibility sources (0.9-1.0)
    tier1_sources: List[str] = field(default_factory=lambda: [
        "reuters", "bloomberg", "wsj", "ft", "nature", "science", 
        "ieee", "acm", "guardian", "bbc", "npr", "pbs"
    ])
    
    # Tier 2: High credibility sources (0.7-0.9)
    tier2_sources: List[str] = field(default_factory=lambda: [
        "techcrunch", "wired", "arstechnica", "theverge", "venturebeat",
        "zdnet", "engadget", "thenextweb", "mit technology review",
        "harvard business review", "stanford ai lab"
    ])
    
    # Tier 3: Medium credibility sources (0.5-0.7)
    tier3_sources: List[str] = field(default_factory=lambda: [
        "mashable", "gizmodo", "lifehacker", "digitaltrends",
        "pcmag", "cnet", "computerworld", "infoworld"
    ])
    
    # Domain-specific credibility boosts
    academic_domains: List[str] = field(default_factory=lambda: [
        "arxiv.org", "acm.org", "ieee.org", "nature.com", "science.org",
        "mit.edu", "stanford.edu", "berkeley.edu", "cmu.edu"
    ])
    
    # Content quality indicators
    quality_indicators: Dict[str, float] = field(default_factory=lambda: {
        "has_author": 0.1,
        "long_content": 0.15,  # >1000 words
        "recent_publication": 0.1,  # <24 hours
        "has_quotes": 0.05,
        "has_data": 0.1,
        "technical_depth": 0.1
    })


class NewsSourcePrioritizer:
    """Prioritizes and scores news articles based on source credibility and content quality."""
    
    def __init__(self, config: Optional[SourceCredibilityConfig] = None):
        self.config = config or SourceCredibilityConfig()
        
        # Compile regex patterns for content analysis
        self.quote_pattern = re.compile(r'["""].*?["""]', re.DOTALL)
        self.data_pattern = re.compile(r'\b\d+(?:\.\d+)?%|\b\d+(?:,\d{3})*(?:\.\d+)?\s*(?:million|billion|thousand|percent)', re.IGNORECASE)
        self.technical_pattern = re.compile(r'\b(?:algorithm|neural|network|model|training|dataset|accuracy|precision|recall|f1|loss|gradient|optimization|transformer|attention|embedding)\b', re.IGNORECASE)
    
    def calculate_source_credibility(self, article: Article) -> float:
        """Calculate credibility score for an article's source."""
        source_name = (article.source_id or "").lower()
        url = (article.url or "").lower()
        
        base_score = 0.3  # Default minimum score
        
        # Check tier-based credibility
        for source in self.config.tier1_sources:
            if source in source_name or source in url:
                base_score = max(base_score, 0.9)
                break
        
        for source in self.config.tier2_sources:
            if source in source_name or source in url:
                base_score = max(base_score, 0.7)
                break
        
        for source in self.config.tier3_sources:
            if source in source_name or source in url:
                base_score = max(base_score, 0.5)
                break
        
        # Academic domain boost
        for domain in self.config.academic_domains:
            if domain in url:
                base_score = min(1.0, base_score + 0.2)
                break
        
        return base_score
    
    def calculate_content_quality(self, article: Article) -> float:
        """Calculate content quality score based on various indicators."""
        quality_score = 0.0
        content = article.content or ""
        
        # Has author
        if article.author and article.author.strip():
            quality_score += self.config.quality_indicators["has_author"]
        
        # Content length
        if len(content) > 1000:
            quality_score += self.config.quality_indicators["long_content"]
        elif len(content) > 500:
            quality_score += self.config.quality_indicators["long_content"] * 0.5
        
        # Recent publication
        if article.published_at:
            pub_date = article.published_at
            if pub_date.tzinfo is not None:
                pub_date = pub_date.replace(tzinfo=None)
            hours_ago = (datetime.now() - pub_date).total_seconds() / 3600
            if hours_ago < 24:
                quality_score += self.config.quality_indicators["recent_publication"]
            elif hours_ago < 72:
                quality_score += self.config.quality_indicators["recent_publication"] * 0.5
        
        # Has quotes (indicates interviews/primary sources)
        if self.quote_pattern.search(content):
            quality_score += self.config.quality_indicators["has_quotes"]
        
        # Contains data/statistics
        if self.data_pattern.search(content):
            quality_score += self.config.quality_indicators["has_data"]
        
        # Technical depth
        technical_matches = len(self.technical_pattern.findall(content))
        if technical_matches >= 5:
            quality_score += self.config.quality_indicators["technical_depth"]
        elif technical_matches >= 2:
            quality_score += self.config.quality_indicators["technical_depth"] * 0.5
        
        return min(1.0, quality_score)
    
    def calculate_overall_score(self, article: Article) -> float:
        """Calculate overall priority score for an article."""
        credibility_score = self.calculate_source_credibility(article)
        quality_score = self.calculate_content_quality(article)
        
        # Weighted combination (credibility is more important)
        overall_score = (credibility_score * 0.7) + (quality_score * 0.3)
        
        return min(1.0, overall_score)
    
    def prioritize_articles(self, articles: List[Article]) -> List[Tuple[Article, float]]:
        """Prioritize a list of articles by calculating scores."""
        scored_articles = []
        
        for article in articles:
            try:
                score = self.calculate_overall_score(article)
                scored_articles.append((article, score))
            except Exception as e:
                logger.warning(f"Error scoring article {article.url}: {e}")
                scored_articles.append((article, 0.3))  # Default low score
        
        # Sort by score (highest first)
        scored_articles.sort(key=lambda x: x[1], reverse=True)
        
        return scored_articles
    
    def filter_by_quality_threshold(self, articles: List[Article], threshold: float = 0.5) -> List[Article]:
        """Filter articles by minimum quality threshold."""
        filtered_articles = []
        
        for article in articles:
            try:
                score = self.calculate_overall_score(article)
                if score >= threshold:
                    # Update article engagement_metrics with score
                    if not article.engagement_metrics:
                        article.engagement_metrics = {}
                    article.engagement_metrics['priority_score'] = score
                    article.engagement_metrics['credibility_score'] = self.calculate_source_credibility(article)
                    article.engagement_metrics['quality_score'] = self.calculate_content_quality(article)
                    
                    filtered_articles.append(article)
            except Exception as e:
                logger.warning(f"Error filtering article {article.url}: {e}")
        
        return filtered_articles
    
    def get_top_articles(self, articles: List[Article], limit: int = 50) -> List[Article]:
        """Get top N articles by priority score."""
        scored_articles = self.prioritize_articles(articles)
        
        top_articles = []
        for article, score in scored_articles[:limit]:
            # Update article engagement_metrics with score
            if not article.engagement_metrics:
                article.engagement_metrics = {}
            article.engagement_metrics['priority_score'] = score
            article.engagement_metrics['credibility_score'] = self.calculate_source_credibility(article)
            article.engagement_metrics['quality_score'] = self.calculate_content_quality(article)
            
            top_articles.append(article)
        
        return top_articles
    
    def analyze_source_distribution(self, articles: List[Article]) -> Dict[str, Any]:
        """Analyze the distribution of sources in the article list."""
        source_stats = {}
        total_articles = len(articles)
        
        for article in articles:
            source_name = article.source_id or "Unknown"
            
            if source_name not in source_stats:
                source_stats[source_name] = {
                    'count': 0,
                    'avg_credibility': 0.0,
                    'avg_quality': 0.0,
                    'urls': []
                }
            
            source_stats[source_name]['count'] += 1
            source_stats[source_name]['urls'].append(article.url)
            
            # Calculate running averages
            credibility = self.calculate_source_credibility(article)
            quality = self.calculate_content_quality(article)
            
            count = source_stats[source_name]['count']
            source_stats[source_name]['avg_credibility'] = (
                (source_stats[source_name]['avg_credibility'] * (count - 1) + credibility) / count
            )
            source_stats[source_name]['avg_quality'] = (
                (source_stats[source_name]['avg_quality'] * (count - 1) + quality) / count
            )
        
        # Calculate percentages
        for source_name in source_stats:
            source_stats[source_name]['percentage'] = (
                source_stats[source_name]['count'] / total_articles * 100
            )
        
        # Sort by count
        sorted_sources = dict(sorted(source_stats.items(), key=lambda x: x[1]['count'], reverse=True))
        
        return {
            'total_articles': total_articles,
            'unique_sources': len(source_stats),
            'source_breakdown': sorted_sources
        }
