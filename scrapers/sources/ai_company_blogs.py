"""
AI Company Blog Scrapers.

This module provides specialized scrapers for major AI company blogs
with company-specific parsing strategies and content extraction.
"""

import logging
import time
import re
from typing import Dict, List, Optional, Iterator, Any, Type
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from abc import ABC, abstractmethod

import requests
from bs4 import BeautifulSoup

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError
from ..enhanced.advanced_extractor import AdvancedContentExtractor, ExtractorConfig


logger = logging.getLogger(__name__)


class AICompanyBlogConfig(ScraperConfig):
    """Base configuration for AI company blog scrapers."""

    def __init__(self, company_name: str, **kwargs):
        # Extract company-specific parameters before calling super()
        self.company_name = company_name
        self.max_articles = kwargs.pop('max_articles', 20)
        self.days_back = kwargs.pop('days_back', 30)
        self.include_author_info = kwargs.pop('include_author_info', True)
        self.extract_images = kwargs.pop('extract_images', True)

        # Set defaults for required fields
        kwargs.setdefault('name', f"{company_name.lower()}_blog")
        kwargs.setdefault('base_url', 'https://example.com')  # Will be overridden by subclasses
        super().__init__(**kwargs)


class BaseAICompanyBlogScraper(BaseScraper, ABC):
    """Base class for AI company blog scrapers."""
    
    def __init__(self, config: AICompanyBlogConfig):
        super().__init__(config)
        self.config: AICompanyBlogConfig = config
        
        # Initialize advanced extractor
        extractor_config = ExtractorConfig(
            use_selenium=False,
            min_content_length=200,
            extract_images=config.extract_images,
            extract_links=True
        )
        self.extractor = AdvancedContentExtractor(extractor_config)
        
        # Company-specific tracking
        self.articles_processed = 0
    
    @abstractmethod
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from the company blog."""
        pass
    
    @abstractmethod
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single article with company-specific parsing."""
        pass
    
    def _is_recent_article(self, date_str: str) -> bool:
        """Check if article is within the configured time range."""
        try:
            # This will be implemented by subclasses based on their date formats
            return True
        except:
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """Get scraping statistics."""
        base_stats = super().get_stats()
        base_stats.update({
            'articles_processed': self.articles_processed,
            'company': self.config.company_name,
            'days_back': self.config.days_back
        })
        return base_stats
    
    def close(self):
        """Clean up resources."""
        if self.extractor:
            self.extractor.close()
        super().close()


class OpenAIBlogScraper(BaseAICompanyBlogScraper):
    """Scraper for OpenAI blog."""
    
    def __init__(self):
        config = AICompanyBlogConfig("OpenAI")
        config.base_url = "https://openai.com/blog"
        super().__init__(config)
    
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from OpenAI blog."""
        try:
            response = self.make_request(self.config.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # OpenAI blog uses specific selectors
            article_links = soup.select('a[href*="/blog/"]')
            
            urls_found = set()
            for link in article_links:
                href = link.get('href')
                if href and '/blog/' in href and href != '/blog':
                    full_url = urljoin(self.config.base_url, href)
                    if full_url not in urls_found:
                        urls_found.add(full_url)
                        yield full_url
                        
                        if len(urls_found) >= self.config.max_articles:
                            break
            
            self.logger.info(f"Found {len(urls_found)} OpenAI blog articles")
            
        except Exception as e:
            self.logger.error(f"Failed to get OpenAI blog URLs: {e}")
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape OpenAI blog article."""
        try:
            result = self.extractor.extract_content(url)
            
            if not result or not result.content:
                return None
            
            # Extract additional OpenAI-specific metadata
            response = self.make_request(url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for author information
            author = result.author
            if not author:
                author_elem = soup.select_one('.author, .byline, [data-author]')
                if author_elem:
                    author = author_elem.get_text(strip=True)
            
            # Extract publication date
            published_at = result.published_at
            if not published_at:
                date_elem = soup.select_one('time, .date, .published')
                if date_elem:
                    date_text = date_elem.get('datetime') or date_elem.get_text(strip=True)
                    published_at = self._parse_date(date_text)
            
            # Create ParsedArticle
            article = ParsedArticle(
                url=url,
                title=result.title,
                content=result.content,
                author=author,
                published_at=published_at,
                source_name=self.config.name,
                tags=result.tags + ['openai', 'ai-company'],
                summary=result.summary,
                content_type='blog_post',
                metadata={
                    'company': 'OpenAI',
                    'extraction_method': result.extraction_method,
                    'quality_score': result.quality_score,
                    'images': result.images[:5] if result.images else []
                }
            )
            
            self.articles_processed += 1
            return article
            
        except Exception as e:
            self.logger.error(f"Failed to scrape OpenAI article {url}: {e}")
            return None
    
    def _parse_date(self, date_str: str) -> Optional[str]:
        """Parse date string to ISO format."""
        try:
            # Handle various date formats
            import dateutil.parser
            parsed_date = dateutil.parser.parse(date_str)
            return parsed_date.isoformat()
        except:
            return None


class GoogleAIBlogScraper(BaseAICompanyBlogScraper):
    """Scraper for Google AI blog."""
    
    def __init__(self):
        config = AICompanyBlogConfig("Google AI")
        config.base_url = "https://ai.googleblog.com"
        super().__init__(config)
    
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from Google AI blog."""
        try:
            response = self.make_request(self.config.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Google AI blog structure
            article_links = soup.select('h2.post-title a, .post-title a')
            
            urls_found = set()
            for link in article_links:
                href = link.get('href')
                if href:
                    full_url = urljoin(self.config.base_url, href)
                    if full_url not in urls_found:
                        urls_found.add(full_url)
                        yield full_url
                        
                        if len(urls_found) >= self.config.max_articles:
                            break
            
            self.logger.info(f"Found {len(urls_found)} Google AI blog articles")
            
        except Exception as e:
            self.logger.error(f"Failed to get Google AI blog URLs: {e}")
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape Google AI blog article."""
        try:
            result = self.extractor.extract_content(url)
            
            if not result or not result.content:
                return None
            
            # Extract Google AI specific metadata
            response = self.make_request(url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Author information
            author = result.author
            if not author:
                author_elem = soup.select_one('.post-author, .author-name')
                if author_elem:
                    author = author_elem.get_text(strip=True)
            
            # Publication date
            published_at = result.published_at
            if not published_at:
                date_elem = soup.select_one('.published, .post-timestamp')
                if date_elem:
                    date_text = date_elem.get_text(strip=True)
                    published_at = self._parse_date(date_text)
            
            # Create ParsedArticle
            article = ParsedArticle(
                url=url,
                title=result.title,
                content=result.content,
                author=author,
                published_at=published_at,
                source_name=self.config.name,
                tags=result.tags + ['google-ai', 'ai-company'],
                summary=result.summary,
                content_type='blog_post',
                metadata={
                    'company': 'Google AI',
                    'extraction_method': result.extraction_method,
                    'quality_score': result.quality_score,
                    'images': result.images[:5] if result.images else []
                }
            )
            
            self.articles_processed += 1
            return article
            
        except Exception as e:
            self.logger.error(f"Failed to scrape Google AI article {url}: {e}")
            return None
    
    def _parse_date(self, date_str: str) -> Optional[str]:
        """Parse Google AI blog date format."""
        try:
            import dateutil.parser
            parsed_date = dateutil.parser.parse(date_str)
            return parsed_date.isoformat()
        except:
            return None


class AnthropicBlogScraper(BaseAICompanyBlogScraper):
    """Scraper for Anthropic blog."""
    
    def __init__(self):
        config = AICompanyBlogConfig("Anthropic")
        config.base_url = "https://www.anthropic.com/news"
        super().__init__(config)
    
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from Anthropic blog."""
        try:
            response = self.make_request(self.config.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Anthropic blog structure
            article_links = soup.select('a[href*="/news/"]')
            
            urls_found = set()
            for link in article_links:
                href = link.get('href')
                if href and '/news/' in href:
                    full_url = urljoin(self.config.base_url, href)
                    if full_url not in urls_found and full_url != self.config.base_url:
                        urls_found.add(full_url)
                        yield full_url
                        
                        if len(urls_found) >= self.config.max_articles:
                            break
            
            self.logger.info(f"Found {len(urls_found)} Anthropic blog articles")
            
        except Exception as e:
            self.logger.error(f"Failed to get Anthropic blog URLs: {e}")
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape Anthropic blog article."""
        try:
            result = self.extractor.extract_content(url)
            
            if not result or not result.content:
                return None
            
            # Create ParsedArticle with Anthropic-specific tags
            article = ParsedArticle(
                url=url,
                title=result.title,
                content=result.content,
                author=result.author,
                published_at=result.published_at,
                source_name=self.config.name,
                tags=result.tags + ['anthropic', 'ai-company', 'claude'],
                summary=result.summary,
                content_type='blog_post',
                metadata={
                    'company': 'Anthropic',
                    'extraction_method': result.extraction_method,
                    'quality_score': result.quality_score,
                    'images': result.images[:5] if result.images else []
                }
            )
            
            self.articles_processed += 1
            return article
            
        except Exception as e:
            self.logger.error(f"Failed to scrape Anthropic article {url}: {e}")
            return None


class MetaAIBlogScraper(BaseAICompanyBlogScraper):
    """Scraper for Meta AI blog."""

    def __init__(self):
        config = AICompanyBlogConfig("Meta AI")
        config.base_url = "https://ai.meta.com/blog"
        super().__init__(config)

    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from Meta AI blog."""
        try:
            response = self.make_request(self.config.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')

            # Meta AI blog structure
            article_links = soup.select('a[href*="/blog/"]')

            urls_found = set()
            for link in article_links:
                href = link.get('href')
                if href and '/blog/' in href:
                    full_url = urljoin(self.config.base_url, href)
                    if full_url not in urls_found and full_url != self.config.base_url:
                        urls_found.add(full_url)
                        yield full_url

                        if len(urls_found) >= self.config.max_articles:
                            break

            self.logger.info(f"Found {len(urls_found)} Meta AI blog articles")

        except Exception as e:
            self.logger.error(f"Failed to get Meta AI blog URLs: {e}")

    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape Meta AI blog article."""
        try:
            result = self.extractor.extract_content(url)

            if not result or not result.content:
                return None

            # Create ParsedArticle with Meta-specific tags
            article = ParsedArticle(
                url=url,
                title=result.title,
                content=result.content,
                author=result.author,
                published_at=result.published_at,
                source_name=self.config.name,
                tags=result.tags + ['meta-ai', 'ai-company', 'facebook'],
                summary=result.summary,
                content_type='blog_post',
                metadata={
                    'company': 'Meta AI',
                    'extraction_method': result.extraction_method,
                    'quality_score': result.quality_score,
                    'images': result.images[:5] if result.images else []
                }
            )

            self.articles_processed += 1
            return article

        except Exception as e:
            self.logger.error(f"Failed to scrape Meta AI article {url}: {e}")
            return None


class MicrosoftAIBlogScraper(BaseAICompanyBlogScraper):
    """Scraper for Microsoft AI blog."""

    def __init__(self):
        config = AICompanyBlogConfig("Microsoft AI")
        config.base_url = "https://blogs.microsoft.com/ai"
        super().__init__(config)

    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from Microsoft AI blog."""
        try:
            response = self.make_request(self.config.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')

            # Microsoft AI blog structure
            article_links = soup.select('h2 a, .entry-title a, .post-title a')

            urls_found = set()
            for link in article_links:
                href = link.get('href')
                if href:
                    full_url = urljoin(self.config.base_url, href)
                    if full_url not in urls_found:
                        urls_found.add(full_url)
                        yield full_url

                        if len(urls_found) >= self.config.max_articles:
                            break

            self.logger.info(f"Found {len(urls_found)} Microsoft AI blog articles")

        except Exception as e:
            self.logger.error(f"Failed to get Microsoft AI blog URLs: {e}")

    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape Microsoft AI blog article."""
        try:
            result = self.extractor.extract_content(url)

            if not result or not result.content:
                return None

            # Create ParsedArticle with Microsoft-specific tags
            article = ParsedArticle(
                url=url,
                title=result.title,
                content=result.content,
                author=result.author,
                published_at=result.published_at,
                source_name=self.config.name,
                tags=result.tags + ['microsoft-ai', 'ai-company', 'azure'],
                summary=result.summary,
                content_type='blog_post',
                metadata={
                    'company': 'Microsoft AI',
                    'extraction_method': result.extraction_method,
                    'quality_score': result.quality_score,
                    'images': result.images[:5] if result.images else []
                }
            )

            self.articles_processed += 1
            return article

        except Exception as e:
            self.logger.error(f"Failed to scrape Microsoft AI article {url}: {e}")
            return None


class AICompanyBlogScraperFactory:
    """Factory for creating AI company blog scrapers."""

    _scrapers = {
        'openai': OpenAIBlogScraper,
        'google_ai': GoogleAIBlogScraper,
        'anthropic': AnthropicBlogScraper,
        'meta_ai': MetaAIBlogScraper,
        'microsoft_ai': MicrosoftAIBlogScraper
    }

    @classmethod
    def create_scraper(cls, company: str) -> Optional[BaseAICompanyBlogScraper]:
        """Create a scraper for the specified company."""
        scraper_class = cls._scrapers.get(company.lower())
        if scraper_class:
            return scraper_class()
        return None

    @classmethod
    def get_available_companies(cls) -> List[str]:
        """Get list of available company scrapers."""
        return list(cls._scrapers.keys())

    @classmethod
    def create_all_scrapers(cls) -> List[BaseAICompanyBlogScraper]:
        """Create scrapers for all available companies."""
        scrapers = []
        for company in cls._scrapers:
            scraper = cls.create_scraper(company)
            if scraper:
                scrapers.append(scraper)
        return scrapers


class MultiCompanyBlogScraper:
    """Scraper that manages multiple AI company blog scrapers."""

    def __init__(self, companies: List[str] = None):
        self.companies = companies or AICompanyBlogScraperFactory.get_available_companies()
        self.scrapers = []

        # Initialize scrapers for specified companies
        for company in self.companies:
            scraper = AICompanyBlogScraperFactory.create_scraper(company)
            if scraper:
                self.scrapers.append(scraper)

        self.logger = logging.getLogger(f"{__name__}.MultiCompanyBlogScraper")
        self.logger.info(f"Initialized scrapers for {len(self.scrapers)} companies")

    def scrape_all_companies(self) -> Iterator[ParsedArticle]:
        """Scrape articles from all configured company blogs."""
        for scraper in self.scrapers:
            try:
                self.logger.info(f"Scraping {scraper.config.company_name} blog...")

                # Get article URLs
                urls = list(scraper.get_article_urls())
                self.logger.info(f"Found {len(urls)} URLs for {scraper.config.company_name}")

                # Scrape articles
                for url in urls:
                    try:
                        article = scraper.scrape_article(url)
                        if article:
                            yield article
                    except Exception as e:
                        self.logger.warning(f"Failed to scrape {url}: {e}")
                        continue

            except Exception as e:
                self.logger.error(f"Failed to scrape {scraper.config.company_name}: {e}")
                continue

    def get_combined_stats(self) -> Dict[str, Any]:
        """Get combined statistics from all scrapers."""
        combined_stats = {
            'total_articles_processed': 0,
            'companies_scraped': len(self.scrapers),
            'company_stats': {}
        }

        for scraper in self.scrapers:
            stats = scraper.get_stats()
            company_name = scraper.config.company_name
            combined_stats['company_stats'][company_name] = stats
            combined_stats['total_articles_processed'] += stats.get('articles_processed', 0)

        return combined_stats

    def close(self):
        """Clean up all scrapers."""
        for scraper in self.scrapers:
            try:
                scraper.close()
            except Exception as e:
                self.logger.warning(f"Error closing scraper: {e}")


# Example usage and testing
if __name__ == "__main__":
    import sys
    import os

    # Add project root to path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    try:
        print("Testing AI Company Blog Scrapers...")

        # Test individual scraper
        print("\n--- Testing OpenAI Blog Scraper ---")
        openai_scraper = OpenAIBlogScraper()

        urls = list(openai_scraper.get_article_urls())
        print(f"Found {len(urls)} OpenAI blog URLs")

        if urls:
            article = openai_scraper.scrape_article(urls[0])
            if article:
                print(f"Title: {article.title}")
                print(f"Author: {article.author}")
                print(f"Content length: {len(article.content)}")
                print(f"Tags: {article.tags}")

        openai_scraper.close()

        # Test multi-company scraper
        print("\n--- Testing Multi-Company Scraper ---")
        multi_scraper = MultiCompanyBlogScraper(['openai', 'anthropic'])

        articles = list(multi_scraper.scrape_all_companies())
        print(f"Scraped {len(articles)} total articles")

        # Print statistics
        stats = multi_scraper.get_combined_stats()
        print(f"\nCombined Statistics:")
        print(f"Total articles: {stats['total_articles_processed']}")
        print(f"Companies scraped: {stats['companies_scraped']}")

        multi_scraper.close()

    except Exception as e:
        print(f"Error during testing: {e}")
