"""
arXiv Research Paper Scraper for AI/ML content.

This module provides specialized scraping capabilities for arXiv.org,
focusing on AI/ML research papers with comprehensive metadata extraction.
"""

import logging
import time
import re
from typing import Dict, List, Optional, Iterator, Any
from datetime import datetime, timedelta
from urllib.parse import urljoin, quote
import xml.etree.ElementTree as ET

import requests
from bs4 import BeautifulSoup

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError, RateLimitError
from ..enhanced.advanced_extractor import AdvancedContentExtractor, ExtractorConfig, ContentType


logger = logging.getLogger(__name__)


class ArxivScraperConfig(ScraperConfig):
    """Configuration for arXiv scraper."""

    def __init__(self, **kwargs):
        # Extract arXiv-specific parameters before calling super()
        self.categories = kwargs.pop('categories', [
            "cs.AI",  # Artificial Intelligence
            "cs.LG",  # Machine Learning
            "cs.CL",  # Computation and Language (NLP)
            "cs.CV",  # Computer Vision
            "cs.NE",  # Neural and Evolutionary Computing
            "stat.ML",  # Machine Learning (Statistics)
        ])

        # Search parameters
        self.max_results = kwargs.pop('max_results', 100)
        self.days_back = kwargs.pop('days_back', 7)  # How many days back to search
        self.include_abstracts = kwargs.pop('include_abstracts', True)
        self.download_pdfs = kwargs.pop('download_pdfs', False)  # Whether to download full PDFs

        # API settings
        self.api_base_url = kwargs.pop('api_base_url', "http://export.arxiv.org/api/query")
        self.api_delay = kwargs.pop('api_delay', 3.0)  # arXiv requests 3 second delays

        # Set defaults for required fields
        kwargs.setdefault('name', 'arxiv')
        kwargs.setdefault('base_url', 'https://arxiv.org')
        super().__init__(**kwargs)


class ArxivScraper(BaseScraper):
    """Scraper for arXiv research papers focused on AI/ML content."""
    
    def __init__(self, config: ArxivScraperConfig = None):
        self.config = config or ArxivScraperConfig()
        super().__init__(self.config)
        
        # Initialize advanced extractor for PDF processing
        extractor_config = ExtractorConfig(
            process_pdfs=self.config.download_pdfs,
            min_content_length=200,
            use_selenium=False  # Not needed for arXiv
        )
        self.extractor = AdvancedContentExtractor(extractor_config)
        
        # arXiv specific tracking
        self.papers_processed = 0
        self.pdfs_downloaded = 0
        
    def get_article_urls(self) -> Iterator[str]:
        """Get arXiv paper URLs using the arXiv API."""
        self.logger.info("Fetching arXiv papers using API")
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=self.config.days_back)
        
        for category in self.config.categories:
            try:
                # Rate limiting for arXiv API
                time.sleep(self.config.api_delay)
                
                # Build search query
                search_query = self._build_search_query(category, start_date, end_date)
                
                # Query arXiv API
                papers = self._query_arxiv_api(search_query)
                
                self.logger.info(f"Found {len(papers)} papers in category {category}")
                
                for paper in papers:
                    yield paper['url']
                    
            except Exception as e:
                self.logger.error(f"Failed to fetch papers for category {category}: {e}")
                continue
    
    def _build_search_query(self, category: str, start_date: datetime, end_date: datetime) -> str:
        """Build arXiv API search query."""
        # Format dates for arXiv API
        start_str = start_date.strftime("%Y%m%d%H%M")
        end_str = end_date.strftime("%Y%m%d%H%M")
        
        # Build query string
        query_parts = [
            f"cat:{category}",
            f"submittedDate:[{start_str}+TO+{end_str}]"
        ]
        
        # Add AI/ML specific keywords for broader categories
        if category in ["cs.AI", "cs.LG"]:
            ai_keywords = [
                "artificial intelligence", "machine learning", "deep learning",
                "neural network", "transformer", "attention", "GPT", "BERT",
                "reinforcement learning", "computer vision", "natural language"
            ]
            keyword_query = " OR ".join([f'all:"{keyword}"' for keyword in ai_keywords])
            query_parts.append(f"({keyword_query})")
        
        return " AND ".join(query_parts)
    
    def _query_arxiv_api(self, search_query: str) -> List[Dict[str, Any]]:
        """Query the arXiv API and parse results."""
        try:
            # Build API URL
            params = {
                'search_query': search_query,
                'start': 0,
                'max_results': self.config.max_results,
                'sortBy': 'submittedDate',
                'sortOrder': 'descending'
            }
            
            # Make API request
            response = self.session.get(self.config.api_base_url, params=params)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.content)
            
            # Extract paper information
            papers = []
            for entry in root.findall('{http://www.w3.org/2005/Atom}entry'):
                paper_data = self._parse_arxiv_entry(entry)
                if paper_data:
                    papers.append(paper_data)
            
            return papers
            
        except Exception as e:
            self.logger.error(f"arXiv API query failed: {e}")
            return []
    
    def _parse_arxiv_entry(self, entry: ET.Element) -> Optional[Dict[str, Any]]:
        """Parse a single arXiv entry from XML."""
        try:
            ns = {'atom': 'http://www.w3.org/2005/Atom', 'arxiv': 'http://arxiv.org/schemas/atom'}
            
            # Extract basic information
            paper_id = entry.find('atom:id', ns).text.split('/')[-1]
            title = entry.find('atom:title', ns).text.strip()
            summary = entry.find('atom:summary', ns).text.strip()
            
            # Extract authors
            authors = []
            for author in entry.findall('atom:author', ns):
                name = author.find('atom:name', ns)
                if name is not None:
                    authors.append(name.text)
            
            # Extract publication date
            published = entry.find('atom:published', ns).text
            updated = entry.find('atom:updated', ns).text
            
            # Extract categories
            categories = []
            for category in entry.findall('atom:category', ns):
                term = category.get('term')
                if term:
                    categories.append(term)
            
            # Extract links
            pdf_url = None
            abs_url = None
            for link in entry.findall('atom:link', ns):
                if link.get('type') == 'application/pdf':
                    pdf_url = link.get('href')
                elif link.get('rel') == 'alternate':
                    abs_url = link.get('href')
            
            # Extract arXiv specific metadata
            comment = entry.find('arxiv:comment', ns)
            comment_text = comment.text if comment is not None else None
            
            journal_ref = entry.find('arxiv:journal_ref', ns)
            journal_text = journal_ref.text if journal_ref is not None else None
            
            return {
                'id': paper_id,
                'title': title,
                'summary': summary,
                'authors': authors,
                'published': published,
                'updated': updated,
                'categories': categories,
                'pdf_url': pdf_url,
                'url': abs_url or f"https://arxiv.org/abs/{paper_id}",
                'comment': comment_text,
                'journal_ref': journal_text
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to parse arXiv entry: {e}")
            return None
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single arXiv paper."""
        try:
            self.logger.debug(f"Scraping arXiv paper: {url}")
            
            # Extract paper ID from URL
            paper_id = self._extract_paper_id(url)
            if not paper_id:
                self.logger.warning(f"Could not extract paper ID from URL: {url}")
                return None
            
            # Get paper metadata from abstract page
            paper_data = self._scrape_abstract_page(url)
            if not paper_data:
                return None
            
            # Download and process PDF if enabled
            if self.config.download_pdfs and paper_data.get('pdf_url'):
                pdf_content = self._process_pdf(paper_data['pdf_url'])
                if pdf_content:
                    paper_data['full_text'] = pdf_content
                    self.pdfs_downloaded += 1
            
            # Convert to ParsedArticle
            article = self._convert_to_parsed_article(paper_data, url)
            
            if article:
                self.papers_processed += 1
                
            return article
            
        except Exception as e:
            self.logger.error(f"Failed to scrape arXiv paper {url}: {e}")
            return None
    
    def _extract_paper_id(self, url: str) -> Optional[str]:
        """Extract arXiv paper ID from URL."""
        # Handle different URL formats
        patterns = [
            r'arxiv\.org/abs/([^/?]+)',
            r'arxiv\.org/pdf/([^/?]+)',
            r'arxiv\.org/format/([^/?]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _scrape_abstract_page(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape the arXiv abstract page for metadata."""
        try:
            response = self.make_request(url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract title
            title_element = soup.find('h1', class_='title')
            title = title_element.get_text().replace('Title:', '').strip() if title_element else ""
            
            # Extract authors
            authors = []
            authors_element = soup.find('div', class_='authors')
            if authors_element:
                for author_link in authors_element.find_all('a'):
                    authors.append(author_link.get_text().strip())
            
            # Extract abstract
            abstract_element = soup.find('blockquote', class_='abstract')
            abstract = ""
            if abstract_element:
                abstract = abstract_element.get_text().replace('Abstract:', '').strip()
            
            # Extract submission info
            submission_element = soup.find('div', class_='submission-history')
            submission_date = None
            if submission_element:
                date_match = re.search(r'\[v1\] (.+?) \(', submission_element.get_text())
                if date_match:
                    submission_date = date_match.group(1)
            
            # Extract categories
            categories = []
            subjects_element = soup.find('td', class_='tablecell subjects')
            if subjects_element:
                for link in subjects_element.find_all('a'):
                    categories.append(link.get_text().strip())
            
            # Extract PDF URL
            pdf_url = None
            pdf_link = soup.find('a', string=re.compile(r'PDF'))
            if pdf_link:
                pdf_url = urljoin(url, pdf_link.get('href'))
            
            # Extract comments
            comments = ""
            comments_element = soup.find('td', class_='tablecell comments')
            if comments_element:
                comments = comments_element.get_text().strip()
            
            return {
                'title': title,
                'authors': authors,
                'abstract': abstract,
                'submission_date': submission_date,
                'categories': categories,
                'pdf_url': pdf_url,
                'comments': comments,
                'url': url
            }
            
        except Exception as e:
            self.logger.error(f"Failed to scrape abstract page {url}: {e}")
            return None

    def _process_pdf(self, pdf_url: str) -> Optional[str]:
        """Download and extract text from PDF."""
        try:
            if not self.extractor:
                return None

            self.logger.debug(f"Processing PDF: {pdf_url}")

            # Use the advanced extractor for PDF processing
            result = self.extractor.extract_pdf_content(pdf_url)

            if result and result.content:
                return result.content

            return None

        except Exception as e:
            self.logger.warning(f"PDF processing failed for {pdf_url}: {e}")
            return None

    def _convert_to_parsed_article(self, paper_data: Dict[str, Any], url: str) -> Optional[ParsedArticle]:
        """Convert arXiv paper data to ParsedArticle format."""
        try:
            # Combine abstract and full text if available
            content = paper_data.get('abstract', '')
            if paper_data.get('full_text'):
                content += "\n\n--- Full Paper Content ---\n\n" + paper_data['full_text']

            if not content or len(content) < 50:
                return None

            # Parse submission date
            published_at = None
            if paper_data.get('submission_date'):
                try:
                    # Try to parse various date formats
                    date_str = paper_data['submission_date']
                    # Remove timezone info and extra text
                    date_str = re.sub(r'\s*\([^)]*\)$', '', date_str)
                    published_at = datetime.strptime(date_str, "%a, %d %b %Y %H:%M:%S").isoformat()
                except:
                    pass

            # Create tags from categories and keywords
            tags = paper_data.get('categories', []).copy()

            # Add AI/ML specific tags based on content
            ai_keywords = self._extract_ai_keywords(content)
            tags.extend(ai_keywords)

            # Remove duplicates
            tags = list(set(tags))

            # Create ParsedArticle
            article = ParsedArticle(
                url=url,
                title=paper_data.get('title', ''),
                content=content,
                author=', '.join(paper_data.get('authors', [])),
                published_at=published_at,
                source_name=self.config.name,
                tags=tags,
                summary=paper_data.get('abstract', '')[:500] if paper_data.get('abstract') else None,
                content_type='research_paper',
                metadata={
                    'arxiv_id': self._extract_paper_id(url),
                    'categories': paper_data.get('categories', []),
                    'comments': paper_data.get('comments', ''),
                    'pdf_url': paper_data.get('pdf_url'),
                    'authors_count': len(paper_data.get('authors', [])),
                    'has_full_text': bool(paper_data.get('full_text'))
                }
            )

            return article

        except Exception as e:
            self.logger.error(f"Failed to convert paper data to ParsedArticle: {e}")
            return None

    def _extract_ai_keywords(self, content: str) -> List[str]:
        """Extract AI/ML keywords from paper content."""
        keywords = []
        content_lower = content.lower()

        # Define AI/ML keyword categories
        keyword_categories = {
            'deep_learning': [
                'deep learning', 'neural network', 'cnn', 'rnn', 'lstm', 'gru',
                'transformer', 'attention', 'bert', 'gpt', 'resnet', 'vgg'
            ],
            'machine_learning': [
                'machine learning', 'supervised learning', 'unsupervised learning',
                'reinforcement learning', 'classification', 'regression', 'clustering'
            ],
            'nlp': [
                'natural language processing', 'nlp', 'language model', 'text generation',
                'sentiment analysis', 'named entity recognition', 'machine translation'
            ],
            'computer_vision': [
                'computer vision', 'image recognition', 'object detection', 'segmentation',
                'face recognition', 'image classification', 'optical character recognition'
            ],
            'ai_general': [
                'artificial intelligence', 'ai', 'expert system', 'knowledge graph',
                'reasoning', 'planning', 'search algorithm'
            ]
        }

        # Check for keywords in content
        for category, category_keywords in keyword_categories.items():
            for keyword in category_keywords:
                if keyword in content_lower:
                    keywords.append(keyword)

        # Limit number of keywords
        return keywords[:15]

    def get_stats(self) -> Dict[str, Any]:
        """Get scraping statistics."""
        base_stats = super().get_stats()
        base_stats.update({
            'papers_processed': self.papers_processed,
            'pdfs_downloaded': self.pdfs_downloaded,
            'categories_monitored': len(self.config.categories),
            'days_back': self.config.days_back
        })
        return base_stats

    def close(self):
        """Clean up resources."""
        if self.extractor:
            self.extractor.close()
        super().close()
