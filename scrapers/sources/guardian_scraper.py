"""
Guardian API scraper for tech and AI coverage.
Implements keyword-based news searching with rate limiting.
"""

import os
import time
import logging
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
import threading
from urllib.parse import urlencode

from ..base.scraper import Base<PERSON>craper, ScraperConfig
from models.database import Article, SourceType

logger = logging.getLogger(__name__)


class GuardianRateLimiter:
    """Rate limiter for Guardian API with per-minute limits."""
    
    def __init__(self, requests_per_minute: int = 720, min_request_interval: float = 0.083):
        self.requests_per_minute = requests_per_minute
        self.min_request_interval = min_request_interval
        self.request_times = []
        self.lock = threading.Lock()
        
    def wait_if_needed(self):
        """Wait if necessary to respect rate limits."""
        with self.lock:
            now = time.time()
            
            # Remove requests older than 1 minute
            minute_ago = now - 60
            self.request_times = [t for t in self.request_times if t > minute_ago]
            
            # Check if we need to wait for per-minute limit
            if len(self.request_times) >= self.requests_per_minute:
                oldest_request = min(self.request_times)
                wait_time = oldest_request + 60 - now
                if wait_time > 0:
                    logger.warning(f"Guardian API rate limit reached. Sleeping for {wait_time:.1f} seconds")
                    time.sleep(wait_time)
                    return
            
            # Check minimum interval between requests
            if self.request_times:
                last_request = max(self.request_times)
                time_since_last = now - last_request
                if time_since_last < self.min_request_interval:
                    wait_time = self.min_request_interval - time_since_last
                    logger.debug(f"Guardian API rate limiting: waiting {wait_time:.1f} seconds")
                    time.sleep(wait_time)
            
            # Record this request
            self.request_times.append(time.time())


@dataclass
class GuardianScraperConfig(ScraperConfig):
    """Configuration for Guardian API scraper."""
    
    # API Configuration
    api_key: str = ""
    base_url: str = "https://content.guardianapis.com"
    
    # Search Configuration
    keywords: List[str] = field(default_factory=lambda: [
        "artificial intelligence", "machine learning", "AI", "ChatGPT",
        "OpenAI", "Google AI", "deep learning", "neural networks",
        "large language model", "LLM", "computer vision", "NLP",
        "natural language processing", "automation", "robotics"
    ])
    
    # Content Filtering
    sections: List[str] = field(default_factory=lambda: [
        "technology", "science", "business", "media", "education"
    ])
    
    tags: List[str] = field(default_factory=lambda: [
        "technology/artificialintelligenceai", "technology/computing",
        "science/science", "business/technology", "technology/internet"
    ])
    
    # Search Parameters
    order_by: str = "newest"  # newest, oldest, relevance
    page_size: int = 50
    max_pages: int = 5
    
    # Date Range (days back from now)
    days_back: int = 7
    
    # Content Fields
    show_fields: List[str] = field(default_factory=lambda: [
        "headline", "byline", "body", "standfirst", "thumbnail",
        "publication", "wordcount", "commentable", "allowUgc"
    ])
    
    show_tags: List[str] = field(default_factory=lambda: [
        "keyword", "contributor", "tone", "type"
    ])
    
    # Rate Limiting
    requests_per_minute: int = 720  # 12 per second = 720 per minute
    min_request_interval: float = 0.083  # seconds (1/12)
    
    def __post_init__(self):
        
        # Load API key from environment
        if not self.api_key:
            self.api_key = os.getenv("GUARDIAN_API_KEY", "")
            if not self.api_key:
                raise ValueError("Guardian API key is required. Set GUARDIAN_API_KEY environment variable.")
        
        # Load rate limiting from environment variables
        self.requests_per_minute = int(os.getenv("GUARDIAN_REQUESTS_PER_MINUTE", self.requests_per_minute))
        self.min_request_interval = float(os.getenv("GUARDIAN_MIN_REQUEST_INTERVAL", self.min_request_interval))


class GuardianScraper(BaseScraper):
    """Scraper for Guardian API tech and AI coverage."""
    
    def __init__(self, config: GuardianScraperConfig):
        super().__init__(config)
        self.config = config
        self.rate_limiter = GuardianRateLimiter(
            requests_per_minute=config.requests_per_minute,
            min_request_interval=config.min_request_interval
        )
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AI-News-Scraper/1.0'
        })
        
        # Cache for article data
        self.article_cache = {}
    
    def get_article_urls(self) -> List[str]:
        """Get article URLs from Guardian API."""
        urls = []
        
        try:
            # Search for each keyword
            for keyword in self.config.keywords:
                logger.info(f"Searching Guardian API for keyword: {keyword}")
                keyword_urls = self._search_keyword(keyword)
                urls.extend(keyword_urls)
                
                # Avoid overwhelming the API
                if len(urls) > 300:
                    logger.info(f"Collected {len(urls)} URLs, stopping to avoid overwhelming API")
                    break
            
            # Remove duplicates while preserving order
            seen = set()
            unique_urls = []
            for url in urls:
                if url not in seen:
                    seen.add(url)
                    unique_urls.append(url)
            
            logger.info(f"Found {len(unique_urls)} unique articles from Guardian API")
            return unique_urls
            
        except Exception as e:
            logger.error(f"Error getting article URLs from Guardian API: {e}")
            return []
    
    def _search_keyword(self, keyword: str) -> List[str]:
        """Search for articles by keyword."""
        urls = []
        
        # Calculate date range
        to_date = datetime.now()
        from_date = to_date - timedelta(days=self.config.days_back)
        
        for page in range(1, self.config.max_pages + 1):
            try:
                self.rate_limiter.wait_if_needed()
                
                # Build search parameters
                params = {
                    'q': keyword,
                    'order-by': self.config.order_by,
                    'page-size': self.config.page_size,
                    'page': page,
                    'from-date': from_date.strftime('%Y-%m-%d'),
                    'to-date': to_date.strftime('%Y-%m-%d'),
                    'api-key': self.config.api_key,
                    'show-fields': ','.join(self.config.show_fields),
                    'show-tags': ','.join(self.config.show_tags)
                }
                
                # Add section filtering
                if self.config.sections:
                    params['section'] = '|'.join(self.config.sections)
                
                # Make API request
                url = f"{self.config.base_url}/search"
                logger.debug(f"Guardian API request: {url} with params: {params}")
                
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if data.get('response', {}).get('status') != 'ok':
                    logger.error(f"Guardian API error: {data}")
                    break
                
                results = data.get('response', {}).get('results', [])
                if not results:
                    logger.info(f"No more articles found for keyword '{keyword}' on page {page}")
                    break
                
                # Extract URLs and cache article data
                page_urls = []
                for article in results:
                    article_url = article.get('webUrl')
                    if article_url:
                        page_urls.append(article_url)
                        # Cache the article data for later use
                        self.article_cache[article_url] = article
                
                urls.extend(page_urls)
                
                logger.info(f"Found {len(page_urls)} articles for '{keyword}' on page {page}")
                
                # Check if we've reached the end
                total_results = data.get('response', {}).get('total', 0)
                if page * self.config.page_size >= total_results:
                    break
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error searching Guardian API for '{keyword}' page {page}: {e}")
                break
            except Exception as e:
                logger.error(f"Error searching Guardian API for '{keyword}' page {page}: {e}")
                break
        
        return urls
    
    def scrape_article(self, url: str) -> Optional[Article]:
        """Scrape article content from Guardian API data."""
        try:
            # Get article data from cache or API
            article_data = self.article_cache.get(url)
            if not article_data:
                article_data = self._get_article_data(url)
                if not article_data:
                    return None
            
            # Extract content fields
            fields = article_data.get('fields', {})
            
            # Create Article object
            article = Article(
                title=fields.get('headline') or article_data.get('webTitle', ''),
                content=fields.get('body', ''),
                url=url,
                source_type=SourceType.NEWS,
                source_id="guardian",
                published_at=self._parse_date(article_data.get('webPublicationDate')),
                author=fields.get('byline'),
                engagement_metrics={
                    'section_id': article_data.get('sectionId'),
                    'section_name': article_data.get('sectionName'),
                    'pillar_id': article_data.get('pillarId'),
                    'pillar_name': article_data.get('pillarName'),
                    'type': article_data.get('type'),
                    'word_count': fields.get('wordcount'),
                    'thumbnail': fields.get('thumbnail'),
                    'tags': self._extract_tags(article_data.get('tags', [])),
                    'credibility_score': 0.95,  # Guardian is high credibility
                    'api_source': 'guardian'
                }
            )
            
            return article
            
        except Exception as e:
            logger.error(f"Error scraping article from {url}: {e}")
            return None
    
    def _get_article_data(self, url: str) -> Optional[Dict[str, Any]]:
        """Get individual article data from Guardian API."""
        try:
            # Extract article ID from URL
            article_id = self._extract_article_id(url)
            if not article_id:
                return None
            
            self.rate_limiter.wait_if_needed()
            
            # Build parameters for individual article request
            params = {
                'api-key': self.config.api_key,
                'show-fields': ','.join(self.config.show_fields),
                'show-tags': ','.join(self.config.show_tags)
            }
            
            # Make API request for individual article
            api_url = f"{self.config.base_url}/{article_id}"
            response = self.session.get(api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('response', {}).get('status') != 'ok':
                logger.error(f"Guardian API error for article {article_id}: {data}")
                return None
            
            content = data.get('response', {}).get('content')
            return content
            
        except Exception as e:
            logger.error(f"Error getting article data for {url}: {e}")
            return None
    
    def _extract_article_id(self, url: str) -> Optional[str]:
        """Extract Guardian article ID from URL."""
        try:
            # Guardian URLs typically end with the article ID
            # e.g., https://www.theguardian.com/technology/2025/jun/30/ai-article-title
            parts = url.rstrip('/').split('/')
            if len(parts) >= 4:
                # Take the last 3 parts as the article ID
                return '/'.join(parts[-3:])
            return None
        except Exception:
            return None
    
    def _extract_tags(self, tags: List[Dict[str, Any]]) -> List[str]:
        """Extract tag names from Guardian API tags."""
        tag_names = []
        for tag in tags:
            if isinstance(tag, dict):
                tag_name = tag.get('webTitle') or tag.get('id', '')
                if tag_name:
                    tag_names.append(tag_name)
        return tag_names
    
    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string from Guardian API."""
        if not date_str:
            return None
        
        try:
            # Guardian API returns ISO format: 2025-06-30T10:53:20Z
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except Exception as e:
            logger.warning(f"Could not parse date '{date_str}': {e}")
            return None
