"""
Hacker News AI Discussion Monitor.

This module provides specialized scraping capabilities for Hacker News,
focusing on AI-related discussions, comments analysis, and trending AI topics.
"""

import logging
import time
import re
from typing import Dict, List, Optional, Iterator, Any, Set
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
import json

import requests
from bs4 import BeautifulSoup

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError, RateLimitError
from ..enhanced.advanced_extractor import AdvancedContentExtractor, ExtractorConfig


logger = logging.getLogger(__name__)


class HackerNewsScraperConfig(ScraperConfig):
    """Configuration for Hacker News scraper."""

    def __init__(self, **kwargs):
        # Extract HN-specific parameters before calling super()
        self.api_base_url = kwargs.pop('api_base_url', "https://hacker-news.firebaseio.com/v0")
        self.pages_to_scrape = kwargs.pop('pages_to_scrape', ["front", "new", "best", "ask", "show"])
        self.max_items_per_page = kwargs.pop('max_items_per_page', 30)
        self.include_comments = kwargs.pop('include_comments', True)
        self.max_comments_per_story = kwargs.pop('max_comments_per_story', 50)
        self.min_score = kwargs.pop('min_score', 5)  # Minimum story score to consider
        self.hours_back = kwargs.pop('hours_back', 24)  # How many hours back to look for stories

        # Set defaults for required fields
        kwargs.setdefault('name', 'hackernews')
        kwargs.setdefault('base_url', 'https://news.ycombinator.com')
        super().__init__(**kwargs)
        
        # AI-related keywords for filtering
        self.ai_keywords = [
            "ai", "artificial intelligence", "machine learning", "ml", "deep learning",
            "neural network", "gpt", "chatgpt", "openai", "anthropic", "claude",
            "llm", "large language model", "transformer", "bert", "nlp",
            "computer vision", "reinforcement learning", "tensorflow", "pytorch",
            "hugging face", "stable diffusion", "midjourney", "dall-e"
        ]
        
        # Rate limiting
        self.api_delay = 0.1  # HN API is quite permissive


class HackerNewsScraper(BaseScraper):
    """Scraper for Hacker News focused on AI-related discussions."""
    
    def __init__(self, config: HackerNewsScraperConfig = None):
        self.config = config or HackerNewsScraperConfig()
        super().__init__(self.config)
        
        # Initialize advanced extractor for linked articles
        extractor_config = ExtractorConfig(
            use_selenium=False,  # Usually not needed for HN
            min_content_length=100,
            extract_links=True
        )
        self.extractor = AdvancedContentExtractor(extractor_config)
        
        # HN specific tracking
        self.stories_processed = 0
        self.comments_processed = 0
        self.ai_stories_found = 0
        
        # Cache for processed items
        self.processed_items: Set[int] = set()
    
    def get_article_urls(self) -> Iterator[str]:
        """Get Hacker News story URLs using the HN API."""
        self.logger.info("Fetching Hacker News stories using API")
        
        # Get story IDs from different pages
        all_story_ids = set()
        
        for page_type in self.config.pages_to_scrape:
            try:
                story_ids = self._get_story_ids_for_page(page_type)
                all_story_ids.update(story_ids)
                self.logger.info(f"Found {len(story_ids)} stories from {page_type} page")
                
            except Exception as e:
                self.logger.error(f"Failed to fetch stories from {page_type}: {e}")
                continue
        
        # Filter stories by AI relevance and recency
        ai_story_ids = self._filter_ai_stories(list(all_story_ids))
        
        self.logger.info(f"Found {len(ai_story_ids)} AI-related stories")
        
        # Convert story IDs to URLs
        for story_id in ai_story_ids:
            yield f"https://news.ycombinator.com/item?id={story_id}"
    
    def _get_story_ids_for_page(self, page_type: str) -> List[int]:
        """Get story IDs for a specific HN page type."""
        try:
            # Map page types to API endpoints
            endpoint_map = {
                "front": "topstories",
                "new": "newstories",
                "best": "beststories",
                "ask": "askstories",
                "show": "showstories"
            }
            
            endpoint = endpoint_map.get(page_type, "topstories")
            url = f"{self.config.api_base_url}/{endpoint}.json"
            
            # Rate limiting
            time.sleep(self.config.api_delay)
            
            response = self.session.get(url)
            response.raise_for_status()
            
            story_ids = response.json()
            
            # Limit number of stories per page
            return story_ids[:self.config.max_items_per_page]
            
        except Exception as e:
            self.logger.error(f"Failed to get story IDs for {page_type}: {e}")
            return []
    
    def _filter_ai_stories(self, story_ids: List[int]) -> List[int]:
        """Filter stories to find AI-related content."""
        ai_stories = []
        cutoff_time = datetime.now() - timedelta(hours=self.config.hours_back)
        
        for story_id in story_ids:
            try:
                # Rate limiting
                time.sleep(self.config.api_delay)
                
                # Get story details
                story_data = self._get_story_data(story_id)
                
                if not story_data:
                    continue
                
                # Check if story is recent enough
                story_time = datetime.fromtimestamp(story_data.get('time', 0))
                if story_time < cutoff_time:
                    continue
                
                # Check minimum score
                if story_data.get('score', 0) < self.config.min_score:
                    continue
                
                # Check if AI-related
                if self._is_ai_related_story(story_data):
                    ai_stories.append(story_id)
                    self.ai_stories_found += 1
                
            except Exception as e:
                self.logger.warning(f"Error processing story {story_id}: {e}")
                continue
        
        return ai_stories
    
    def _get_story_data(self, story_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed story data from HN API."""
        try:
            url = f"{self.config.api_base_url}/item/{story_id}.json"
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            self.logger.warning(f"Failed to get story data for {story_id}: {e}")
            return None
    
    def _is_ai_related_story(self, story_data: Dict[str, Any]) -> bool:
        """Check if a story is AI-related based on title and content."""
        # Get title and text content
        title = story_data.get('title', '').lower()
        text = story_data.get('text', '').lower()
        url = story_data.get('url', '').lower()
        
        # Combine all text for keyword matching
        combined_text = f"{title} {text} {url}"
        
        # Check for AI keywords
        for keyword in self.config.ai_keywords:
            if keyword.lower() in combined_text:
                return True
        
        return False
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single Hacker News story with comments."""
        try:
            self.logger.debug(f"Scraping HN story: {url}")
            
            # Extract story ID from URL
            story_id = self._extract_story_id(url)
            if not story_id:
                return None
            
            # Skip if already processed
            if story_id in self.processed_items:
                return None
            
            # Get story data from API
            story_data = self._get_story_data(story_id)
            if not story_data:
                return None
            
            # Get comments if enabled
            comments_text = ""
            if self.config.include_comments:
                comments_text = self._get_story_comments(story_data)
            
            # Process linked article if present
            linked_content = ""
            if story_data.get('url'):
                linked_content = self._process_linked_article(story_data['url'])
            
            # Convert to ParsedArticle
            article = self._convert_to_parsed_article(story_data, url, comments_text, linked_content)
            
            if article:
                self.stories_processed += 1
                self.processed_items.add(story_id)
            
            return article
            
        except Exception as e:
            self.logger.error(f"Failed to scrape HN story {url}: {e}")
            return None
    
    def _extract_story_id(self, url: str) -> Optional[int]:
        """Extract story ID from HN URL."""
        match = re.search(r'id=(\d+)', url)
        if match:
            return int(match.group(1))
        return None
    
    def _get_story_comments(self, story_data: Dict[str, Any]) -> str:
        """Get and process comments for a story."""
        comments_text = ""
        comment_ids = story_data.get('kids', [])
        
        if not comment_ids:
            return comments_text
        
        # Limit number of comments to process
        comment_ids = comment_ids[:self.config.max_comments_per_story]
        
        for comment_id in comment_ids:
            try:
                # Rate limiting
                time.sleep(self.config.api_delay)
                
                comment_data = self._get_story_data(comment_id)
                if comment_data and comment_data.get('text'):
                    # Clean HTML from comment text
                    comment_text = self._clean_html_text(comment_data['text'])
                    if comment_text:
                        comments_text += f"\n--- Comment by {comment_data.get('by', 'Anonymous')} ---\n"
                        comments_text += comment_text + "\n"
                        self.comments_processed += 1
                
            except Exception as e:
                self.logger.warning(f"Error processing comment {comment_id}: {e}")
                continue
        
        return comments_text
    
    def _clean_html_text(self, html_text: str) -> str:
        """Clean HTML tags from comment text."""
        try:
            soup = BeautifulSoup(html_text, 'html.parser')
            return soup.get_text(separator=' ', strip=True)
        except:
            return html_text
    
    def _process_linked_article(self, article_url: str) -> str:
        """Process the linked article content."""
        try:
            # Skip certain domains that are not useful
            skip_domains = ['youtube.com', 'twitter.com', 'github.com']
            domain = urlparse(article_url).netloc.lower()
            
            for skip_domain in skip_domains:
                if skip_domain in domain:
                    return ""

            # Use advanced extractor to get article content
            result = self.extractor.extract_content(article_url)

            if result and result.content and len(result.content) > 200:
                return f"\n--- Linked Article Content ---\n{result.content[:2000]}..."  # Limit length

            return ""

        except Exception as e:
            self.logger.debug(f"Failed to process linked article {article_url}: {e}")
            return ""

    def _convert_to_parsed_article(self, story_data: Dict[str, Any], url: str,
                                 comments_text: str, linked_content: str) -> Optional[ParsedArticle]:
        """Convert HN story data to ParsedArticle format."""
        try:
            # Build content from story text, comments, and linked article
            content_parts = []

            # Add story text if present
            if story_data.get('text'):
                story_text = self._clean_html_text(story_data['text'])
                if story_text:
                    content_parts.append(f"--- Story Description ---\n{story_text}")

            # Add linked article content
            if linked_content:
                content_parts.append(linked_content)

            # Add comments
            if comments_text:
                content_parts.append(f"\n--- Discussion Comments ---\n{comments_text}")

            # Combine all content
            content = "\n\n".join(content_parts)

            # If no substantial content, skip
            if len(content) < 100:
                return None

            # Parse timestamp
            published_at = None
            if story_data.get('time'):
                published_at = datetime.fromtimestamp(story_data['time']).isoformat()

            # Extract tags from title and content
            tags = self._extract_tags_from_content(story_data.get('title', ''), content)

            # Determine content type
            content_type = 'forum_post'
            if story_data.get('type') == 'story':
                content_type = 'article' if story_data.get('url') else 'forum_post'
            elif story_data.get('type') in ['ask', 'show']:
                content_type = 'forum_post'

            # Create ParsedArticle
            article = ParsedArticle(
                url=url,
                title=story_data.get('title', ''),
                content=content,
                author=story_data.get('by', ''),
                published_at=published_at,
                source_name=self.config.name,
                tags=tags,
                summary=self._generate_summary(content),
                content_type=content_type,
                metadata={
                    'hn_id': story_data.get('id'),
                    'score': story_data.get('score', 0),
                    'comments_count': len(story_data.get('kids', [])),
                    'story_type': story_data.get('type'),
                    'linked_url': story_data.get('url'),
                    'has_comments': bool(comments_text),
                    'has_linked_content': bool(linked_content)
                }
            )

            return article

        except Exception as e:
            self.logger.error(f"Failed to convert HN story to ParsedArticle: {e}")
            return None

    def _extract_tags_from_content(self, title: str, content: str) -> List[str]:
        """Extract relevant tags from title and content."""
        tags = []
        combined_text = f"{title} {content}".lower()

        # AI/ML specific tags
        ai_tag_map = {
            'artificial intelligence': ['ai', 'artificial-intelligence'],
            'machine learning': ['ml', 'machine-learning'],
            'deep learning': ['deep-learning'],
            'neural network': ['neural-networks'],
            'natural language processing': ['nlp'],
            'computer vision': ['computer-vision'],
            'reinforcement learning': ['reinforcement-learning'],
            'gpt': ['gpt', 'language-models'],
            'chatgpt': ['chatgpt', 'openai'],
            'openai': ['openai'],
            'anthropic': ['anthropic'],
            'claude': ['claude'],
            'transformer': ['transformers'],
            'llm': ['llm', 'language-models'],
            'stable diffusion': ['stable-diffusion', 'image-generation'],
            'midjourney': ['midjourney', 'image-generation'],
            'dall-e': ['dall-e', 'image-generation']
        }

        # Check for AI keywords and add corresponding tags
        for keyword, keyword_tags in ai_tag_map.items():
            if keyword in combined_text:
                tags.extend(keyword_tags)

        # Add HN-specific tags
        tags.append('hackernews')

        # Remove duplicates and limit
        return list(set(tags))[:10]

    def _generate_summary(self, content: str, max_length: int = 200) -> str:
        """Generate a summary from content."""
        if not content:
            return ""

        # Take first paragraph or first few sentences
        paragraphs = content.split('\n\n')
        first_paragraph = paragraphs[0] if paragraphs else content

        # Limit length
        if len(first_paragraph) > max_length:
            sentences = first_paragraph.split('.')
            summary = ""
            for sentence in sentences:
                if len(summary + sentence) < max_length:
                    summary += sentence + ". "
                else:
                    break
            return summary.strip()

        return first_paragraph

    def get_stats(self) -> Dict[str, Any]:
        """Get scraping statistics."""
        base_stats = super().get_stats()
        base_stats.update({
            'stories_processed': self.stories_processed,
            'comments_processed': self.comments_processed,
            'ai_stories_found': self.ai_stories_found,
            'pages_monitored': len(self.config.pages_to_scrape),
            'hours_back': self.config.hours_back
        })
        return base_stats

    def close(self):
        """Clean up resources."""
        if self.extractor:
            self.extractor.close()
        super().close()


# Example usage and testing
if __name__ == "__main__":
    import sys
    import os

    # Add project root to path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Create scraper with custom config
    config = HackerNewsScraperConfig(
        max_items_per_page=10,
        hours_back=12,
        include_comments=True,
        max_comments_per_story=10
    )

    scraper = HackerNewsScraper(config)

    try:
        print("Testing Hacker News scraper...")

        # Test getting article URLs
        urls = list(scraper.get_article_urls())
        print(f"Found {len(urls)} AI-related HN stories")

        # Test scraping a few stories
        for i, url in enumerate(urls[:3]):
            print(f"\nScraping story {i+1}: {url}")
            article = scraper.scrape_article(url)

            if article:
                print(f"Title: {article.title}")
                print(f"Author: {article.author}")
                print(f"Score: {article.metadata.get('score', 'N/A')}")
                print(f"Content length: {len(article.content)}")
                print(f"Tags: {article.tags}")
                print(f"Has comments: {article.metadata.get('has_comments', False)}")
            else:
                print("Failed to scrape article")

        # Print statistics
        stats = scraper.get_stats()
        print(f"\nScraping Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"Error during testing: {e}")

    finally:
        scraper.close()
