"""
Generic news website scraper implementation.
"""

import re
from typing import Iterator, Optional, List
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ContentParsingError


class NewsScraper(BaseScraper):
    """Generic scraper for news websites."""
    
    def __init__(self, config: ScraperConfig, article_selectors: List[str] = None):
        super().__init__(config)
        
        # Default selectors for finding article links
        self.article_selectors = article_selectors or [
            'a[href*="/article/"]',
            'a[href*="/news/"]', 
            'a[href*="/post/"]',
            'a[href*="/story/"]',
            '.article-link',
            '.news-link',
            'article a',
            '.entry-title a',
            'h2 a',
            'h3 a'
        ]
        
        # Patterns to exclude
        self.exclude_patterns = [
            r'/tag/',
            r'/category/',
            r'/author/',
            r'/search/',
            r'#comment',
            r'\.pdf$',
            r'\.jpg$',
            r'\.png$',
            r'\.gif$'
        ]
    
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from the news website."""
        try:
            # Start with the main page
            response = self.make_request(self.config.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find article links
            article_urls = set()
            
            for selector in self.article_selectors:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href:
                        # Convert relative URLs to absolute
                        full_url = urljoin(self.config.base_url, href)
                        
                        # Filter out unwanted URLs
                        if self._is_valid_article_url(full_url):
                            article_urls.add(full_url)
            
            self.logger.info(f"Found {len(article_urls)} article URLs")
            
            # Yield unique URLs
            for url in article_urls:
                yield url
                
        except Exception as e:
            self.logger.error(f"Failed to get article URLs: {e}")
            raise
    
    def _is_valid_article_url(self, url: str) -> bool:
        """Check if URL is a valid article URL."""
        # Check against exclude patterns
        for pattern in self.exclude_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        # Must be from the same domain
        base_domain = urlparse(self.config.base_url).netloc
        url_domain = urlparse(url).netloc
        
        if url_domain != base_domain:
            return False
        
        # Must be HTTP/HTTPS
        if not url.startswith(('http://', 'https://')):
            return False
        
        return True
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single article from the given URL."""
        try:
            self.logger.debug(f"Scraping article: {url}")
            
            # Make request
            response = self.make_request(url)
            
            # Parse content
            article = self.parse_content(response.text, url)
            
            if article:
                self.logger.debug(f"Successfully scraped: {article.title[:50]}...")
                return article
            else:
                self.logger.debug(f"Article not AI-related, skipping: {url}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to scrape article {url}: {e}")
            return None
    
    def get_sitemap_urls(self) -> List[str]:
        """Try to get article URLs from sitemap."""
        sitemap_urls = [
            urljoin(self.config.base_url, '/sitemap.xml'),
            urljoin(self.config.base_url, '/sitemap_index.xml'),
            urljoin(self.config.base_url, '/news-sitemap.xml'),
            urljoin(self.config.base_url, '/robots.txt')  # Check robots.txt for sitemap
        ]
        
        article_urls = []
        
        for sitemap_url in sitemap_urls:
            try:
                response = self.make_request(sitemap_url)
                
                if 'robots.txt' in sitemap_url:
                    # Parse robots.txt for sitemap URLs
                    for line in response.text.split('\n'):
                        if line.lower().startswith('sitemap:'):
                            actual_sitemap = line.split(':', 1)[1].strip()
                            article_urls.extend(self._parse_sitemap(actual_sitemap))
                else:
                    # Parse XML sitemap
                    article_urls.extend(self._parse_sitemap(sitemap_url, response.text))
                    
            except Exception as e:
                self.logger.debug(f"Could not access sitemap {sitemap_url}: {e}")
                continue
        
        return article_urls
    
    def _parse_sitemap(self, sitemap_url: str, content: str = None) -> List[str]:
        """Parse XML sitemap for article URLs."""
        if content is None:
            try:
                response = self.make_request(sitemap_url)
                content = response.text
            except:
                return []
        
        article_urls = []
        
        try:
            soup = BeautifulSoup(content, 'xml')
            
            # Look for URL entries
            urls = soup.find_all('url')
            for url_entry in urls:
                loc = url_entry.find('loc')
                if loc and loc.text:
                    url = loc.text.strip()
                    if self._is_valid_article_url(url):
                        article_urls.append(url)
            
            # Look for sitemap index entries
            sitemaps = soup.find_all('sitemap')
            for sitemap_entry in sitemaps:
                loc = sitemap_entry.find('loc')
                if loc and loc.text:
                    # Recursively parse sub-sitemaps
                    sub_urls = self._parse_sitemap(loc.text.strip())
                    article_urls.extend(sub_urls)
                    
        except Exception as e:
            self.logger.debug(f"Failed to parse sitemap {sitemap_url}: {e}")
        
        return article_urls
    
    def get_rss_urls(self) -> List[str]:
        """Try to get article URLs from RSS feeds."""
        rss_urls = [
            urljoin(self.config.base_url, '/feed'),
            urljoin(self.config.base_url, '/rss'),
            urljoin(self.config.base_url, '/feed.xml'),
            urljoin(self.config.base_url, '/rss.xml'),
            urljoin(self.config.base_url, '/index.xml')
        ]
        
        article_urls = []
        
        for rss_url in rss_urls:
            try:
                response = self.make_request(rss_url)
                soup = BeautifulSoup(response.text, 'xml')
                
                # Parse RSS items
                items = soup.find_all('item')
                for item in items:
                    link = item.find('link')
                    if link and link.text:
                        url = link.text.strip()
                        if self._is_valid_article_url(url):
                            article_urls.append(url)
                
                # Parse Atom entries
                entries = soup.find_all('entry')
                for entry in entries:
                    link = entry.find('link')
                    if link:
                        href = link.get('href')
                        if href and self._is_valid_article_url(href):
                            article_urls.append(href)
                            
            except Exception as e:
                self.logger.debug(f"Could not access RSS feed {rss_url}: {e}")
                continue
        
        return article_urls
    
    def get_all_article_urls(self) -> Iterator[str]:
        """Get article URLs from multiple sources."""
        seen_urls = set()
        
        # Get URLs from main page
        for url in self.get_article_urls():
            if url not in seen_urls:
                seen_urls.add(url)
                yield url
        
        # Get URLs from sitemap
        for url in self.get_sitemap_urls():
            if url not in seen_urls:
                seen_urls.add(url)
                yield url
        
        # Get URLs from RSS
        for url in self.get_rss_urls():
            if url not in seen_urls:
                seen_urls.add(url)
                yield url
