"""
NewsAPI scraper for professional news sources.
Implements keyword-based news searching with rate limiting.
"""

import os
import time
import logging
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
import threading
from urllib.parse import urlencode

from ..base.scraper import Base<PERSON>craper, ScraperConfig
from models.database import Article, SourceType

logger = logging.getLogger(__name__)


class NewsAPIRateLimiter:
    """Rate limiter for NewsAPI with hourly limits."""
    
    def __init__(self, requests_per_hour: int = 250, min_request_interval: float = 14.4):
        self.requests_per_hour = requests_per_hour
        self.min_request_interval = min_request_interval
        self.request_times = []
        self.lock = threading.Lock()
        
    def wait_if_needed(self):
        """Wait if necessary to respect rate limits."""
        with self.lock:
            now = time.time()
            
            # Remove requests older than 1 hour
            hour_ago = now - 3600
            self.request_times = [t for t in self.request_times if t > hour_ago]
            
            # Check if we need to wait for hourly limit
            if len(self.request_times) >= self.requests_per_hour:
                oldest_request = min(self.request_times)
                wait_time = oldest_request + 3600 - now
                if wait_time > 0:
                    logger.warning(f"NewsAPI hourly rate limit reached. Sleeping for {wait_time:.1f} seconds")
                    time.sleep(wait_time)
                    return
            
            # Check minimum interval between requests
            if self.request_times:
                last_request = max(self.request_times)
                time_since_last = now - last_request
                if time_since_last < self.min_request_interval:
                    wait_time = self.min_request_interval - time_since_last
                    logger.debug(f"NewsAPI rate limiting: waiting {wait_time:.1f} seconds")
                    time.sleep(wait_time)
            
            # Record this request
            self.request_times.append(time.time())


@dataclass
class NewsAPIScraperConfig(ScraperConfig):
    """Configuration for NewsAPI scraper."""
    
    # API Configuration
    api_key: str = ""
    base_url: str = "https://newsapi.org/v2"
    
    # Search Configuration
    keywords: List[str] = field(default_factory=lambda: [
        "artificial intelligence", "machine learning", "deep learning", 
        "neural networks", "LLM", "large language model", "ChatGPT", 
        "OpenAI", "Google AI", "AI research", "computer vision",
        "natural language processing", "NLP", "transformer", "GPT"
    ])
    
    # Content Filtering
    domains: List[str] = field(default_factory=lambda: [
        "techcrunch.com", "venturebeat.com", "wired.com", "arstechnica.com",
        "theverge.com", "engadget.com", "thenextweb.com", "zdnet.com",
        "reuters.com", "bloomberg.com", "wsj.com", "ft.com"
    ])
    
    exclude_domains: List[str] = field(default_factory=lambda: [
        "reddit.com", "twitter.com", "facebook.com", "youtube.com"
    ])
    
    # Search Parameters
    language: str = "en"
    sort_by: str = "publishedAt"  # relevancy, popularity, publishedAt
    page_size: int = 100
    max_pages: int = 3
    
    # Date Range (days back from now)
    days_back: int = 7
    
    # Rate Limiting
    requests_per_hour: int = 250
    min_request_interval: float = 14.4  # seconds
    
    # Source Credibility Scoring
    high_credibility_sources: List[str] = field(default_factory=lambda: [
        "reuters", "bloomberg", "wsj", "ft", "nature", "science", "ieee"
    ])
    
    medium_credibility_sources: List[str] = field(default_factory=lambda: [
        "techcrunch", "wired", "arstechnica", "theverge", "venturebeat"
    ])
    
    def __post_init__(self):
        
        # Load API key from environment
        if not self.api_key:
            self.api_key = os.getenv("NEWS_API_KEY", "")
            if not self.api_key:
                raise ValueError("NewsAPI API key is required. Set NEWS_API_KEY environment variable.")
        
        # Load rate limiting from environment variables
        self.requests_per_hour = int(os.getenv("NEWSAPI_REQUESTS_PER_HOUR", self.requests_per_hour))
        self.min_request_interval = float(os.getenv("NEWSAPI_MIN_REQUEST_INTERVAL", self.min_request_interval))


class NewsAPIScraper(BaseScraper):
    """Scraper for NewsAPI professional news sources."""
    
    def __init__(self, config: NewsAPIScraperConfig):
        super().__init__(config)
        self.config = config
        self.rate_limiter = NewsAPIRateLimiter(
            requests_per_hour=config.requests_per_hour,
            min_request_interval=config.min_request_interval
        )
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AI-News-Scraper/1.0',
            'X-Api-Key': config.api_key
        })
    
    def get_article_urls(self) -> List[str]:
        """Get article URLs from NewsAPI."""
        urls = []
        
        try:
            # Search for each keyword
            for keyword in self.config.keywords:
                logger.info(f"Searching NewsAPI for keyword: {keyword}")
                keyword_urls = self._search_keyword(keyword)
                urls.extend(keyword_urls)
                
                # Avoid overwhelming the API
                if len(urls) > 500:
                    logger.info(f"Collected {len(urls)} URLs, stopping to avoid overwhelming API")
                    break
            
            # Remove duplicates while preserving order
            seen = set()
            unique_urls = []
            for url in urls:
                if url not in seen:
                    seen.add(url)
                    unique_urls.append(url)
            
            logger.info(f"Found {len(unique_urls)} unique articles from NewsAPI")
            return unique_urls
            
        except Exception as e:
            logger.error(f"Error getting article URLs from NewsAPI: {e}")
            return []
    
    def _search_keyword(self, keyword: str) -> List[str]:
        """Search for articles by keyword."""
        urls = []
        
        # Calculate date range
        to_date = datetime.now()
        from_date = to_date - timedelta(days=self.config.days_back)
        
        for page in range(1, self.config.max_pages + 1):
            try:
                self.rate_limiter.wait_if_needed()
                
                # Build search parameters
                params = {
                    'q': keyword,
                    'language': self.config.language,
                    'sortBy': self.config.sort_by,
                    'pageSize': self.config.page_size,
                    'page': page,
                    'from': from_date.strftime('%Y-%m-%d'),
                    'to': to_date.strftime('%Y-%m-%d'),
                    'apiKey': self.config.api_key
                }
                
                # Add domain filtering
                if self.config.domains:
                    params['domains'] = ','.join(self.config.domains)
                
                if self.config.exclude_domains:
                    params['excludeDomains'] = ','.join(self.config.exclude_domains)
                
                # Make API request
                url = f"{self.config.base_url}/everything"
                logger.debug(f"NewsAPI request: {url} with params: {params}")
                
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if data.get('status') != 'ok':
                    logger.error(f"NewsAPI error: {data.get('message', 'Unknown error')}")
                    break
                
                articles = data.get('articles', [])
                if not articles:
                    logger.info(f"No more articles found for keyword '{keyword}' on page {page}")
                    break
                
                # Extract URLs
                page_urls = [article['url'] for article in articles if article.get('url')]
                urls.extend(page_urls)
                
                logger.info(f"Found {len(page_urls)} articles for '{keyword}' on page {page}")
                
                # Check if we've reached the end
                total_results = data.get('totalResults', 0)
                if page * self.config.page_size >= total_results:
                    break
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error searching NewsAPI for '{keyword}' page {page}: {e}")
                break
            except Exception as e:
                logger.error(f"Error searching NewsAPI for '{keyword}' page {page}: {e}")
                break
        
        return urls
    
    def scrape_article(self, url: str) -> Optional[Article]:
        """Scrape article content from NewsAPI data."""
        try:
            # For NewsAPI, we get the article data from the search results
            # We'll need to make another API call to get full content if needed
            article_data = self._get_article_data(url)
            if not article_data:
                return None
            
            # Calculate credibility score
            credibility_score = self._calculate_credibility_score(article_data.get('source', {}).get('name', ''))
            
            # Create Article object
            article = Article(
                title=article_data.get('title', ''),
                content=article_data.get('content', '') or article_data.get('description', ''),
                url=url,
                source_type=SourceType.NEWS,
                source_id=f"newsapi_{article_data.get('source', {}).get('id', 'unknown')}",
                published_at=self._parse_date(article_data.get('publishedAt')),
                author=article_data.get('author'),
                engagement_metrics={
                    'source_id': article_data.get('source', {}).get('id'),
                    'url_to_image': article_data.get('urlToImage'),
                    'credibility_score': credibility_score,
                    'api_source': 'newsapi'
                }
            )
            
            return article
            
        except Exception as e:
            logger.error(f"Error scraping article from {url}: {e}")
            return None
    
    def _get_article_data(self, url: str) -> Optional[Dict[str, Any]]:
        """Get article data from NewsAPI (cached from search results)."""
        # In a real implementation, we might cache search results
        # For now, we'll return None and rely on the search results
        # This is a placeholder for potential future enhancement
        return None
    
    def _calculate_credibility_score(self, source_name: str) -> float:
        """Calculate credibility score based on source."""
        source_lower = source_name.lower()
        
        # Check high credibility sources
        for high_source in self.config.high_credibility_sources:
            if high_source in source_lower:
                return 0.9
        
        # Check medium credibility sources
        for medium_source in self.config.medium_credibility_sources:
            if medium_source in source_lower:
                return 0.7
        
        # Default credibility for other sources
        return 0.5
    
    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string from NewsAPI."""
        if not date_str:
            return None
        
        try:
            # NewsAPI returns ISO format: 2025-06-30T10:53:20Z
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except Exception as e:
            logger.warning(f"Could not parse date '{date_str}': {e}")
            return None
