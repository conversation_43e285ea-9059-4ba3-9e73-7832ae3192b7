"""
Reddit scraper for AI/ML related posts and discussions.
"""

import json
import time
from typing import Iterator, Optional, List, Dict
from urllib.parse import urljoin
from datetime import datetime

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError, RateLimitError


class RedditScraper(BaseScraper):
    """Scraper for Reddit AI/ML communities."""
    
    def __init__(self, config: ScraperConfig, subreddits: List[str] = None):
        super().__init__(config)
        
        # Default AI/ML subreddits
        self.subreddits = subreddits or [
            'MachineLearning',
            'artificial',
            'deeplearning',
            'LanguageTechnology',
            'compsci',
            'programming',
            'technology',
            'singularity',
            'OpenAI',
            'ChatGPT',
            'LocalLLaMA'
        ]
        
        # Reddit API configuration
        self.api_base = "https://www.reddit.com"
        self.user_agent = "AI News Scraper 1.0"
        
        # Update session headers for Reddit
        self.session.headers.update({
            'User-Agent': self.user_agent
        })
    
    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from Reddit posts."""
        for subreddit in self.subreddits:
            try:
                # Get hot posts from subreddit
                for post_data in self._get_subreddit_posts(subreddit, sort='hot', limit=25):
                    if self._is_relevant_post(post_data):
                        yield self._get_post_url(post_data)
                
                # Get new posts from subreddit
                for post_data in self._get_subreddit_posts(subreddit, sort='new', limit=25):
                    if self._is_relevant_post(post_data):
                        yield self._get_post_url(post_data)
                        
            except Exception as e:
                self.logger.error(f"Failed to get posts from r/{subreddit}: {e}")
                continue
    
    def _get_subreddit_posts(self, subreddit: str, sort: str = 'hot', limit: int = 25) -> List[Dict]:
        """Get posts from a subreddit using Reddit's JSON API."""
        url = f"{self.api_base}/r/{subreddit}/{sort}.json"
        params = {'limit': limit}
        
        try:
            response = self.make_request(url, params=params)
            data = response.json()
            
            if 'data' in data and 'children' in data['data']:
                return [child['data'] for child in data['data']['children']]
            else:
                self.logger.warning(f"Unexpected Reddit API response format for r/{subreddit}")
                return []
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse Reddit JSON for r/{subreddit}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Failed to fetch r/{subreddit}: {e}")
            return []
    
    def _is_relevant_post(self, post_data: Dict) -> bool:
        """Check if Reddit post is relevant to AI/ML."""
        # Check if post is removed or deleted
        if post_data.get('removed_by_category') or post_data.get('selftext') == '[deleted]':
            return False
        
        # Check minimum score
        score = post_data.get('score', 0)
        if score < 5:  # Minimum upvotes
            return False
        
        # Check if it's a text post or link post
        post_hint = post_data.get('post_hint', '')
        if post_hint in ['image', 'video']:
            return False
        
        # Check for AI/ML keywords in title
        title = post_data.get('title', '').lower()
        ai_keywords = [
            'ai', 'artificial intelligence', 'machine learning', 'ml', 'deep learning',
            'neural network', 'llm', 'gpt', 'bert', 'transformer', 'nlp',
            'computer vision', 'reinforcement learning', 'chatgpt', 'openai',
            'anthropic', 'google ai', 'deepmind', 'tensorflow', 'pytorch'
        ]
        
        for keyword in ai_keywords:
            if keyword in title:
                return True
        
        return False
    
    def _get_post_url(self, post_data: Dict) -> str:
        """Get the URL for a Reddit post."""
        permalink = post_data.get('permalink', '')
        return urljoin(self.api_base, permalink)
    
    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a Reddit post and convert to ParsedArticle."""
        try:
            # Get post data from Reddit API
            json_url = url.rstrip('/') + '.json'
            response = self.make_request(json_url)
            data = response.json()
            
            if not data or len(data) < 1:
                return None
            
            post_data = data[0]['data']['children'][0]['data']
            
            # Extract post information
            title = post_data.get('title', '')
            selftext = post_data.get('selftext', '')
            url_field = post_data.get('url', '')
            author = post_data.get('author', '')
            created_utc = post_data.get('created_utc', 0)
            subreddit = post_data.get('subreddit', '')
            score = post_data.get('ups', 0)
            num_comments = post_data.get('num_comments', 0)
            
            # Determine content
            if selftext:
                # Self post - use selftext as content
                content = selftext
                article_url = url
            elif url_field and url_field != url:
                # Link post - try to scrape the linked content
                try:
                    linked_response = self.make_request(url_field)
                    linked_article = self.parse_content(linked_response.text, url_field)
                    if linked_article:
                        # Use the linked article but keep Reddit metadata
                        content = linked_article.content
                        article_url = url_field
                    else:
                        # Fallback to Reddit post
                        content = f"Reddit discussion about: {url_field}"
                        article_url = url
                except:
                    # Fallback to Reddit post
                    content = f"Reddit discussion about: {url_field}"
                    article_url = url
            else:
                # Discussion post
                content = f"Reddit discussion: {title}"
                article_url = url
            
            # Create ParsedArticle
            published_at = datetime.fromtimestamp(created_utc) if created_utc else None
            
            # Add Reddit-specific metadata to content
            reddit_metadata = f"\n\nReddit Metadata:\n"
            reddit_metadata += f"Subreddit: r/{subreddit}\n"
            reddit_metadata += f"Score: {score} upvotes\n"
            reddit_metadata += f"Comments: {num_comments}\n"
            reddit_metadata += f"Original URL: {url}"
            
            content += reddit_metadata
            
            article = ParsedArticle(
                title=title,
                content=content,
                url=article_url,
                author=f"u/{author}" if author else None,
                published_at=published_at,
                tags=[f"reddit", f"r/{subreddit}"],
                summary=title,  # Use title as summary for Reddit posts
                source_name="Reddit"
            )
            
            # Check if AI-related (should be true due to filtering)
            if self.content_parser.is_ai_related(article):
                return article
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to scrape Reddit post {url}: {e}")
            return None
    
    def get_trending_ai_posts(self) -> Iterator[str]:
        """Get trending AI posts across all monitored subreddits."""
        all_posts = []
        
        # Collect posts from all subreddits
        for subreddit in self.subreddits:
            try:
                posts = self._get_subreddit_posts(subreddit, sort='hot', limit=10)
                for post in posts:
                    if self._is_relevant_post(post):
                        post['subreddit_name'] = subreddit
                        all_posts.append(post)
            except Exception as e:
                self.logger.error(f"Failed to get trending posts from r/{subreddit}: {e}")
                continue
        
        # Sort by score (upvotes)
        all_posts.sort(key=lambda p: p.get('score', 0), reverse=True)
        
        # Return top posts
        for post in all_posts[:50]:  # Top 50 trending posts
            yield self._get_post_url(post)
    
    def search_ai_posts(self, query: str, limit: int = 25) -> Iterator[str]:
        """Search for AI-related posts using Reddit search."""
        search_url = f"{self.api_base}/search.json"
        params = {
            'q': f"{query} (subreddit:MachineLearning OR subreddit:artificial OR subreddit:deeplearning)",
            'sort': 'relevance',
            'limit': limit,
            't': 'week'  # Past week
        }
        
        try:
            response = self.make_request(search_url, params=params)
            data = response.json()
            
            if 'data' in data and 'children' in data['data']:
                for child in data['data']['children']:
                    post_data = child['data']
                    if self._is_relevant_post(post_data):
                        yield self._get_post_url(post_data)
                        
        except Exception as e:
            self.logger.error(f"Failed to search Reddit for '{query}': {e}")
    
    def get_comments(self, post_url: str, limit: int = 10) -> List[Dict]:
        """Get top comments from a Reddit post."""
        json_url = post_url.rstrip('/') + '.json'
        params = {'limit': limit}
        
        try:
            response = self.make_request(json_url, params=params)
            data = response.json()
            
            if len(data) >= 2 and 'data' in data[1]:
                comments = []
                for child in data[1]['data']['children']:
                    if child['kind'] == 't1':  # Comment
                        comment_data = child['data']
                        if comment_data.get('body') and comment_data['body'] != '[deleted]':
                            comments.append({
                                'author': comment_data.get('author', ''),
                                'body': comment_data.get('body', ''),
                                'score': comment_data.get('score', 0),
                                'created_utc': comment_data.get('created_utc', 0)
                            })
                
                return sorted(comments, key=lambda c: c['score'], reverse=True)
                
        except Exception as e:
            self.logger.error(f"Failed to get comments for {post_url}: {e}")
        
        return []
