"""
Reddit scraper implementation using PRAW (Python Reddit API Wrapper).

This module provides comprehensive Reddit scraping capabilities including:
- Subreddit monitoring
- Post and comment collection
- Engagement metrics tracking
- Real-time hot post monitoring
"""

import logging
import time
import threading
from datetime import datetime, timezone, timedelta
from typing import Iterator, List, Optional, Dict, Any
from dataclasses import dataclass, field

import praw
from praw.models import Submission, Comment, Subreddit
from prawcore.exceptions import PrawcoreException

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError, ConfigurationError
from config.settings import settings

# Optional import for advanced comment analysis
try:
    from ..analysis.reddit_comment_analyzer import RedditCommentAnalyzer
    COMMENT_ANALYSIS_AVAILABLE = True
except ImportError:
    COMMENT_ANALYSIS_AVAILABLE = False

# Optional import for engagement tracking
try:
    from ..metrics.reddit_engagement_tracker import RedditEngagementTracker
    ENGAGEMENT_TRACKING_AVAILABLE = True
except ImportError:
    ENGAGEMENT_TRACKING_AVAILABLE = False

# Optional import for duplicate detection
try:
    from ..deduplication.reddit_deduplicator import RedditDeduplicator
    DEDUPLICATION_AVAILABLE = True
except ImportError:
    DEDUPLICATION_AVAILABLE = False


class RateLimiter:
    """Rate limiter for API requests with configurable limits."""

    def __init__(self, requests_per_minute: int = 60):
        """
        Initialize rate limiter.

        Args:
            requests_per_minute: Maximum requests allowed per minute
        """
        self.requests_per_minute = requests_per_minute
        self.min_interval = 60.0 / requests_per_minute  # seconds between requests
        self.last_request_time = 0.0
        self.lock = threading.Lock()

    def wait_if_needed(self):
        """Wait if necessary to respect rate limits."""
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time

            if time_since_last < self.min_interval:
                sleep_time = self.min_interval - time_since_last
                time.sleep(sleep_time)

            self.last_request_time = time.time()


@dataclass
class RedditScraperConfig(ScraperConfig):
    """Configuration for Reddit scraper."""

    # Reddit-specific settings
    subreddits: List[str] = field(default_factory=lambda: [
        'MachineLearning',
        'artificial',
        'ArtificialIntelligence',
        'deeplearning',
        'MLQuestions',
        'compsci',
        'programming',
        'technology',
        'singularity',
        'OpenAI',
        'ChatGPT',
        'LocalLLaMA'
    ])

    # Scraping parameters
    max_posts_per_subreddit: int = 100
    include_comments: bool = True
    max_comments_per_post: int = 50
    min_score_threshold: int = 1  # Minimum upvotes to consider
    max_age_days: int = 7  # Maximum age of posts to scrape

    # Monitoring settings
    monitor_hot: bool = True
    monitor_new: bool = True
    monitor_top: bool = False
    time_filter: str = 'day'  # hour, day, week, month, year, all

    # Rate limiting (Reddit free tier limits)
    # Reddit API allows 60 requests per minute for free tier
    requests_per_minute: int = 60
    # Additional safety margin - delay between requests in seconds
    min_request_interval: float = 1.0  # 1 second between requests for safety

    def __post_init__(self):
        """Validate Reddit configuration."""
        # Check environment variables directly if settings don't have credentials
        import os
        client_id = settings.reddit.client_id or os.getenv("REDDIT_CLIENT_ID")
        client_secret = settings.reddit.client_secret or os.getenv("REDDIT_CLIENT_SECRET")

        if not client_id or not client_secret:
            raise ConfigurationError(
                "Reddit API credentials not configured. Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET"
            )

        # Load rate limiting from environment variables
        self.requests_per_minute = int(os.getenv("REDDIT_REQUESTS_PER_MINUTE", self.requests_per_minute))
        self.min_request_interval = float(os.getenv("REDDIT_MIN_REQUEST_INTERVAL", self.min_request_interval))


class RedditScraper(BaseScraper):
    """Reddit scraper using PRAW library."""

    def __init__(self, config: RedditScraperConfig):
        super().__init__(config)
        self.config: RedditScraperConfig = config
        self.reddit: Optional[praw.Reddit] = None
        self._setup_reddit_client()

        # Initialize comment analyzer if available
        self.comment_analyzer = None
        if COMMENT_ANALYSIS_AVAILABLE:
            self.comment_analyzer = RedditCommentAnalyzer()

        # Initialize engagement tracker if available
        self.engagement_tracker = None
        if ENGAGEMENT_TRACKING_AVAILABLE and self.reddit:
            self.engagement_tracker = RedditEngagementTracker(self.reddit)

        # Initialize deduplicator if available
        self.deduplicator = None
        if DEDUPLICATION_AVAILABLE:
            self.deduplicator = RedditDeduplicator()

        # Initialize rate limiter
        self.rate_limiter = RateLimiter(requests_per_minute=self.config.requests_per_minute)

        # Statistics tracking - merge with base stats
        self.stats.update({
            'posts_scraped': 0,
            'comments_scraped': 0,
            'subreddits_processed': 0,
            'api_calls': 0,
            'errors': 0,
            'high_quality_comments': 0,
            'expert_contributors': 0,
            'engagement_tracked': 0,
            'viral_posts': 0,
            'high_engagement_posts': 0,
            'duplicates_detected': 0,
            'unique_posts': 0
        })

    def _setup_reddit_client(self):
        """Initialize Reddit API client."""
        try:
            import os
            # Use environment variables as fallback if settings don't have credentials
            client_id = settings.reddit.client_id or os.getenv("REDDIT_CLIENT_ID")
            client_secret = settings.reddit.client_secret or os.getenv("REDDIT_CLIENT_SECRET")
            user_agent = settings.reddit.user_agent or os.getenv("REDDIT_USER_AGENT", "AI_News_Scraper/1.0")

            self.reddit = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent,
                # Read-only mode (no authentication required for public content)
                username=None,
                password=None
            )

            # Test the connection
            self.reddit.user.me()  # This will be None for read-only mode
            self.logger.info("Reddit API client initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Reddit client: {e}")
            raise ConfigurationError(f"Reddit API setup failed: {str(e)}")

    def get_article_urls(self) -> Iterator[str]:
        """Get article URLs from Reddit posts."""
        for subreddit_name in self.config.subreddits:
            try:
                # Rate limit before API call
                self.rate_limiter.wait_if_needed()
                self.stats['api_calls'] += 1

                subreddit = self.reddit.subreddit(subreddit_name)
                self.logger.info(f"Processing subreddit: r/{subreddit_name}")

                # Get posts from different sorting methods
                post_generators = []

                if self.config.monitor_hot:
                    # Rate limit before API call
                    self.rate_limiter.wait_if_needed()
                    self.stats['api_calls'] += 1
                    post_generators.append(('hot', subreddit.hot(limit=self.config.max_posts_per_subreddit)))

                if self.config.monitor_new:
                    # Rate limit before API call
                    self.rate_limiter.wait_if_needed()
                    self.stats['api_calls'] += 1
                    post_generators.append(('new', subreddit.new(limit=self.config.max_posts_per_subreddit)))

                if self.config.monitor_top:
                    # Rate limit before API call
                    self.rate_limiter.wait_if_needed()
                    self.stats['api_calls'] += 1
                    post_generators.append(('top', subreddit.top(
                        time_filter=self.config.time_filter,
                        limit=self.config.max_posts_per_subreddit
                    )))

                seen_posts = set()

                for sort_type, posts in post_generators:
                    self.logger.debug(f"Processing {sort_type} posts from r/{subreddit_name}")

                    for post in posts:
                        if post.id in seen_posts:
                            continue

                        seen_posts.add(post.id)

                        # Filter by score and age
                        if post.score < self.config.min_score_threshold:
                            continue

                        post_age = datetime.now(timezone.utc) - datetime.fromtimestamp(
                            post.created_utc, tz=timezone.utc
                        )

                        if post_age.days > self.config.max_age_days:
                            continue

                        # Yield the Reddit post URL
                        yield f"https://reddit.com{post.permalink}"

                        self.stats['posts_scraped'] += 1

                self.stats['subreddits_processed'] += 1

            except Exception as e:
                self.logger.error(f"Error processing subreddit r/{subreddit_name}: {e}")
                self.stats['errors'] += 1
                continue

    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a Reddit post and convert to ParsedArticle."""
        try:
            # Extract post ID from URL
            if '/comments/' not in url:
                self.logger.warning(f"Invalid Reddit URL format: {url}")
                return None

            # Rate limit before API call
            self.rate_limiter.wait_if_needed()

            # Get submission from URL
            submission = self.reddit.submission(url=url)
            self.stats['api_calls'] += 1

            # Parse the submission
            article = self._parse_reddit_submission(submission)

            if article and self.config.include_comments:
                # Add top comments as additional content
                comments = self._get_top_comments(submission)
                if comments:
                    article.content += "\n\n--- Top Comments ---\n" + comments

            return article

        except Exception as e:
            self.logger.error(f"Failed to scrape Reddit post {url}: {e}")
            self.stats['errors'] += 1
            return None

    def _parse_reddit_submission(self, submission: Submission) -> Optional[ParsedArticle]:
        """Parse a Reddit submission into a ParsedArticle."""
        try:
            # Determine content based on post type
            content = ""

            if submission.selftext:
                # Text post
                content = submission.selftext
            elif submission.url and submission.url != f"https://reddit.com{submission.permalink}":
                # Link post - include the linked URL
                content = f"Link: {submission.url}"
                if hasattr(submission, 'preview') and submission.preview:
                    # Try to get preview text if available
                    content += f"\n\nPreview available at: {submission.url}"

            # Create tags from subreddit and flair
            tags = [f"r/{submission.subreddit.display_name}"]
            if submission.link_flair_text:
                tags.append(submission.link_flair_text)

            # Create ParsedArticle
            article = ParsedArticle(
                title=submission.title,
                content=content,
                url=f"https://reddit.com{submission.permalink}",
                author=str(submission.author) if submission.author else "deleted",
                published_at=datetime.fromtimestamp(submission.created_utc, tz=timezone.utc),
                source_name=f"Reddit - r/{submission.subreddit.display_name}",
                tags=tags
            )

            # Add Reddit-specific metadata
            article.metadata = {
                'reddit_id': submission.id,
                'subreddit': submission.subreddit.display_name,
                'score': submission.score,
                'upvote_ratio': submission.upvote_ratio,
                'num_comments': submission.num_comments,
                'gilded': submission.gilded,
                'stickied': submission.stickied,
                'over_18': submission.over_18,
                'spoiler': submission.spoiler,
                'locked': submission.locked,
                'flair': submission.link_flair_text,
                'domain': getattr(submission, 'domain', None),
                'external_url': submission.url if submission.url != f"https://reddit.com{submission.permalink}" else None
            }

            # Track engagement metrics if available
            if self.engagement_tracker:
                try:
                    engagement_metrics = self.engagement_tracker.track_post_engagement(
                        submission, include_comments=self.config.include_comments
                    )

                    # Add engagement data to metadata
                    article.metadata.update({
                        'engagement_score': round(engagement_metrics.engagement_score, 3),
                        'viral_potential': round(engagement_metrics.viral_potential, 3),
                        'discussion_quality': round(engagement_metrics.discussion_quality, 3),
                        'comments_per_hour': round(engagement_metrics.comments_per_hour, 2),
                        'score_per_hour': round(engagement_metrics.score_per_hour, 2),
                        'comment_score_avg': round(engagement_metrics.comment_score_avg, 2),
                        'comment_depth_max': engagement_metrics.comment_depth_max,
                        'total_awards_received': engagement_metrics.total_awards_received,
                        'award_types': engagement_metrics.award_types[:5]  # Top 5 award types
                    })

                    # Update statistics
                    self.stats['engagement_tracked'] += 1
                    if engagement_metrics.viral_potential > 0.7:
                        self.stats['viral_posts'] += 1
                    if engagement_metrics.engagement_score > 0.7:
                        self.stats['high_engagement_posts'] += 1

                except Exception as e:
                    self.logger.debug(f"Engagement tracking failed: {e}")

            # Check for duplicates if deduplicator is available
            if self.deduplicator:
                try:
                    # Prepare post data for duplicate detection
                    post_data = {
                        'id': article.url,  # Use URL as unique ID
                        'reddit_id': submission.id,
                        'title': article.title,
                        'content': article.content,
                        'url': article.url,
                        'external_url': article.metadata.get('external_url'),
                        'subreddit': article.metadata['subreddit'],
                        'author': article.author,
                        'created_utc': article.published_at
                    }

                    # Find duplicates
                    duplicates = self.deduplicator.find_duplicates(post_data)

                    if duplicates:
                        # Add duplicate information to metadata
                        article.metadata['duplicates_found'] = len(duplicates)
                        article.metadata['duplicate_matches'] = [
                            {
                                'original_post_id': dup.original_post_id,
                                'match_type': dup.match_type,
                                'confidence': round(dup.confidence, 3),
                                'similarity_score': round(dup.similarity_score, 3)
                            }
                            for dup in duplicates[:5]  # Limit to top 5 matches
                        ]

                        # Update statistics
                        self.stats['duplicates_detected'] += len(duplicates)

                        # Log high-confidence duplicates
                        high_confidence_dups = [d for d in duplicates if d.confidence > 0.8]
                        if high_confidence_dups:
                            self.logger.info(
                                f"High-confidence duplicates found for post {submission.id}: "
                                f"{len(high_confidence_dups)} matches"
                            )
                    else:
                        self.stats['unique_posts'] += 1

                except Exception as e:
                    self.logger.debug(f"Duplicate detection failed: {e}")

            return article

        except Exception as e:
            self.logger.error(f"Failed to parse Reddit submission: {e}")
            return None

    def _get_top_comments(self, submission: Submission) -> str:
        """Get top comments from a Reddit submission with optional advanced analysis."""
        try:
            # Rate limit before API call
            self.rate_limiter.wait_if_needed()
            self.stats['api_calls'] += 1

            # Expand comment tree
            submission.comments.replace_more(limit=0)

            comments_text = []
            comment_count = 0
            analyzed_comments = []

            for comment in submission.comments.list():
                if comment_count >= self.config.max_comments_per_post:
                    break

                if isinstance(comment, Comment) and comment.body != '[deleted]':
                    # Basic comment formatting
                    comment_text = f"[{comment.author}] ({comment.score} points): {comment.body}"

                    # Advanced analysis if available
                    if self.comment_analyzer:
                        try:
                            analysis = self.comment_analyzer.analyze_comment(comment)
                            analyzed_comments.append(analysis)

                            # Add quality indicator for high-quality comments
                            if analysis.quality_score > 0.7:
                                comment_text += " [HIGH QUALITY]"
                                self.stats['high_quality_comments'] += 1

                            # Add technical terms if found
                            if analysis.technical_terms:
                                tech_terms = ", ".join(analysis.technical_terms[:3])  # Show first 3
                                comment_text += f" [TECH: {tech_terms}]"

                        except Exception as e:
                            self.logger.debug(f"Comment analysis failed: {e}")

                    comments_text.append(comment_text)
                    comment_count += 1
                    self.stats['comments_scraped'] += 1

            # Add thread analysis summary if available
            if self.comment_analyzer and analyzed_comments:
                try:
                    # Get expert contributors
                    experts = self.comment_analyzer.get_expert_contributors(analyzed_comments)
                    if experts:
                        self.stats['expert_contributors'] += len(experts)
                        expert_summary = f"\n\n--- Expert Contributors ---\n{', '.join(experts[:5])}"
                        comments_text.append(expert_summary)

                except Exception as e:
                    self.logger.debug(f"Expert analysis failed: {e}")

            return "\n\n".join(comments_text)

        except Exception as e:
            self.logger.error(f"Failed to get comments: {e}")
            return ""

    def analyze_thread(self, url: str) -> Optional[Dict[str, Any]]:
        """Get detailed analysis of a Reddit thread."""
        if not self.comment_analyzer:
            self.logger.warning("Comment analyzer not available for thread analysis")
            return None

        try:
            # Get submission from URL
            submission = self.reddit.submission(url=url)

            # Perform thread analysis
            thread_analysis = self.comment_analyzer.analyze_thread(
                submission, max_comments=self.config.max_comments_per_post
            )

            return {
                'url': url,
                'title': submission.title,
                'total_comments': thread_analysis.total_comments,
                'max_depth': thread_analysis.max_depth,
                'avg_score': thread_analysis.avg_score,
                'top_contributors': thread_analysis.top_contributors,
                'discussion_topics': thread_analysis.discussion_topics,
                'sentiment_distribution': thread_analysis.sentiment_distribution,
                'technical_depth': thread_analysis.technical_depth,
                'controversy_score': thread_analysis.controversy_score
            }

        except Exception as e:
            self.logger.error(f"Thread analysis failed for {url}: {e}")
            return None

    def get_engagement_summary(self, post_id: str) -> Optional[Dict[str, Any]]:
        """Get engagement summary for a specific post."""
        if not self.engagement_tracker:
            return None
        return self.engagement_tracker.get_engagement_summary(post_id)

    def get_top_engaging_posts(self, limit: int = 10,
                              metric: str = 'engagement_score') -> List[Dict[str, Any]]:
        """Get top posts by engagement metric."""
        if not self.engagement_tracker:
            return []
        return self.engagement_tracker.get_top_engaging_posts(limit, metric)

    def track_subreddit_engagement(self, subreddit_name: str,
                                 time_filter: str = 'day',
                                 limit: int = 100) -> Optional[Dict[str, Any]]:
        """Track engagement metrics for an entire subreddit."""
        if not self.engagement_tracker:
            return None

        try:
            stats = self.engagement_tracker.track_subreddit_engagement(
                subreddit_name, time_filter, limit
            )
            return {
                'subreddit': stats.subreddit,
                'period': f"{stats.period_start.isoformat()} to {stats.period_end.isoformat()}",
                'total_posts': stats.total_posts,
                'avg_score': round(stats.avg_score, 2),
                'avg_comments': round(stats.avg_comments, 2),
                'avg_upvote_ratio': round(stats.avg_upvote_ratio, 3),
                'top_posts_by_score': stats.top_posts_by_score,
                'viral_posts': stats.viral_posts,
                'high_quality_posts': stats.high_quality_posts,
                'controversial_posts': stats.controversial_posts,
                'peak_activity_hours': stats.peak_activity_hours,
                'engagement_trends': stats.engagement_trends
            }
        except Exception as e:
            self.logger.error(f"Subreddit engagement tracking failed: {e}")
            return None

    def get_duplicate_clusters(self) -> List[List[str]]:
        """Get clusters of duplicate posts."""
        if not self.deduplicator:
            return []
        return self.deduplicator.get_duplicate_clusters()

    def get_deduplication_stats(self) -> Optional[Dict[str, Any]]:
        """Get deduplication statistics."""
        if not self.deduplicator:
            return None
        return self.deduplicator.get_statistics()

    def find_post_duplicates(self, post_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find duplicates for a specific post."""
        if not self.deduplicator:
            return []

        try:
            duplicates = self.deduplicator.find_duplicates(post_data)
            return [
                {
                    'original_post_id': dup.original_post_id,
                    'duplicate_post_id': dup.duplicate_post_id,
                    'match_type': dup.match_type,
                    'confidence': round(dup.confidence, 3),
                    'similarity_score': round(dup.similarity_score, 3),
                    'details': dup.details
                }
                for dup in duplicates
            ]
        except Exception as e:
            self.logger.error(f"Failed to find duplicates: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get scraping statistics."""
        return {
            **super().get_stats(),
            **self.stats,
            'subreddits_configured': len(self.config.subreddits),
            'include_comments': self.config.include_comments,
            'comment_analysis_available': COMMENT_ANALYSIS_AVAILABLE,
            'engagement_tracking_available': ENGAGEMENT_TRACKING_AVAILABLE,
            'deduplication_available': DEDUPLICATION_AVAILABLE
        }

    def shutdown(self):
        """Clean up resources."""
        super().shutdown()
        if self.reddit:
            # PRAW doesn't require explicit cleanup
            self.reddit = None
        self.logger.info("Reddit scraper shut down")


def create_reddit_scraper(
    subreddits: Optional[List[str]] = None,
    max_posts_per_subreddit: int = 100,
    include_comments: bool = True
) -> RedditScraper:
    """Factory function to create a Reddit scraper with common settings."""

    config = RedditScraperConfig(
        name="Reddit AI/ML Scraper",
        base_url="https://reddit.com",
        subreddits=subreddits or [
            'MachineLearning', 'artificial', 'ArtificialIntelligence',
            'deeplearning', 'MLQuestions', 'OpenAI', 'ChatGPT', 'LocalLLaMA'
        ],
        max_posts_per_subreddit=max_posts_per_subreddit,
        include_comments=include_comments
    )

    return RedditScraper(config)
