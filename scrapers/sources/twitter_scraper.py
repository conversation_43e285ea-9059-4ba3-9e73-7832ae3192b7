"""
Twitter/X scraper implementation using Tweepy (Twitter API v2).

This module provides comprehensive Twitter scraping capabilities including:
- Hashtag monitoring
- Real-time streaming
- Tweet and reply collection
- Engagement metrics tracking
- User timeline scraping
"""

import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Iterator, List, Optional, Dict, Any, Set
from dataclasses import dataclass, field
import asyncio
import threading

import tweepy
from tweepy.errors import TweepyException

from ..base.scraper import BaseScraper, ScraperConfig
from ..base.content_parser import ParsedArticle
from ..base.exceptions import ScrapingError, ConfigurationError
from config.settings import settings


@dataclass
class TwitterScraperConfig(ScraperConfig):
    """Configuration for Twitter scraper."""
    
    # Twitter-specific settings
    hashtags: List[str] = field(default_factory=lambda: [
        '#AI', '#MachineLearning', '#LLM', '#ArtificialIntelligence',
        '#DeepLearning', '#NLP', '#OpenAI', '#ChatGPT', '#GPT4',
        '#Transformer', '#NeuralNetwork', '#DataScience', '#MLOps'
    ])
    
    # User accounts to monitor
    user_accounts: List[str] = field(default_factory=lambda: [
        'OpenAI', 'AnthropicAI', 'GoogleAI', 'DeepMind', 'huggingface',
        'ylecun', 'karpathy', 'goodfellow_ian', 'jeffdean'
    ])
    
    # Search parameters
    max_tweets_per_hashtag: int = 100
    max_tweets_per_user: int = 50
    include_replies: bool = False
    include_retweets: bool = True
    min_likes_threshold: int = 5  # Minimum likes to consider
    max_age_days: int = 7  # Maximum age of tweets to scrape
    
    # Streaming settings
    enable_streaming: bool = False
    stream_keywords: List[str] = field(default_factory=lambda: [
        'artificial intelligence', 'machine learning', 'large language model',
        'neural network', 'deep learning', 'OpenAI', 'ChatGPT'
    ])
    
    # Rate limiting (Twitter API v2 limits)
    requests_per_minute: int = 300  # Twitter API v2 limit
    
    def __post_init__(self):
        """Validate Twitter configuration."""
        if not settings.twitter.bearer_token:
            raise ConfigurationError(
                "Twitter Bearer Token not configured. Please set TWITTER_BEARER_TOKEN"
            )


class TwitterEngagementAnalyzer:
    """Analyzes Twitter engagement metrics and patterns."""
    
    def __init__(self):
        self.logger = logging.getLogger("twitter.engagement")
    
    def analyze_tweet_engagement(self, tweet: tweepy.Tweet) -> Dict[str, Any]:
        """Analyze engagement metrics for a tweet."""
        metrics = tweet.public_metrics or {}
        
        # Basic metrics
        likes = metrics.get('like_count', 0)
        retweets = metrics.get('retweet_count', 0)
        replies = metrics.get('reply_count', 0)
        quotes = metrics.get('quote_count', 0)
        
        # Calculate engagement rate
        total_engagement = likes + retweets + replies + quotes
        
        # Engagement velocity (engagement per hour since posting)
        created_at = tweet.created_at
        if created_at:
            hours_since_post = (datetime.now(timezone.utc) - created_at).total_seconds() / 3600
            engagement_velocity = total_engagement / max(hours_since_post, 1)
        else:
            engagement_velocity = 0
        
        # Quality score based on engagement distribution
        if total_engagement > 0:
            like_ratio = likes / total_engagement
            retweet_ratio = retweets / total_engagement
            reply_ratio = replies / total_engagement
            
            # Higher quality if more balanced engagement
            quality_score = 1 - abs(like_ratio - 0.6) - abs(retweet_ratio - 0.25) - abs(reply_ratio - 0.15)
            quality_score = max(0, min(1, quality_score))
        else:
            quality_score = 0
        
        return {
            'likes': likes,
            'retweets': retweets,
            'replies': replies,
            'quotes': quotes,
            'total_engagement': total_engagement,
            'engagement_velocity': engagement_velocity,
            'quality_score': quality_score,
            'like_ratio': likes / max(total_engagement, 1),
            'retweet_ratio': retweets / max(total_engagement, 1),
            'reply_ratio': replies / max(total_engagement, 1)
        }


class TwitterScraper(BaseScraper):
    """Twitter scraper using Twitter API v2."""
    
    def __init__(self, config: TwitterScraperConfig):
        super().__init__(config)
        self.config: TwitterScraperConfig = config
        self.client = None
        self.engagement_analyzer = TwitterEngagementAnalyzer()
        self.stream = None
        self.streaming_thread = None
        self._setup_twitter_client()
        
        # Optional duplicate detection
        self.deduplicator = None
        try:
            from ..deduplication.twitter_deduplicator import TwitterDeduplicator
            self.deduplicator = TwitterDeduplicator()
            self.logger.info("Twitter duplicate detection enabled")
        except ImportError:
            self.logger.info("Twitter duplicate detection not available")
    
    def _setup_twitter_client(self):
        """Initialize Twitter API client."""
        try:
            self.client = tweepy.Client(
                bearer_token=settings.twitter.bearer_token,
                consumer_key=settings.twitter.api_key,
                consumer_secret=settings.twitter.api_secret,
                access_token=settings.twitter.access_token,
                access_token_secret=settings.twitter.access_token_secret,
                wait_on_rate_limit=True
            )
            
            # Test the connection
            try:
                self.client.get_me()
                self.logger.info("Twitter API client initialized successfully")
            except Exception:
                # If get_me fails, we might only have bearer token (read-only)
                self.logger.info("Twitter API client initialized in read-only mode")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Twitter client: {e}")
            raise ConfigurationError(f"Twitter API setup failed: {str(e)}")
    
    def get_article_urls(self) -> Iterator[str]:
        """Get tweet URLs from various sources."""
        # Search by hashtags
        for hashtag in self.config.hashtags:
            try:
                yield from self._search_hashtag(hashtag)
            except Exception as e:
                self.logger.error(f"Failed to search hashtag {hashtag}: {e}")
        
        # Monitor user timelines
        for username in self.config.user_accounts:
            try:
                yield from self._get_user_timeline(username)
            except Exception as e:
                self.logger.error(f"Failed to get timeline for {username}: {e}")
    
    def _search_hashtag(self, hashtag: str) -> Iterator[str]:
        """Search tweets by hashtag."""
        self.logger.info(f"Searching hashtag: {hashtag}")
        
        # Build search query
        query = f"{hashtag} -is:retweet" if not self.config.include_retweets else hashtag
        if self.config.min_likes_threshold > 0:
            query += f" min_faves:{self.config.min_likes_threshold}"
        
        try:
            tweets = tweepy.Paginator(
                self.client.search_recent_tweets,
                query=query,
                max_results=min(100, self.config.max_tweets_per_hashtag),
                tweet_fields=['created_at', 'author_id', 'public_metrics', 'context_annotations', 'entities']
            ).flatten(limit=self.config.max_tweets_per_hashtag)
            
            for tweet in tweets:
                # Check age filter
                if self._is_tweet_too_old(tweet):
                    continue
                
                yield f"https://twitter.com/i/web/status/{tweet.id}"
                
        except TweepyException as e:
            self.logger.error(f"Twitter API error searching {hashtag}: {e}")
    
    def _get_user_timeline(self, username: str) -> Iterator[str]:
        """Get tweets from user timeline."""
        self.logger.info(f"Getting timeline for user: {username}")
        
        try:
            # Get user ID
            user = self.client.get_user(username=username)
            if not user.data:
                self.logger.warning(f"User not found: {username}")
                return
            
            # Get user tweets
            tweets = tweepy.Paginator(
                self.client.get_users_tweets,
                id=user.data.id,
                max_results=min(100, self.config.max_tweets_per_user),
                exclude=['retweets'] if not self.config.include_retweets else None,
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'entities']
            ).flatten(limit=self.config.max_tweets_per_user)
            
            for tweet in tweets:
                # Check age filter
                if self._is_tweet_too_old(tweet):
                    continue
                
                yield f"https://twitter.com/i/web/status/{tweet.id}"
                
        except TweepyException as e:
            self.logger.error(f"Twitter API error getting timeline for {username}: {e}")
    
    def _is_tweet_too_old(self, tweet: tweepy.Tweet) -> bool:
        """Check if tweet is too old based on configuration."""
        if not tweet.created_at:
            return False

        age_limit = datetime.now(timezone.utc) - timedelta(days=self.config.max_age_days)
        return tweet.created_at < age_limit

    def scrape_article(self, url: str) -> Optional[ParsedArticle]:
        """Scrape a single tweet."""
        try:
            # Extract tweet ID from URL
            tweet_id = self._extract_tweet_id(url)
            if not tweet_id:
                self.logger.error(f"Could not extract tweet ID from URL: {url}")
                return None

            # Get tweet data
            tweet = self.client.get_tweet(
                id=tweet_id,
                tweet_fields=['created_at', 'author_id', 'public_metrics', 'context_annotations', 'entities', 'referenced_tweets'],
                user_fields=['username', 'name', 'verified'],
                expansions=['author_id']
            )

            if not tweet.data:
                self.logger.warning(f"Tweet not found: {tweet_id}")
                return None

            return self._parse_twitter_post(tweet.data, tweet.includes, url)

        except TweepyException as e:
            self.logger.error(f"Twitter API error scraping {url}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error scraping tweet {url}: {e}")
            return None

    def _extract_tweet_id(self, url: str) -> Optional[str]:
        """Extract tweet ID from Twitter URL."""
        import re

        # Match various Twitter URL formats
        patterns = [
            r'twitter\.com/\w+/status/(\d+)',
            r'x\.com/\w+/status/(\d+)',
            r'twitter\.com/i/web/status/(\d+)',
            r'x\.com/i/web/status/(\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None

    def _parse_twitter_post(self, tweet: tweepy.Tweet, includes: Dict, url: str) -> ParsedArticle:
        """Parse Twitter post into ParsedArticle format."""

        # Get author information
        author_info = self._get_author_info(tweet.author_id, includes)
        author_name = author_info.get('username', 'Unknown')

        # Extract content
        content = tweet.text or ""

        # Clean up content (remove URLs, mentions for better analysis)
        clean_content = self._clean_tweet_content(content)

        # Get engagement metrics
        engagement_metrics = self.engagement_analyzer.analyze_tweet_engagement(tweet)

        # Check for duplicates if deduplicator is available
        if self.deduplicator:
            try:
                # Prepare tweet data for duplicate detection
                tweet_data = {
                    'id': url,  # Use URL as unique ID
                    'tweet_id': tweet.id,
                    'title': content[:100] + "..." if len(content) > 100 else content,
                    'content': content,
                    'url': url,
                    'author': author_name,
                    'created_utc': tweet.created_at,
                    'engagement_metrics': engagement_metrics
                }

                # Find duplicates
                duplicates = self.deduplicator.find_duplicates(tweet_data)

                # Update statistics
                if duplicates:
                    self.stats['duplicates_detected'] = self.stats.get('duplicates_detected', 0) + 1
                    self.logger.debug(f"Found {len(duplicates)} duplicate(s) for tweet {tweet.id}")

                # Add duplicate information to metadata
                if duplicates:
                    engagement_metrics['duplicates_found'] = len(duplicates)
                    engagement_metrics['duplicate_details'] = [
                        {
                            'original_post_id': dup.original_post_id,
                            'match_type': dup.match_type,
                            'confidence': dup.confidence
                        } for dup in duplicates
                    ]

            except Exception as e:
                self.logger.warning(f"Duplicate detection failed for tweet {tweet.id}: {e}")

        # Create ParsedArticle
        article = ParsedArticle(
            title=content[:100] + "..." if len(content) > 100 else content,
            content=clean_content,
            url=url,
            author=author_name,
            published_at=tweet.created_at,
            source=self.config.name,
            metadata={
                'tweet_id': tweet.id,
                'author_id': tweet.author_id,
                'author_verified': author_info.get('verified', False),
                'author_display_name': author_info.get('name', ''),
                'engagement_metrics': engagement_metrics,
                'context_annotations': getattr(tweet, 'context_annotations', []),
                'entities': getattr(tweet, 'entities', {}),
                'referenced_tweets': getattr(tweet, 'referenced_tweets', []),
                'source_type': 'twitter'
            }
        )

        return article

    def _get_author_info(self, author_id: str, includes: Dict) -> Dict[str, Any]:
        """Extract author information from includes."""
        if not includes or 'users' not in includes:
            return {}

        for user in includes['users']:
            if user.id == author_id:
                return {
                    'username': user.username,
                    'name': user.name,
                    'verified': getattr(user, 'verified', False)
                }

        return {}

    def _clean_tweet_content(self, content: str) -> str:
        """Clean tweet content for better analysis."""
        import re

        # Remove URLs
        content = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', content)

        # Remove mentions (but keep the context)
        content = re.sub(r'@\w+', '', content)

        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content).strip()

        return content

    def get_stats(self) -> Dict[str, Any]:
        """Get scraping statistics including duplicate detection stats."""
        base_stats = super().get_stats()

        # Add duplicate detection stats if available
        if self.deduplicator:
            try:
                dedup_stats = self.deduplicator.get_deduplication_stats()
                base_stats.update({
                    'duplicates_detected': self.stats.get('duplicates_detected', 0),
                    'unique_tweets': dedup_stats.get('unique_posts', 0),
                    'duplicate_clusters': dedup_stats.get('duplicate_clusters', 0),
                    'deduplication_ratio': dedup_stats.get('deduplication_ratio', 0.0)
                })
            except Exception as e:
                self.logger.warning(f"Failed to get deduplication stats: {e}")

        return base_stats

    def get_duplicate_clusters(self) -> List[List[str]]:
        """Get duplicate clusters if deduplicator is available."""
        if self.deduplicator:
            try:
                return self.deduplicator.get_duplicate_clusters()
            except Exception as e:
                self.logger.warning(f"Failed to get duplicate clusters: {e}")
        return []

    def find_tweet_duplicates(self, tweet_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find duplicates for a specific tweet."""
        if self.deduplicator:
            try:
                return self.deduplicator.find_duplicates(tweet_data)
            except Exception as e:
                self.logger.warning(f"Failed to find duplicates: {e}")
        return []


def create_twitter_scraper(
    hashtags: Optional[List[str]] = None,
    user_accounts: Optional[List[str]] = None,
    max_tweets_per_hashtag: int = 100,
    enable_streaming: bool = False
) -> TwitterScraper:
    """Factory function to create a Twitter scraper with common settings."""

    config = TwitterScraperConfig(
        name="Twitter AI/ML Scraper",
        base_url="https://twitter.com",
        hashtags=hashtags or [
            '#AI', '#MachineLearning', '#LLM', '#ArtificialIntelligence',
            '#DeepLearning', '#NLP', '#OpenAI', '#ChatGPT', '#GPT4'
        ],
        user_accounts=user_accounts or [
            'OpenAI', 'AnthropicAI', 'GoogleAI', 'DeepMind', 'huggingface'
        ],
        max_tweets_per_hashtag=max_tweets_per_hashtag,
        enable_streaming=enable_streaming
    )

    return TwitterScraper(config)
