"""
Twitter real-time streaming system for AI/ML content.

This module provides real-time Twitter streaming capabilities including:
- Hashtag-based streaming
- Keyword filtering
- Real-time content processing
- Stream management and recovery
"""

import logging
import time
import threading
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
import asyncio
from queue import Queue, Empty

import tweepy
from tweepy.errors import TweepyException

from ..sources.twitter_scraper import TwitterScraper, TwitterScraperConfig
from ..base.content_parser import ParsedArticle
from config.settings import settings


@dataclass
class TwitterStreamConfig:
    """Configuration for Twitter streaming."""
    
    # Stream filters
    track_keywords: List[str] = field(default_factory=lambda: [
        'artificial intelligence', 'machine learning', 'deep learning',
        'neural network', 'large language model', 'LLM', 'GPT',
        'OpenAI', 'ChatGPT', 'transformer', 'BERT', 'NLP'
    ])
    
    track_hashtags: List[str] = field(default_factory=lambda: [
        '#AI', '#MachineLearning', '#DeepLearning', '#NLP',
        '#LLM', '#OpenAI', '#ChatGPT', '#ArtificialIntelligence'
    ])
    
    follow_users: List[str] = field(default_factory=lambda: [
        'OpenAI', 'AnthropicAI', 'GoogleAI', 'DeepMind', 'huggingface'
    ])
    
    # Stream settings
    languages: List[str] = field(default_factory=lambda: ['en'])
    tweet_fields: List[str] = field(default_factory=lambda: [
        'created_at', 'author_id', 'public_metrics', 'context_annotations', 'entities'
    ])
    user_fields: List[str] = field(default_factory=lambda: [
        'username', 'name', 'verified', 'public_metrics'
    ])
    expansions: List[str] = field(default_factory=lambda: ['author_id'])
    
    # Processing settings
    enable_real_time_processing: bool = True
    max_queue_size: int = 1000
    processing_batch_size: int = 10
    processing_interval_seconds: int = 5
    
    # Stream management
    auto_restart: bool = True
    max_restart_attempts: int = 5
    restart_delay_seconds: int = 30


class TwitterStreamListener(tweepy.StreamingClient):
    """Custom Twitter stream listener for AI/ML content."""
    
    def __init__(self, bearer_token: str, config: TwitterStreamConfig, 
                 tweet_queue: Queue, logger: logging.Logger):
        super().__init__(bearer_token, wait_on_rate_limit=True)
        self.config = config
        self.tweet_queue = tweet_queue
        self.logger = logger
        self.tweets_received = 0
        self.start_time = time.time()
        
    def on_tweet(self, tweet):
        """Handle incoming tweets."""
        try:
            self.tweets_received += 1
            
            # Add tweet to processing queue
            if self.tweet_queue.qsize() < self.config.max_queue_size:
                self.tweet_queue.put({
                    'tweet': tweet,
                    'received_at': datetime.now(timezone.utc),
                    'includes': getattr(tweet, 'includes', {})
                })
            else:
                self.logger.warning("Tweet queue is full, dropping tweet")
            
            if self.tweets_received % 100 == 0:
                elapsed = time.time() - self.start_time
                rate = self.tweets_received / elapsed if elapsed > 0 else 0
                self.logger.info(f"Received {self.tweets_received} tweets, rate: {rate:.2f}/sec")
                
        except Exception as e:
            self.logger.error(f"Error processing tweet: {e}")
    
    def on_error(self, status_code):
        """Handle stream errors."""
        self.logger.error(f"Twitter stream error: {status_code}")
        if status_code == 420:  # Rate limit
            self.logger.warning("Rate limited, backing off")
            return False  # Disconnect
        return True  # Continue
    
    def on_disconnect(self):
        """Handle stream disconnection."""
        self.logger.warning("Twitter stream disconnected")


class TwitterStreamManager:
    """Manages Twitter streaming and real-time processing."""
    
    def __init__(self, config: TwitterStreamConfig):
        self.config = config
        self.logger = logging.getLogger("twitter.stream")
        
        # Stream components
        self.stream_client = None
        self.tweet_queue = Queue(maxsize=config.max_queue_size)
        self.processing_thread = None
        self.stream_thread = None
        
        # State management
        self.is_streaming = False
        self.is_processing = False
        self.restart_attempts = 0
        
        # Statistics
        self.stats = {
            'tweets_received': 0,
            'tweets_processed': 0,
            'processing_errors': 0,
            'stream_restarts': 0,
            'start_time': None
        }
        
        # Initialize Twitter scraper for processing
        scraper_config = TwitterScraperConfig(
            name="Twitter Stream Processor",
            base_url="https://twitter.com"
        )
        self.scraper = TwitterScraper(scraper_config)
        
        # Callback for processed tweets
        self.tweet_callback: Optional[Callable[[ParsedArticle], None]] = None
    
    def set_tweet_callback(self, callback: Callable[[ParsedArticle], None]):
        """Set callback function for processed tweets."""
        self.tweet_callback = callback
    
    def start_streaming(self):
        """Start the Twitter stream."""
        if self.is_streaming:
            self.logger.warning("Stream is already running")
            return
        
        try:
            self.logger.info("Starting Twitter stream...")
            
            # Initialize stream client
            self.stream_client = TwitterStreamListener(
                bearer_token=settings.twitter.bearer_token,
                config=self.config,
                tweet_queue=self.tweet_queue,
                logger=self.logger
            )
            
            # Clear existing rules
            existing_rules = self.stream_client.get_rules()
            if existing_rules.data:
                rule_ids = [rule.id for rule in existing_rules.data]
                self.stream_client.delete_rules(rule_ids)
                self.logger.info(f"Deleted {len(rule_ids)} existing rules")
            
            # Add new rules
            rules = self._build_stream_rules()
            if rules:
                self.stream_client.add_rules(rules)
                self.logger.info(f"Added {len(rules)} stream rules")
            
            # Start processing thread
            self.is_processing = True
            self.processing_thread = threading.Thread(target=self._process_tweets, daemon=True)
            self.processing_thread.start()
            
            # Start streaming
            self.is_streaming = True
            self.stats['start_time'] = time.time()
            
            # Start stream in separate thread
            self.stream_thread = threading.Thread(target=self._run_stream, daemon=True)
            self.stream_thread.start()
            
            self.logger.info("Twitter stream started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start Twitter stream: {e}")
            self.stop_streaming()
            raise
    
    def _build_stream_rules(self) -> List[tweepy.StreamRule]:
        """Build stream rules from configuration."""
        rules = []
        
        # Combine keywords and hashtags
        all_terms = self.config.track_keywords + self.config.track_hashtags
        
        # Twitter has a limit on rule length, so we need to be smart about combining terms
        # Group terms into rules of reasonable length
        max_rule_length = 500  # Conservative limit
        current_rule_terms = []
        current_length = 0
        
        for term in all_terms:
            # Add quotes around multi-word terms
            if ' ' in term and not term.startswith('#'):
                formatted_term = f'"{term}"'
            else:
                formatted_term = term
            
            term_length = len(formatted_term) + 4  # +4 for " OR "
            
            if current_length + term_length > max_rule_length and current_rule_terms:
                # Create rule from current terms
                rule_value = " OR ".join(current_rule_terms)
                rules.append(tweepy.StreamRule(rule_value))
                current_rule_terms = [formatted_term]
                current_length = len(formatted_term)
            else:
                current_rule_terms.append(formatted_term)
                current_length += term_length
        
        # Add remaining terms as final rule
        if current_rule_terms:
            rule_value = " OR ".join(current_rule_terms)
            rules.append(tweepy.StreamRule(rule_value))
        
        # Add language filter if specified
        if self.config.languages:
            for rule in rules:
                lang_filter = " OR ".join([f"lang:{lang}" for lang in self.config.languages])
                rule.value += f" ({lang_filter})"
        
        return rules
    
    def _run_stream(self):
        """Run the Twitter stream."""
        try:
            self.stream_client.filter(
                tweet_fields=self.config.tweet_fields,
                user_fields=self.config.user_fields,
                expansions=self.config.expansions,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Stream error: {e}")
            if self.config.auto_restart and self.restart_attempts < self.config.max_restart_attempts:
                self._restart_stream()
    
    def _restart_stream(self):
        """Restart the stream after an error."""
        self.restart_attempts += 1
        self.stats['stream_restarts'] += 1
        
        self.logger.info(f"Restarting stream (attempt {self.restart_attempts}/{self.config.max_restart_attempts})")
        
        # Stop current stream
        if self.stream_client:
            try:
                self.stream_client.disconnect()
            except:
                pass
        
        # Wait before restarting
        time.sleep(self.config.restart_delay_seconds)
        
        # Restart
        try:
            self.start_streaming()
            self.restart_attempts = 0  # Reset on successful restart
        except Exception as e:
            self.logger.error(f"Failed to restart stream: {e}")
            if self.restart_attempts >= self.config.max_restart_attempts:
                self.logger.error("Max restart attempts reached, giving up")
                self.stop_streaming()
    
    def _process_tweets(self):
        """Process tweets from the queue."""
        self.logger.info("Started tweet processing thread")
        
        while self.is_processing:
            try:
                # Process tweets in batches
                tweets_to_process = []
                
                # Collect batch
                for _ in range(self.config.processing_batch_size):
                    try:
                        tweet_data = self.tweet_queue.get(timeout=1)
                        tweets_to_process.append(tweet_data)
                    except Empty:
                        break
                
                # Process collected tweets
                for tweet_data in tweets_to_process:
                    try:
                        self._process_single_tweet(tweet_data)
                        self.stats['tweets_processed'] += 1
                    except Exception as e:
                        self.logger.error(f"Error processing tweet: {e}")
                        self.stats['processing_errors'] += 1
                
                # Sleep if no tweets to process
                if not tweets_to_process:
                    time.sleep(self.config.processing_interval_seconds)
                    
            except Exception as e:
                self.logger.error(f"Error in processing loop: {e}")
                time.sleep(self.config.processing_interval_seconds)
    
    def _process_single_tweet(self, tweet_data: Dict[str, Any]):
        """Process a single tweet."""
        tweet = tweet_data['tweet']
        includes = tweet_data.get('includes', {})
        
        # Create URL for the tweet
        url = f"https://twitter.com/i/web/status/{tweet.id}"
        
        # Parse tweet using scraper
        parsed_article = self.scraper._parse_twitter_post(tweet, includes, url)
        
        if parsed_article and self.tweet_callback:
            self.tweet_callback(parsed_article)
    
    def stop_streaming(self):
        """Stop the Twitter stream."""
        self.logger.info("Stopping Twitter stream...")
        
        # Stop streaming
        self.is_streaming = False
        if self.stream_client:
            try:
                self.stream_client.disconnect()
            except:
                pass
        
        # Stop processing
        self.is_processing = False
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5)
        
        self.logger.info("Twitter stream stopped")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get streaming statistics."""
        current_time = time.time()
        elapsed = current_time - self.stats['start_time'] if self.stats['start_time'] else 0
        
        return {
            **self.stats,
            'elapsed_time': elapsed,
            'tweets_per_second': self.stats['tweets_received'] / max(elapsed, 1),
            'processing_rate': self.stats['tweets_processed'] / max(elapsed, 1),
            'queue_size': self.tweet_queue.qsize(),
            'is_streaming': self.is_streaming,
            'is_processing': self.is_processing,
            'restart_attempts': self.restart_attempts
        }


def create_twitter_stream(
    track_keywords: Optional[List[str]] = None,
    track_hashtags: Optional[List[str]] = None,
    enable_processing: bool = True
) -> TwitterStreamManager:
    """Factory function to create a Twitter stream manager."""
    
    config = TwitterStreamConfig(
        track_keywords=track_keywords or [
            'artificial intelligence', 'machine learning', 'deep learning',
            'neural network', 'large language model', 'LLM'
        ],
        track_hashtags=track_hashtags or [
            '#AI', '#MachineLearning', '#DeepLearning', '#NLP', '#LLM'
        ],
        enable_real_time_processing=enable_processing
    )
    
    return TwitterStreamManager(config)
