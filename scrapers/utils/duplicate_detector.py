"""
Duplicate detection utilities for avoiding duplicate content.
"""

import hashlib
import re
from typing import Set, Dict, Optional, List
from dataclasses import dataclass
from difflib import SequenceMatcher
import logging

from ..base.content_parser import ParsedArticle


@dataclass
class DuplicateMatch:
    """Information about a duplicate match."""
    original_url: str
    duplicate_url: str
    similarity_score: float
    match_type: str  # 'url', 'content_hash', 'title', 'content_similarity'


class DuplicateDetector:
    """Detects duplicate articles using multiple strategies."""
    
    def __init__(self, similarity_threshold: float = 0.85):
        self.similarity_threshold = similarity_threshold
        self.logger = logging.getLogger("duplicate_detector")
        
        # In-memory caches for fast duplicate detection
        self.url_cache: Set[str] = set()
        self.content_hashes: Set[str] = set()
        self.title_hashes: Set[str] = set()
        self.articles_cache: List[ParsedArticle] = []
        
        # Configuration
        self.max_cache_size = 10000  # Maximum articles to keep in memory
        self.enable_content_similarity = True
        self.enable_title_similarity = True
    
    def normalize_url(self, url: str) -> str:
        """Normalize URL for comparison."""
        # Remove common tracking parameters
        tracking_params = [
            'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
            'fbclid', 'gclid', 'ref', 'source', 'campaign'
        ]
        
        # Remove fragment
        if '#' in url:
            url = url.split('#')[0]
        
        # Remove tracking parameters
        if '?' in url:
            base_url, params = url.split('?', 1)
            param_pairs = params.split('&')
            filtered_params = []
            
            for param in param_pairs:
                if '=' in param:
                    key = param.split('=')[0]
                    if key not in tracking_params:
                        filtered_params.append(param)
            
            if filtered_params:
                url = f"{base_url}?{'&'.join(filtered_params)}"
            else:
                url = base_url
        
        # Normalize trailing slash
        if url.endswith('/') and url.count('/') > 2:
            url = url.rstrip('/')
        
        return url.lower()
    
    def generate_content_hash(self, content: str) -> str:
        """Generate a hash of normalized content."""
        # Normalize content for hashing
        normalized = re.sub(r'\s+', ' ', content.lower().strip())
        # Remove common words that don't affect meaning
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words = [word for word in normalized.split() if word not in stop_words]
        normalized = ' '.join(words)
        
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def generate_title_hash(self, title: str) -> str:
        """Generate a hash of normalized title."""
        # Remove common title prefixes/suffixes
        title = re.sub(r'^(breaking|news|update|exclusive):\s*', '', title.lower())
        title = re.sub(r'\s*-\s*[^-]+$', '', title)  # Remove site name suffix
        
        # Normalize whitespace
        title = re.sub(r'\s+', ' ', title.strip())
        
        return hashlib.md5(title.encode()).hexdigest()
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings."""
        # Normalize texts
        text1 = re.sub(r'\s+', ' ', text1.lower().strip())
        text2 = re.sub(r'\s+', ' ', text2.lower().strip())
        
        # Use SequenceMatcher for similarity
        return SequenceMatcher(None, text1, text2).ratio()
    
    def is_duplicate_url(self, url: str) -> bool:
        """Check if URL is a duplicate."""
        normalized_url = self.normalize_url(url)
        return normalized_url in self.url_cache
    
    def is_duplicate_content(self, article: ParsedArticle) -> Optional[DuplicateMatch]:
        """Check if article content is a duplicate."""
        # Check content hash
        content_hash = self.generate_content_hash(article.content)
        if content_hash in self.content_hashes:
            return DuplicateMatch(
                original_url="unknown",  # Would need database lookup
                duplicate_url=article.url,
                similarity_score=1.0,
                match_type="content_hash"
            )
        
        # Check title hash
        if article.title:
            title_hash = self.generate_title_hash(article.title)
            if title_hash in self.title_hashes:
                return DuplicateMatch(
                    original_url="unknown",
                    duplicate_url=article.url,
                    similarity_score=1.0,
                    match_type="title"
                )
        
        # Check content similarity if enabled
        if self.enable_content_similarity:
            for cached_article in self.articles_cache:
                similarity = self.calculate_text_similarity(article.content, cached_article.content)
                if similarity >= self.similarity_threshold:
                    return DuplicateMatch(
                        original_url=cached_article.url,
                        duplicate_url=article.url,
                        similarity_score=similarity,
                        match_type="content_similarity"
                    )
        
        # Check title similarity if enabled
        if self.enable_title_similarity and article.title:
            for cached_article in self.articles_cache:
                if cached_article.title:
                    similarity = self.calculate_text_similarity(article.title, cached_article.title)
                    if similarity >= self.similarity_threshold:
                        return DuplicateMatch(
                            original_url=cached_article.url,
                            duplicate_url=article.url,
                            similarity_score=similarity,
                            match_type="title_similarity"
                        )
        
        return None
    
    def add_article(self, article: ParsedArticle) -> bool:
        """Add article to duplicate detection cache."""
        try:
            # Check for duplicates first
            if self.is_duplicate_url(article.url):
                return False
            
            duplicate_match = self.is_duplicate_content(article)
            if duplicate_match:
                self.logger.debug(f"Duplicate detected: {duplicate_match.match_type} - {article.url}")
                return False
            
            # Add to caches
            normalized_url = self.normalize_url(article.url)
            self.url_cache.add(normalized_url)
            
            content_hash = self.generate_content_hash(article.content)
            self.content_hashes.add(content_hash)
            
            if article.title:
                title_hash = self.generate_title_hash(article.title)
                self.title_hashes.add(title_hash)
            
            # Add to articles cache
            self.articles_cache.append(article)
            
            # Maintain cache size
            if len(self.articles_cache) > self.max_cache_size:
                # Remove oldest articles
                removed_count = len(self.articles_cache) - self.max_cache_size
                removed_articles = self.articles_cache[:removed_count]
                self.articles_cache = self.articles_cache[removed_count:]
                
                # Remove from other caches (this is approximate)
                for removed_article in removed_articles:
                    try:
                        removed_url = self.normalize_url(removed_article.url)
                        self.url_cache.discard(removed_url)
                        
                        removed_content_hash = self.generate_content_hash(removed_article.content)
                        self.content_hashes.discard(removed_content_hash)
                        
                        if removed_article.title:
                            removed_title_hash = self.generate_title_hash(removed_article.title)
                            self.title_hashes.discard(removed_title_hash)
                    except:
                        pass  # Ignore errors during cleanup
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding article to duplicate detector: {e}")
            return True  # Allow article through on error
    
    def is_duplicate(self, article: ParsedArticle) -> bool:
        """Check if article is a duplicate (main interface)."""
        if self.is_duplicate_url(article.url):
            return True
        
        duplicate_match = self.is_duplicate_content(article)
        return duplicate_match is not None
    
    def get_stats(self) -> Dict:
        """Get duplicate detection statistics."""
        return {
            'cached_urls': len(self.url_cache),
            'cached_content_hashes': len(self.content_hashes),
            'cached_title_hashes': len(self.title_hashes),
            'cached_articles': len(self.articles_cache),
            'similarity_threshold': self.similarity_threshold,
            'max_cache_size': self.max_cache_size,
            'content_similarity_enabled': self.enable_content_similarity,
            'title_similarity_enabled': self.enable_title_similarity
        }
    
    def clear_cache(self):
        """Clear all caches."""
        self.url_cache.clear()
        self.content_hashes.clear()
        self.title_hashes.clear()
        self.articles_cache.clear()
        self.logger.info("Cleared duplicate detection caches")
    
    def set_similarity_threshold(self, threshold: float):
        """Set the similarity threshold for duplicate detection."""
        if 0.0 <= threshold <= 1.0:
            self.similarity_threshold = threshold
            self.logger.info(f"Set similarity threshold to {threshold}")
        else:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
    
    def configure(self, enable_content_similarity: bool = None, 
                  enable_title_similarity: bool = None,
                  max_cache_size: int = None):
        """Configure duplicate detection settings."""
        if enable_content_similarity is not None:
            self.enable_content_similarity = enable_content_similarity
        
        if enable_title_similarity is not None:
            self.enable_title_similarity = enable_title_similarity
        
        if max_cache_size is not None:
            self.max_cache_size = max_cache_size
        
        self.logger.info(f"Updated configuration: content_sim={self.enable_content_similarity}, "
                        f"title_sim={self.enable_title_similarity}, cache_size={self.max_cache_size}")
