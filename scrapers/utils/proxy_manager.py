"""
Proxy rotation and management for distributed scraping.
"""

import time
import random
import requests
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from threading import Lock
import logging

from ..base.exceptions import ProxyError


@dataclass
class ProxyInfo:
    """Information about a proxy server."""
    url: str
    protocol: str  # http, https, socks4, socks5
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    is_working: bool = True
    last_used: float = 0
    response_time: float = 0
    failure_count: int = 0
    success_count: int = 0
    
    @property
    def proxy_dict(self) -> Dict[str, str]:
        """Get proxy dictionary for requests."""
        if self.username and self.password:
            auth_url = f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            auth_url = f"{self.protocol}://{self.host}:{self.port}"
        
        return {
            'http': auth_url,
            'https': auth_url
        }
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of the proxy."""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0


class ProxyManager:
    """Manages proxy rotation and health checking."""
    
    def __init__(self, test_url: str = "http://httpbin.org/ip", test_timeout: int = 10):
        self.proxies: List[ProxyInfo] = []
        self.test_url = test_url
        self.test_timeout = test_timeout
        self.logger = logging.getLogger("proxy_manager")
        self._lock = Lock()
        
        # Configuration
        self.max_failures = 5  # Max failures before marking proxy as bad
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = 0
    
    def add_proxy(self, proxy_url: str, protocol: str = "http", 
                  username: str = None, password: str = None) -> bool:
        """Add a proxy to the rotation pool."""
        try:
            # Parse proxy URL
            if "://" in proxy_url:
                parts = proxy_url.split("://", 1)
                protocol = parts[0]
                host_port = parts[1]
            else:
                host_port = proxy_url
            
            if "@" in host_port:
                auth, host_port = host_port.split("@", 1)
                if ":" in auth:
                    username, password = auth.split(":", 1)
            
            if ":" in host_port:
                host, port = host_port.rsplit(":", 1)
                port = int(port)
            else:
                host = host_port
                port = 8080 if protocol == "http" else 1080
            
            proxy_info = ProxyInfo(
                url=proxy_url,
                protocol=protocol,
                host=host,
                port=port,
                username=username,
                password=password
            )
            
            # Test the proxy before adding
            if self._test_proxy(proxy_info):
                with self._lock:
                    self.proxies.append(proxy_info)
                self.logger.info(f"Added working proxy: {host}:{port}")
                return True
            else:
                self.logger.warning(f"Proxy test failed: {host}:{port}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to add proxy {proxy_url}: {e}")
            return False
    
    def add_proxy_list(self, proxy_urls: List[str]) -> int:
        """Add multiple proxies from a list."""
        added_count = 0
        for proxy_url in proxy_urls:
            if self.add_proxy(proxy_url):
                added_count += 1
        
        self.logger.info(f"Added {added_count}/{len(proxy_urls)} proxies")
        return added_count
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """Get a working proxy for use."""
        with self._lock:
            # Run health check if needed
            if time.time() - self.last_health_check > self.health_check_interval:
                self._health_check()
            
            # Filter working proxies
            working_proxies = [p for p in self.proxies if p.is_working]
            
            if not working_proxies:
                self.logger.warning("No working proxies available")
                return None
            
            # Sort by last used time and success rate
            working_proxies.sort(key=lambda p: (p.last_used, -p.success_rate))
            
            # Select the least recently used proxy with good success rate
            selected_proxy = working_proxies[0]
            selected_proxy.last_used = time.time()
            
            return selected_proxy.proxy_dict
    
    def mark_proxy_success(self, proxy_dict: Dict[str, str]):
        """Mark a proxy as successful."""
        proxy_url = proxy_dict.get('http', proxy_dict.get('https', ''))
        with self._lock:
            for proxy in self.proxies:
                if proxy_url in proxy.proxy_dict.values():
                    proxy.success_count += 1
                    proxy.failure_count = max(0, proxy.failure_count - 1)  # Reduce failure count
                    break
    
    def mark_proxy_failure(self, proxy_dict: Dict[str, str]):
        """Mark a proxy as failed."""
        proxy_url = proxy_dict.get('http', proxy_dict.get('https', ''))
        with self._lock:
            for proxy in self.proxies:
                if proxy_url in proxy.proxy_dict.values():
                    proxy.failure_count += 1
                    if proxy.failure_count >= self.max_failures:
                        proxy.is_working = False
                        self.logger.warning(f"Marking proxy as failed: {proxy.host}:{proxy.port}")
                    break
    
    def _test_proxy(self, proxy_info: ProxyInfo) -> bool:
        """Test if a proxy is working."""
        try:
            start_time = time.time()
            response = requests.get(
                self.test_url,
                proxies=proxy_info.proxy_dict,
                timeout=self.test_timeout,
                verify=False
            )
            
            proxy_info.response_time = time.time() - start_time
            
            if response.status_code == 200:
                proxy_info.success_count += 1
                return True
            else:
                proxy_info.failure_count += 1
                return False
                
        except Exception as e:
            proxy_info.failure_count += 1
            self.logger.debug(f"Proxy test failed for {proxy_info.host}:{proxy_info.port}: {e}")
            return False
    
    def _health_check(self):
        """Perform health check on all proxies."""
        self.logger.info("Performing proxy health check...")
        
        for proxy in self.proxies:
            if self._test_proxy(proxy):
                proxy.is_working = True
            else:
                if proxy.failure_count >= self.max_failures:
                    proxy.is_working = False
        
        self.last_health_check = time.time()
        
        working_count = sum(1 for p in self.proxies if p.is_working)
        self.logger.info(f"Health check complete: {working_count}/{len(self.proxies)} proxies working")
    
    def get_stats(self) -> Dict:
        """Get proxy statistics."""
        with self._lock:
            total_proxies = len(self.proxies)
            working_proxies = sum(1 for p in self.proxies if p.is_working)
            
            if self.proxies:
                avg_response_time = sum(p.response_time for p in self.proxies) / total_proxies
                avg_success_rate = sum(p.success_rate for p in self.proxies) / total_proxies
            else:
                avg_response_time = 0
                avg_success_rate = 0
            
            return {
                'total_proxies': total_proxies,
                'working_proxies': working_proxies,
                'failed_proxies': total_proxies - working_proxies,
                'avg_response_time': avg_response_time,
                'avg_success_rate': avg_success_rate,
                'last_health_check': self.last_health_check
            }
    
    def remove_failed_proxies(self):
        """Remove all failed proxies from the pool."""
        with self._lock:
            before_count = len(self.proxies)
            self.proxies = [p for p in self.proxies if p.is_working]
            removed_count = before_count - len(self.proxies)
            
            if removed_count > 0:
                self.logger.info(f"Removed {removed_count} failed proxies")
    
    def clear_all(self):
        """Clear all proxies."""
        with self._lock:
            self.proxies.clear()
            self.logger.info("Cleared all proxies")
