"""
User agent rotation and management for avoiding detection.
"""

import random
import time
from typing import List, Dict, Optional
from dataclasses import dataclass
import logging


@dataclass
class UserAgentInfo:
    """Information about a user agent."""
    agent_string: str
    browser: str
    os: str
    device_type: str  # desktop, mobile, tablet
    usage_count: int = 0
    last_used: float = 0
    
    def __post_init__(self):
        if self.last_used == 0:
            self.last_used = time.time()


class UserAgentManager:
    """Manages user agent rotation for web scraping."""
    
    def __init__(self):
        self.user_agents: List[UserAgentInfo] = []
        self.logger = logging.getLogger("user_agent_manager")
        self._load_default_user_agents()
    
    def _load_default_user_agents(self):
        """Load a comprehensive list of realistic user agents."""
        default_agents = [
            # Chrome on Windows
            UserAgentInfo(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Chrome", "Windows", "desktop"
            ),
            UserAgentInfo(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Chrome", "Windows", "desktop"
            ),
            
            # Chrome on macOS
            UserAgentInfo(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Chrome", "macOS", "desktop"
            ),
            UserAgentInfo(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Chrome", "macOS", "desktop"
            ),
            
            # Chrome on Linux
            UserAgentInfo(
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Chrome", "Linux", "desktop"
            ),
            
            # Firefox on Windows
            UserAgentInfo(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Firefox", "Windows", "desktop"
            ),
            UserAgentInfo(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                "Firefox", "Windows", "desktop"
            ),
            
            # Firefox on macOS
            UserAgentInfo(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Firefox", "macOS", "desktop"
            ),
            
            # Safari on macOS
            UserAgentInfo(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
                "Safari", "macOS", "desktop"
            ),
            UserAgentInfo(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
                "Safari", "macOS", "desktop"
            ),
            
            # Edge on Windows
            UserAgentInfo(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                "Edge", "Windows", "desktop"
            ),
            
            # Mobile Chrome on Android
            UserAgentInfo(
                "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
                "Chrome", "Android", "mobile"
            ),
            UserAgentInfo(
                "Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
                "Chrome", "Android", "mobile"
            ),
            
            # Safari on iOS
            UserAgentInfo(
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
                "Safari", "iOS", "mobile"
            ),
            UserAgentInfo(
                "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
                "Safari", "iOS", "tablet"
            ),
            
            # Firefox on Android
            UserAgentInfo(
                "Mozilla/5.0 (Mobile; rv:121.0) Gecko/121.0 Firefox/121.0",
                "Firefox", "Android", "mobile"
            ),
        ]
        
        self.user_agents.extend(default_agents)
        self.logger.info(f"Loaded {len(default_agents)} default user agents")
    
    def add_user_agent(self, agent_string: str, browser: str, os: str, device_type: str = "desktop"):
        """Add a custom user agent to the pool."""
        user_agent = UserAgentInfo(agent_string, browser, os, device_type)
        self.user_agents.append(user_agent)
        self.logger.debug(f"Added user agent: {browser} on {os}")
    
    def get_random_user_agent(self, device_type: str = None, browser: str = None, os: str = None) -> str:
        """Get a random user agent with optional filtering."""
        # Filter user agents based on criteria
        candidates = self.user_agents
        
        if device_type:
            candidates = [ua for ua in candidates if ua.device_type == device_type]
        
        if browser:
            candidates = [ua for ua in candidates if ua.browser.lower() == browser.lower()]
        
        if os:
            candidates = [ua for ua in candidates if ua.os.lower() == os.lower()]
        
        if not candidates:
            # Fallback to all user agents if no matches
            candidates = self.user_agents
        
        # Prefer less recently used agents
        candidates.sort(key=lambda ua: (ua.last_used, ua.usage_count))
        
        # Select from top 50% least used
        selection_pool = candidates[:max(1, len(candidates) // 2)]
        selected = random.choice(selection_pool)
        
        # Update usage statistics
        selected.usage_count += 1
        selected.last_used = time.time()
        
        return selected.agent_string
    
    def get_desktop_user_agent(self) -> str:
        """Get a random desktop user agent."""
        return self.get_random_user_agent(device_type="desktop")
    
    def get_mobile_user_agent(self) -> str:
        """Get a random mobile user agent."""
        return self.get_random_user_agent(device_type="mobile")
    
    def get_chrome_user_agent(self) -> str:
        """Get a random Chrome user agent."""
        return self.get_random_user_agent(browser="Chrome")
    
    def get_firefox_user_agent(self) -> str:
        """Get a random Firefox user agent."""
        return self.get_random_user_agent(browser="Firefox")
    
    def get_safari_user_agent(self) -> str:
        """Get a random Safari user agent."""
        return self.get_random_user_agent(browser="Safari")
    
    def get_user_agent_by_popularity(self) -> str:
        """Get a user agent weighted by browser popularity."""
        # Browser market share weights (approximate)
        browser_weights = {
            "Chrome": 0.65,
            "Safari": 0.20,
            "Edge": 0.08,
            "Firefox": 0.05,
            "Other": 0.02
        }
        
        # Select browser based on weights
        browsers = list(browser_weights.keys())
        weights = list(browser_weights.values())
        selected_browser = random.choices(browsers, weights=weights)[0]
        
        if selected_browser == "Other":
            return self.get_random_user_agent()
        else:
            return self.get_random_user_agent(browser=selected_browser)
    
    def get_stats(self) -> Dict:
        """Get user agent usage statistics."""
        if not self.user_agents:
            return {}
        
        total_usage = sum(ua.usage_count for ua in self.user_agents)
        
        browser_stats = {}
        os_stats = {}
        device_stats = {}
        
        for ua in self.user_agents:
            # Browser stats
            browser_stats[ua.browser] = browser_stats.get(ua.browser, 0) + ua.usage_count
            
            # OS stats
            os_stats[ua.os] = os_stats.get(ua.os, 0) + ua.usage_count
            
            # Device stats
            device_stats[ua.device_type] = device_stats.get(ua.device_type, 0) + ua.usage_count
        
        return {
            'total_user_agents': len(self.user_agents),
            'total_usage': total_usage,
            'browser_distribution': browser_stats,
            'os_distribution': os_stats,
            'device_distribution': device_stats,
            'most_used': max(self.user_agents, key=lambda ua: ua.usage_count).agent_string if self.user_agents else None,
            'least_used': min(self.user_agents, key=lambda ua: ua.usage_count).agent_string if self.user_agents else None
        }
    
    def reset_usage_stats(self):
        """Reset all usage statistics."""
        for ua in self.user_agents:
            ua.usage_count = 0
            ua.last_used = time.time()
        
        self.logger.info("Reset user agent usage statistics")
    
    def remove_user_agent(self, agent_string: str) -> bool:
        """Remove a user agent from the pool."""
        for i, ua in enumerate(self.user_agents):
            if ua.agent_string == agent_string:
                del self.user_agents[i]
                self.logger.debug(f"Removed user agent: {agent_string[:50]}...")
                return True
        return False
    
    def get_user_agents_by_browser(self, browser: str) -> List[str]:
        """Get all user agents for a specific browser."""
        return [ua.agent_string for ua in self.user_agents if ua.browser.lower() == browser.lower()]
    
    def get_user_agents_by_os(self, os: str) -> List[str]:
        """Get all user agents for a specific operating system."""
        return [ua.agent_string for ua in self.user_agents if ua.os.lower() == os.lower()]
