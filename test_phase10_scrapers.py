#!/usr/bin/env python3
"""
Comprehensive test script for Phase 10 enhanced web scraping capabilities.

This script tests all the new scrapers and enhanced functionality implemented in Phase 10:
- Enhanced Content Extraction Framework
- arXiv Research Paper Scraper
- Hacker News AI Discussion Monitor
- AI Company Blog Scrapers
"""

import sys
import os
import logging
from datetime import datetime
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(__file__))

# Import Phase 10 components
from scrapers.enhanced.advanced_extractor import AdvancedContentExtractor, ExtractorConfig
from scrapers.sources.arxiv_scraper import ArxivScraper, ArxivScraperConfig
from scrapers.sources.hackernews_scraper import Hacker<PERSON>ewsScraper, HackerNewsScraperConfig
from scrapers.sources.ai_company_blogs import (
    OpenAIBlogScraper, 
    AnthropicBlogScraper,
    MultiCompanyBlogScraper,
    AICompanyBlogScraperFactory
)


def setup_logging():
    """Configure logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('phase10_test.log')
        ]
    )


def test_advanced_extractor():
    """Test the enhanced content extraction framework."""
    print("\n" + "="*60)
    print("TESTING ENHANCED CONTENT EXTRACTION FRAMEWORK")
    print("="*60)
    
    try:
        # Test configuration
        config = ExtractorConfig(
            use_selenium=False,
            min_content_length=100,
            extract_images=True,
            extract_links=True
        )
        
        extractor = AdvancedContentExtractor(config)
        
        # Test URLs for different content types
        test_urls = [
            "https://openai.com/blog/gpt-4",  # AI company blog
            "https://arxiv.org/abs/2301.00001",  # Research paper (if exists)
            "https://news.ycombinator.com/item?id=34000000"  # HN discussion
        ]
        
        results = []
        for url in test_urls:
            try:
                print(f"\nTesting extraction from: {url}")
                result = extractor.extract_content(url)
                
                if result:
                    print(f"✓ Successfully extracted content")
                    print(f"  Title: {result.title[:50]}...")
                    print(f"  Content length: {len(result.content)}")
                    print(f"  Quality score: {result.quality_score:.2f}")
                    print(f"  Extraction method: {result.extraction_method}")
                    results.append(result)
                else:
                    print(f"✗ Failed to extract content")
                    
            except Exception as e:
                print(f"✗ Error extracting from {url}: {e}")
        
        extractor.close()
        
        print(f"\nAdvanced Extractor Test Results:")
        print(f"  URLs tested: {len(test_urls)}")
        print(f"  Successful extractions: {len(results)}")
        print(f"  Success rate: {len(results)/len(test_urls)*100:.1f}%")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"✗ Advanced extractor test failed: {e}")
        return False


def test_arxiv_scraper():
    """Test the arXiv research paper scraper."""
    print("\n" + "="*60)
    print("TESTING ARXIV RESEARCH PAPER SCRAPER")
    print("="*60)
    
    try:
        # Configure for limited testing
        config = ArxivScraperConfig(
            max_results=5,
            days_back=3,
            download_pdfs=False  # Disable PDF download for testing
        )
        
        scraper = ArxivScraper(config)
        
        print("Getting arXiv paper URLs...")
        urls = list(scraper.get_article_urls())
        print(f"Found {len(urls)} arXiv paper URLs")
        
        articles = []
        for i, url in enumerate(urls[:3]):  # Test first 3 papers
            print(f"\nScraping paper {i+1}: {url}")
            article = scraper.scrape_article(url)
            
            if article:
                print(f"✓ Successfully scraped paper")
                print(f"  Title: {article.title[:50]}...")
                print(f"  Authors: <AUTHORS>
                print(f"  Content length: {len(article.content)}")
                print(f"  Tags: {article.tags[:5]}")
                print(f"  arXiv ID: {article.metadata.get('arxiv_id', 'N/A')}")
                articles.append(article)
            else:
                print(f"✗ Failed to scrape paper")
        
        stats = scraper.get_stats()
        print(f"\narXiv Scraper Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        scraper.close()
        
        return len(articles) > 0
        
    except Exception as e:
        print(f"✗ arXiv scraper test failed: {e}")
        return False


def test_hackernews_scraper():
    """Test the Hacker News AI discussion monitor."""
    print("\n" + "="*60)
    print("TESTING HACKER NEWS AI DISCUSSION MONITOR")
    print("="*60)
    
    try:
        # Configure for limited testing
        config = HackerNewsScraperConfig(
            max_items_per_page=5,
            hours_back=12,
            include_comments=True,
            max_comments_per_story=5
        )
        
        scraper = HackerNewsScraper(config)
        
        print("Getting Hacker News AI-related stories...")
        urls = list(scraper.get_article_urls())
        print(f"Found {len(urls)} AI-related HN stories")
        
        articles = []
        for i, url in enumerate(urls[:3]):  # Test first 3 stories
            print(f"\nScraping story {i+1}: {url}")
            article = scraper.scrape_article(url)
            
            if article:
                print(f"✓ Successfully scraped story")
                print(f"  Title: {article.title[:50]}...")
                print(f"  Author: {article.author}")
                print(f"  Score: {article.metadata.get('score', 'N/A')}")
                print(f"  Content length: {len(article.content)}")
                print(f"  Has comments: {article.metadata.get('has_comments', False)}")
                print(f"  Tags: {article.tags[:5]}")
                articles.append(article)
            else:
                print(f"✗ Failed to scrape story")
        
        stats = scraper.get_stats()
        print(f"\nHacker News Scraper Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        scraper.close()
        
        return len(articles) > 0
        
    except Exception as e:
        print(f"✗ Hacker News scraper test failed: {e}")
        return False


def test_ai_company_blogs():
    """Test the AI company blog scrapers."""
    print("\n" + "="*60)
    print("TESTING AI COMPANY BLOG SCRAPERS")
    print("="*60)
    
    try:
        # Test individual scrapers
        print("\n--- Testing Individual Company Scrapers ---")
        
        # Test OpenAI scraper
        print("\nTesting OpenAI Blog Scraper...")
        openai_scraper = OpenAIBlogScraper()
        
        openai_urls = list(openai_scraper.get_article_urls())
        print(f"Found {len(openai_urls)} OpenAI blog URLs")
        
        if openai_urls:
            article = openai_scraper.scrape_article(openai_urls[0])
            if article:
                print(f"✓ Successfully scraped OpenAI article")
                print(f"  Title: {article.title[:50]}...")
                print(f"  Company: {article.metadata.get('company', 'N/A')}")
                print(f"  Content length: {len(article.content)}")
        
        openai_scraper.close()
        
        # Test multi-company scraper
        print("\n--- Testing Multi-Company Scraper ---")
        
        # Test with limited companies for faster testing
        test_companies = ['openai', 'anthropic']
        multi_scraper = MultiCompanyBlogScraper(test_companies)
        
        print(f"Testing multi-company scraper with: {test_companies}")
        
        articles = []
        for article in multi_scraper.scrape_all_companies():
            articles.append(article)
            print(f"✓ Scraped: {article.title[:50]}... from {article.metadata.get('company', 'Unknown')}")
            
            # Limit for testing
            if len(articles) >= 5:
                break
        
        stats = multi_scraper.get_combined_stats()
        print(f"\nMulti-Company Scraper Statistics:")
        print(f"  Total articles: {stats['total_articles_processed']}")
        print(f"  Companies scraped: {stats['companies_scraped']}")
        
        for company, company_stats in stats['company_stats'].items():
            print(f"  {company}: {company_stats.get('articles_processed', 0)} articles")
        
        multi_scraper.close()
        
        return len(articles) > 0
        
    except Exception as e:
        print(f"✗ AI company blog scrapers test failed: {e}")
        return False


def test_factory_pattern():
    """Test the AI company blog scraper factory."""
    print("\n" + "="*60)
    print("TESTING AI COMPANY BLOG SCRAPER FACTORY")
    print("="*60)
    
    try:
        # Test factory methods
        available_companies = AICompanyBlogScraperFactory.get_available_companies()
        print(f"Available companies: {available_companies}")
        
        # Test creating individual scrapers
        for company in available_companies[:3]:  # Test first 3
            scraper = AICompanyBlogScraperFactory.create_scraper(company)
            if scraper:
                print(f"✓ Successfully created {company} scraper")
                scraper.close()
            else:
                print(f"✗ Failed to create {company} scraper")
        
        # Test creating all scrapers
        all_scrapers = AICompanyBlogScraperFactory.create_all_scrapers()
        print(f"✓ Created {len(all_scrapers)} company scrapers")
        
        # Clean up
        for scraper in all_scrapers:
            scraper.close()
        
        return True
        
    except Exception as e:
        print(f"✗ Factory pattern test failed: {e}")
        return False


def main():
    """Run all Phase 10 tests."""
    print("PHASE 10 ENHANCED WEB SCRAPING - COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    setup_logging()
    
    # Run all tests
    test_results = {
        'Advanced Content Extractor': test_advanced_extractor(),
        'arXiv Research Paper Scraper': test_arxiv_scraper(),
        'Hacker News AI Monitor': test_hackernews_scraper(),
        'AI Company Blog Scrapers': test_ai_company_blogs(),
        'Factory Pattern': test_factory_pattern()
    }
    
    # Print summary
    print("\n" + "="*80)
    print("PHASE 10 TEST SUMMARY")
    print("="*80)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:<35} {status}")
    
    print(f"\nOverall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL PHASE 10 TESTS PASSED! Enhanced web scraping is ready.")
    else:
        print("⚠️  Some tests failed. Please review the logs for details.")
    
    print(f"\nTest completed at: {datetime.now()}")
    print("Detailed logs saved to: phase10_test.log")


if __name__ == "__main__":
    main()
