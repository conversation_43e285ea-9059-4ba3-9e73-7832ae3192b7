"""
Tests for content processing framework (Phase 4).
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from processing import (
    ContentProcessor, ProcessingConfig, SentimentAnalyzer, 
    TopicCategorizer, TrendDetector, KeywordExtractor,
    AnalysisType
)
from scrapers.base.content_parser import ParsedArticle


class TestContentProcessing:
    """Test suite for content processing framework."""
    
    @pytest.fixture
    def sample_article(self):
        """Create a sample AI/ML article for testing."""
        return ParsedArticle(
            url="https://example.com/ai-breakthrough",
            title="Revolutionary Breakthrough in Large Language Models Shows Promise for AGI",
            content="""
            Researchers at a leading AI lab have announced a revolutionary breakthrough in
            large language model architecture that brings us significantly closer to artificial
            general intelligence (AGI). The new transformer-based model, called GPT-Next,
            demonstrates unprecedented capabilities in reasoning, multimodal understanding,
            and few-shot learning across diverse domains.

            The breakthrough involves a novel attention mechanism that allows the model to
            maintain long-term memory and perform complex reasoning tasks. Initial testing
            shows the model achieving human-level performance on challenging benchmarks
            including mathematical reasoning, scientific problem-solving, and creative writing.

            "This represents a paradigm shift in how we approach artificial intelligence,"
            said Dr. <PERSON>, lead researcher on the project. "The model's ability to
            generalize across domains while maintaining coherent reasoning is unprecedented."

            The research has significant implications for the AI industry, with potential
            applications in autonomous systems, scientific research, and enterprise automation.
            However, the team also emphasizes the importance of AI safety and responsible
            deployment as these capabilities advance.

            Major tech companies including Google, Microsoft, and OpenAI are reportedly
            investing heavily in similar research directions, signaling a new competitive
            landscape in the race toward AGI. The findings were published in the latest
            issue of Nature Machine Intelligence.
            """,
            author="Tech Reporter",
            published_at=datetime.now(),
            source_name="AI News Daily",
            tags=["AI", "LLM", "AGI", "Research"],
            summary="Breakthrough in large language models brings AGI closer to reality"
        )
    
    @pytest.fixture
    def processor_config(self):
        """Create a test configuration for content processor."""
        return ProcessingConfig(
            enable_sentiment=True,
            enable_topics=True,
            enable_trends=True,
            enable_keywords=True,
            parallel_processing=False,
            min_confidence_threshold=0.2
        )
    
    def test_sentiment_analyzer_initialization(self):
        """Test sentiment analyzer initialization."""
        analyzer = SentimentAnalyzer()
        assert analyzer.initialize()
        assert analyzer.is_initialized
        assert analyzer.get_analysis_type() == AnalysisType.SENTIMENT
    
    def test_sentiment_analysis(self, sample_article):
        """Test sentiment analysis on sample article."""
        analyzer = SentimentAnalyzer()
        analyzer.initialize()
        
        result = analyzer.analyze(sample_article)
        
        assert result is not None
        assert result.analysis_type == AnalysisType.SENTIMENT
        assert 0 <= result.confidence <= 1
        assert 'polarity' in result.data
        assert 'score' in result.data
        assert 'confidences' in result.data
    
    def test_topic_categorizer_initialization(self):
        """Test topic categorizer initialization."""
        categorizer = TopicCategorizer()
        assert categorizer.initialize()
        assert categorizer.is_initialized
        assert categorizer.get_analysis_type() == AnalysisType.TOPIC
    
    def test_topic_categorization(self, sample_article):
        """Test topic categorization on sample article."""
        categorizer = TopicCategorizer()
        categorizer.initialize()
        
        result = categorizer.analyze(sample_article)
        
        assert result is not None
        assert result.analysis_type == AnalysisType.TOPIC
        assert 0 <= result.confidence <= 1
        assert 'primary_categories' in result.data
        assert 'all_scores' in result.data
        
        # Should detect LLM and AI-related categories
        primary_categories = result.data['primary_categories']
        all_scores = result.data['all_scores']
        # Either primary categories should be found, or there should be scores for relevant categories
        assert len(primary_categories) > 0 or any('language' in cat or 'learning' in cat for cat in all_scores.keys())
    
    def test_trend_detector_initialization(self):
        """Test trend detector initialization."""
        detector = TrendDetector()
        assert detector.initialize()
        assert detector.is_initialized
        assert detector.get_analysis_type() == AnalysisType.TREND
    
    def test_trend_detection(self, sample_article):
        """Test trend detection on sample article."""
        detector = TrendDetector()
        detector.initialize()
        
        result = detector.analyze(sample_article)
        
        assert result is not None
        assert result.analysis_type == AnalysisType.TREND
        assert 0 <= result.confidence <= 1
        assert 'trend_signals' in result.data
        assert 'primary_trends' in result.data
        
        # Should detect research breakthrough trends
        trend_signals = result.data['trend_signals']
        assert len(trend_signals) > 0
    
    def test_keyword_extractor_initialization(self):
        """Test keyword extractor initialization."""
        extractor = KeywordExtractor()
        assert extractor.initialize()
        assert extractor.is_initialized
        assert extractor.get_analysis_type() == AnalysisType.KEYWORD
    
    def test_keyword_extraction(self, sample_article):
        """Test keyword extraction on sample article."""
        extractor = KeywordExtractor()
        extractor.initialize()
        
        result = extractor.analyze(sample_article)
        
        assert result is not None
        assert result.analysis_type == AnalysisType.KEYWORD
        assert 0 <= result.confidence <= 1
        assert 'keywords' in result.data
        assert 'categorized_keywords' in result.data
        
        # Should extract AI/ML related keywords
        keywords = result.data['keywords']
        assert len(keywords) > 0
        
        keyword_texts = [kw['keyword'] for kw in keywords]
        assert any('ai' in kw or 'model' in kw or 'language' in kw for kw in keyword_texts)
    
    def test_content_processor_initialization(self, processor_config):
        """Test content processor initialization."""
        processor = ContentProcessor(processor_config)
        assert processor.initialize()
        assert len(processor.analyzers) == 4  # All analyzers enabled
    
    def test_content_processor_single_article(self, sample_article, processor_config):
        """Test processing a single article."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        analysis = processor.process_article(sample_article)
        
        assert analysis is not None
        assert len(analysis.analysis_results) > 0
        assert 0 <= analysis.overall_confidence <= 1
        
        # Should have results from all enabled analyzers
        analysis_types = {result.analysis_type for result in analysis.analysis_results}
        expected_types = {AnalysisType.SENTIMENT, AnalysisType.TOPIC, AnalysisType.TREND, AnalysisType.KEYWORD}
        assert analysis_types.intersection(expected_types)  # At least some overlap
    
    def test_content_processor_multiple_articles(self, sample_article, processor_config):
        """Test processing multiple articles."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        # Create multiple articles
        articles = [sample_article] * 3
        
        analyses = processor.process_articles(articles)
        
        assert len(analyses) == 3
        for analysis in analyses:
            assert analysis is not None
            assert len(analysis.analysis_results) > 0
    
    def test_analysis_summary_generation(self, sample_article, processor_config):
        """Test analysis summary generation."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        # Process article
        analysis = processor.process_article(sample_article)
        analyses = [analysis] if analysis else []
        
        summary = processor.get_analysis_summary(analyses)
        
        assert 'summary_stats' in summary
        assert 'sentiment_summary' in summary
        assert 'topic_summary' in summary
        assert 'trend_summary' in summary
        assert 'keyword_summary' in summary
        assert 'processing_stats' in summary
        
        # Check summary stats
        stats = summary['summary_stats']
        assert stats['total_articles'] == 1
        assert 0 <= stats['average_confidence'] <= 1
    
    def test_processing_statistics(self, sample_article, processor_config):
        """Test processing statistics tracking."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        # Process article
        processor.process_article(sample_article)
        
        stats = processor.get_processing_stats()
        
        assert stats['articles_processed'] == 1
        assert stats['successful_analyses'] >= 0
        assert stats['total_processing_time_ms'] > 0
        assert 'analyzer_stats' in stats
    
    def test_confidence_threshold_filtering(self, sample_article):
        """Test confidence threshold filtering."""
        config = ProcessingConfig(min_confidence_threshold=0.8)  # High threshold
        processor = ContentProcessor(config)
        processor.initialize()
        
        analysis = processor.process_article(sample_article)
        
        if analysis:
            # All results should meet the high confidence threshold
            for result in analysis.analysis_results:
                assert result.confidence >= 0.8
    
    def test_selective_analyzer_configuration(self, sample_article):
        """Test selective analyzer configuration."""
        # Only enable sentiment and topics
        config = ProcessingConfig(
            enable_sentiment=True,
            enable_topics=True,
            enable_trends=False,
            enable_keywords=False
        )
        
        processor = ContentProcessor(config)
        processor.initialize()
        
        assert len(processor.analyzers) == 2
        
        analysis = processor.process_article(sample_article)
        
        if analysis:
            analysis_types = {result.analysis_type for result in analysis.analysis_results}
            assert AnalysisType.SENTIMENT in analysis_types or AnalysisType.TOPIC in analysis_types
            assert AnalysisType.TREND not in analysis_types
            assert AnalysisType.KEYWORD not in analysis_types
    
    def test_empty_article_handling(self, processor_config):
        """Test handling of empty or invalid articles."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        # Test with None
        assert processor.process_article(None) is None
        
        # Test with empty content
        empty_article = ParsedArticle(
            url="https://example.com/empty",
            title="",
            content="",
            author="",
            published_at=datetime.now(),
            source_name="Test",
            tags=[],
            summary=""
        )
        
        assert processor.process_article(empty_article) is None
    
    def test_processor_shutdown(self, processor_config):
        """Test processor shutdown."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        # Should not raise any exceptions
        processor.shutdown()
    
    def test_stats_reset(self, sample_article, processor_config):
        """Test statistics reset functionality."""
        processor = ContentProcessor(processor_config)
        processor.initialize()
        
        # Process article to generate stats
        processor.process_article(sample_article)
        
        # Verify stats exist
        stats_before = processor.get_processing_stats()
        assert stats_before['articles_processed'] > 0
        
        # Reset stats
        processor.reset_stats()
        
        # Verify stats are reset
        stats_after = processor.get_processing_stats()
        assert stats_after['articles_processed'] == 0
        assert stats_after['total_processing_time_ms'] == 0
