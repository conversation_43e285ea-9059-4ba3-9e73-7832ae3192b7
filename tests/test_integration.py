"""
Integration tests for the scraping framework.
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrapers import NewsScraper, ScraperConfig, RateLimit, ParsedArticle
from scrapers.utils.duplicate_detector import DuplicateDetector
from config.scraper_config import ScrapingConfigManager


class TestScrapingIntegration:
    """Integration tests for the complete scraping workflow."""
    
    def test_config_manager_initialization(self):
        """Test that the configuration manager initializes correctly."""
        config_manager = ScrapingConfigManager()
        
        # Check that sources are loaded
        all_sources = config_manager.get_all_sources()
        assert len(all_sources) > 0
        
        # Check that news sources exist
        news_sources = config_manager.get_sources_by_type("news")
        assert len(news_sources) > 0
        
        # Check that Reddit sources exist
        reddit_sources = config_manager.get_sources_by_type("reddit")
        assert len(reddit_sources) > 0
    
    def test_scraper_config_creation(self):
        """Test creating scraper configs from source configs."""
        config_manager = ScrapingConfigManager()
        
        # Get a news source
        news_sources = config_manager.get_sources_by_type("news")
        source = news_sources[0]
        
        # Create scraper config
        scraper_config = config_manager.create_scraper_config(source)
        
        assert scraper_config.name == source.name
        assert scraper_config.base_url == source.base_url
        assert scraper_config.rate_limit == source.rate_limit
    
    @patch('requests.Session.get')
    def test_news_scraper_workflow(self, mock_get):
        """Test the complete news scraper workflow with mocked responses."""
        # Mock HTML response for sitemap
        sitemap_html = """
        <html>
            <body>
                <a href="/article/ai-breakthrough-2024">AI Breakthrough</a>
                <a href="/article/machine-learning-trends">ML Trends</a>
                <a href="/tag/ai">AI Tag Page</a>
                <a href="/image.jpg">Image</a>
            </body>
        </html>
        """
        
        # Mock HTML response for article
        article_html = """
        <html>
            <head>
                <title>AI Breakthrough in 2024</title>
                <meta name="author" content="Tech Reporter">
                <meta name="description" content="Latest AI developments">
            </head>
            <body>
                <article>
                    <h1>AI Breakthrough in 2024</h1>
                    <p>This article discusses the latest artificial intelligence 
                       developments and machine learning breakthroughs in 2024.</p>
                    <p>The new neural network architecture shows promising results
                       for natural language processing tasks.</p>
                </article>
            </body>
        </html>
        """
        
        # Setup mock responses
        def mock_response(url, **kwargs):
            response = Mock()
            response.status_code = 200
            response.headers = {'content-type': 'text/html'}
            
            if 'article' in url:
                response.text = article_html
                response.content = article_html.encode('utf-8')
            else:
                response.text = sitemap_html
                response.content = sitemap_html.encode('utf-8')
            
            return response
        
        mock_get.side_effect = mock_response
        
        # Create scraper configuration
        config = ScraperConfig(
            name="test_news",
            base_url="https://example.com",
            rate_limit=RateLimit(delay_between_requests=0.1, respect_robots_txt=False),
            timeout=10,
            max_retries=1
        )
        
        # Test scraping
        with NewsScraper(config) as scraper:
            articles = list(scraper.scrape_all())
            
            # Should find at least one article
            assert len(articles) > 0
            
            # Check article properties
            article = articles[0]
            assert isinstance(article, ParsedArticle)
            assert article.title is not None
            assert article.content is not None
            assert article.url is not None
            assert len(article.content) > 50  # Should have substantial content
            
            # Check that AI-related content is detected
            from scrapers.base.content_parser import ContentParser
            parser = ContentParser()
            assert parser.is_ai_related(article) == True
    
    def test_duplicate_detection_workflow(self):
        """Test duplicate detection in a realistic workflow."""
        detector = DuplicateDetector(similarity_threshold=0.85)
        
        # Create test articles
        article1 = ParsedArticle(
            title="AI Breakthrough in Machine Learning",
            content="This article discusses artificial intelligence and machine learning advances in 2024.",
            url="https://example.com/ai-breakthrough-1",
            source_name="TechNews"
        )
        
        article2 = ParsedArticle(
            title="AI Breakthrough in Machine Learning",  # Same title
            content="This article discusses artificial intelligence and machine learning advances in 2024.",  # Same content
            url="https://different-site.com/ai-news",  # Different URL
            source_name="TechBlog"
        )
        
        article3 = ParsedArticle(
            title="Completely Different Topic",
            content="This article is about cooking recipes and has nothing to do with technology.",
            url="https://example.com/cooking-recipe",
            source_name="FoodBlog"
        )
        
        # Test workflow
        assert detector.add_article(article1) == True  # First article added
        assert detector.is_duplicate(article2) == True  # Should be detected as duplicate
        assert detector.add_article(article2) == False  # Should not be added
        assert detector.is_duplicate(article3) == False  # Should not be duplicate
        assert detector.add_article(article3) == True  # Should be added
        
        # Check statistics
        assert len(detector.url_cache) == 2  # Only unique articles
        assert len(detector.content_hashes) == 2
    
    def test_ai_keyword_detection(self):
        """Test AI keyword detection across different content types."""
        from scrapers.base.content_parser import ContentParser
        parser = ContentParser()
        
        # Test various AI-related content
        ai_articles = [
            ParsedArticle(
                title="ChatGPT and Large Language Models",
                content="Discussion about OpenAI's ChatGPT and other LLMs.",
                url="https://example.com/chatgpt"
            ),
            ParsedArticle(
                title="Deep Learning with PyTorch",
                content="Tutorial on neural networks and deep learning using PyTorch framework.",
                url="https://example.com/pytorch"
            ),
            ParsedArticle(
                title="Computer Vision Breakthrough",
                content="New developments in computer vision and image recognition using CNNs.",
                url="https://example.com/cv"
            ),
            ParsedArticle(
                title="Natural Language Processing",
                content="Advances in NLP and transformer architectures like BERT and GPT.",
                url="https://example.com/nlp"
            )
        ]
        
        # Test non-AI content
        non_ai_articles = [
            ParsedArticle(
                title="Cooking Recipe",
                content="How to make delicious pasta with tomato sauce and herbs.",
                url="https://example.com/recipe"
            ),
            ParsedArticle(
                title="Travel Guide",
                content="Best places to visit in Europe during summer vacation.",
                url="https://example.com/travel"
            ),
            ParsedArticle(
                title="Sports News",
                content="Latest football match results and player statistics.",
                url="https://example.com/sports"
            )
        ]
        
        # All AI articles should be detected as AI-related
        for article in ai_articles:
            assert parser.is_ai_related(article) == True, f"Failed to detect AI content in: {article.title}"
        
        # Non-AI articles should not be detected as AI-related
        for article in non_ai_articles:
            assert parser.is_ai_related(article) == False, f"Incorrectly detected AI content in: {article.title}"
    
    def test_rate_limiting_integration(self):
        """Test rate limiting in a realistic scraping scenario."""
        import time
        from scrapers.base.rate_limiter import RateLimiter, RateLimit
        
        limiter = RateLimiter()
        
        # Set up rate limiting for a domain
        rate_limit = RateLimit(
            delay_between_requests=0.2,  # 200ms delay
            respect_robots_txt=False  # Disable for testing
        )
        limiter.set_rate_limit("https://example.com", rate_limit)
        
        # Simulate multiple requests
        urls = [
            "https://example.com/page1",
            "https://example.com/page2", 
            "https://example.com/page3"
        ]
        
        start_time = time.time()
        
        for url in urls:
            limiter.wait_if_needed(url)
        
        total_time = time.time() - start_time
        
        # Should take at least 400ms (2 delays of 200ms each)
        # First request doesn't wait, second waits 200ms, third waits 200ms
        assert total_time >= 0.4, f"Rate limiting not working properly: {total_time}"
    
    def test_error_handling(self):
        """Test error handling in scraping workflow."""
        from scrapers.base.exceptions import ScrapingError, ContentParsingError
        
        # Test invalid URL handling
        config = ScraperConfig(
            name="test_error",
            base_url="https://invalid-domain-that-does-not-exist.com",
            rate_limit=RateLimit(respect_robots_txt=False),
            timeout=1,  # Short timeout
            max_retries=1
        )
        
        # Should handle network errors gracefully
        with NewsScraper(config) as scraper:
            articles = list(scraper.scrape_all())
            # Should return empty list instead of crashing
            assert isinstance(articles, list)
    
    def test_content_filtering(self):
        """Test content filtering and validation."""
        from scrapers.base.content_parser import ContentParser
        
        parser = ContentParser()
        
        # Test minimum content length filtering
        short_article = ParsedArticle(
            title="Short",
            content="Too short",  # Very short content
            url="https://example.com/short"
        )
        
        long_article = ParsedArticle(
            title="Comprehensive AI Guide",
            content="This is a comprehensive guide to artificial intelligence " * 10,  # Long content
            url="https://example.com/long"
        )
        
        # Content validation should work
        assert len(short_article.content) < 100
        assert len(long_article.content) > 100


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v"])
