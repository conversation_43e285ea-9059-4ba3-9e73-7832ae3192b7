"""
Tests for database models.
"""

import pytest
from sqlalchemy.orm import Session
from models.database import Article, Source, Tag, SourceType


def test_create_source(db_session: Session):
    """Test creating a source."""
    source = Source(
        name="Test Source",
        type="rss",
        url="https://example.com/rss",
        is_active=True
    )
    db_session.add(source)
    db_session.commit()
    
    assert source.id is not None
    assert source.name == "Test Source"
    assert source.type == "rss"
    assert source.is_active is True


def test_create_tag(db_session: Session):
    """Test creating a tag."""
    tag = Tag(
        name="AI",
        category="technology",
        confidence_score=0.95
    )
    db_session.add(tag)
    db_session.commit()
    
    assert tag.id is not None
    assert tag.name == "AI"
    assert tag.category == "technology"
    assert tag.confidence_score == 0.95


def test_create_article(db_session: Session):
    """Test creating an article."""
    # Create a source first
    source = Source(
        name="Test Source",
        type="rss",
        url="https://example.com/rss"
    )
    db_session.add(source)
    db_session.commit()
    
    # Create an article
    article = Article(
        title="Test Article",
        content="This is a test article about AI.",
        url="https://example.com/article/1",
        source_type=SourceType.RSS,
        source_id="test-1",
        author="Test Author",
        source_uuid=source.id
    )
    db_session.add(article)
    db_session.commit()
    
    assert article.id is not None
    assert article.title == "Test Article"
    assert article.source_type == SourceType.RSS
    assert article.source_uuid == source.id


def test_article_tag_relationship(db_session: Session):
    """Test the many-to-many relationship between articles and tags."""
    # Create a tag
    tag = Tag(name="Machine Learning", category="technology")
    db_session.add(tag)
    
    # Create an article
    article = Article(
        title="ML Article",
        url="https://example.com/ml-article",
        source_type=SourceType.BLOG
    )
    db_session.add(article)
    
    # Associate tag with article
    article.tags.append(tag)
    db_session.commit()
    
    # Verify relationship
    assert len(article.tags) == 1
    assert article.tags[0].name == "Machine Learning"
    assert len(tag.articles) == 1
    assert tag.articles[0].title == "ML Article"
