"""
Integration tests for Phase 8 content processing components.

Tests the complete Phase 8 pipeline including:
- Content relevance scoring
- Automatic topic clustering  
- Spam and quality filtering
- Content summarization
"""

import pytest
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List

from scrapers.base.content_parser import ParsedArticle
from processing.processor import ContentProcessor, ProcessingConfig
from processing.relevance.scorer import ContentRelevanceScorer
from processing.clustering.topic_clusterer import TopicClusterer
from processing.quality.filter import ContentQualityFilter, QualityLevel
from processing.summarization.summarizer import ContentSummarizer, SummaryLength
from processing.base.analyzer import AnalysisType


class TestPhase8Integration:
    """Integration tests for Phase 8 content processing."""
    
    @pytest.fixture
    def sample_articles(self) -> List[ParsedArticle]:
        """Create sample articles for testing."""
        articles = [
            # High-quality AI article
            ParsedArticle(
                url="https://arxiv.org/abs/2023.12345",
                title="Revolutionary Breakthrough in Large Language Model Architecture",
                content="""
                Researchers at Stanford University have developed a novel transformer architecture 
                that significantly improves the efficiency of large language models. The new approach, 
                called Efficient Attention Mechanism (EAM), reduces computational complexity by 40% 
                while maintaining comparable performance on standard benchmarks.
                
                The study, published in Nature Machine Intelligence, demonstrates that EAM can process 
                natural language tasks with unprecedented speed. The methodology involves a sophisticated 
                attention mechanism that selectively focuses on the most relevant parts of the input sequence.
                
                "This breakthrough could revolutionize how we deploy AI systems in production environments," 
                said Dr. Sarah Chen, lead researcher on the project. The findings have significant 
                implications for the future of artificial intelligence and machine learning applications.
                
                The research team conducted extensive experiments on multiple datasets including GLUE, 
                SuperGLUE, and custom benchmarks. Results show consistent improvements across all metrics, 
                with particular strength in language understanding and generation tasks.
                """,
                author="Dr. Sarah Chen",
                published_at=datetime.now() - timedelta(hours=2),
                source_name="academic"
            ),
            
            # Medium-quality tech news
            ParsedArticle(
                url="https://techcrunch.com/2023/ai-news",
                title="OpenAI Announces New GPT Model with Enhanced Capabilities",
                content="""
                OpenAI today announced the release of their latest language model, featuring improved 
                reasoning capabilities and better factual accuracy. The new model shows significant 
                improvements in mathematical problem solving and code generation.
                
                According to the company, the model has been trained on a larger dataset and incorporates 
                new training techniques that enhance its ability to understand context and generate 
                coherent responses. Early testing shows promising results across various benchmarks.
                
                The announcement comes amid increasing competition in the AI space, with companies like 
                Google, Anthropic, and others releasing their own advanced language models. Industry 
                experts believe this could accelerate innovation in AI applications.
                """,
                author="Tech Reporter",
                published_at=datetime.now() - timedelta(hours=6),
                source_name="news"
            ),
            
            # Low-quality/spam article
            ParsedArticle(
                url="https://clickbait-site.com/amazing-ai",
                title="You Won't Believe This SHOCKING AI Discovery That Will Change Everything!",
                content="""
                OMG this is so amazing!!! Scientists have discovered something incredible about AI that 
                will blow your mind. Click here to learn the one weird trick that AI researchers don't 
                want you to know!
                
                This revolutionary discovery will make you rich and change your life forever. Don't miss 
                out on this limited time opportunity to learn the secret that big tech companies are hiding.
                
                Buy now and get exclusive access to insider information that could make you millions!
                """,
                author=None,
                published_at=datetime.now() - timedelta(days=1),
                source_name="blog"
            ),
            
            # Duplicate content (similar to first article)
            ParsedArticle(
                url="https://another-site.com/ai-breakthrough",
                title="Stanford Researchers Develop New Transformer Architecture",
                content="""
                Researchers at Stanford University have developed a novel transformer architecture 
                that significantly improves the efficiency of large language models. The new approach 
                reduces computational complexity while maintaining performance on benchmarks.
                
                The study demonstrates that the new method can process natural language tasks with 
                improved speed. The methodology involves an attention mechanism that focuses on 
                relevant parts of the input sequence.
                """,
                author="News Writer",
                published_at=datetime.now() - timedelta(hours=4),
                source_name="news"
            ),
            
            # Irrelevant content
            ParsedArticle(
                url="https://cooking-blog.com/recipes",
                title="Best Chocolate Chip Cookie Recipe for 2023",
                content="""
                Looking for the perfect chocolate chip cookie recipe? Look no further! This amazing 
                recipe will give you the most delicious cookies you've ever tasted. The secret is 
                in the butter temperature and mixing technique.
                
                Ingredients: flour, butter, sugar, eggs, chocolate chips, vanilla extract, baking soda.
                Mix all ingredients together and bake at 350 degrees for 12 minutes. Enjoy!
                """,
                author="Chef Baker",
                published_at=datetime.now() - timedelta(hours=8),
                source_name="blog"
            )
        ]
        return articles
    
    @pytest.fixture
    def processor_config(self) -> ProcessingConfig:
        """Create processor configuration for testing."""
        return ProcessingConfig(
            enable_sentiment=True,
            enable_topics=True,
            enable_trends=True,
            enable_keywords=True,
            enable_relevance=True,
            enable_clustering=True,
            enable_quality_filter=True,
            enable_summarization=True,
            filter_low_quality=True,
            quality_threshold=0.4,
            min_confidence_threshold=0.3
        )
    
    def test_relevance_scoring(self, sample_articles):
        """Test content relevance scoring."""
        scorer = ContentRelevanceScorer()
        assert scorer.initialize()
        
        # Test high-quality AI article
        result = scorer.analyze(sample_articles[0])
        assert result.analysis_type == AnalysisType.RELEVANCE
        assert result.confidence > 0.7
        assert result.data['overall_score'] > 0.7
        
        # Test irrelevant content
        result = scorer.analyze(sample_articles[4])  # Cookie recipe
        assert result.data['overall_score'] < 0.3
    
    def test_quality_filtering(self, sample_articles):
        """Test content quality filtering."""
        filter_analyzer = ContentQualityFilter()
        assert filter_analyzer.initialize()
        
        # Test high-quality article
        result = filter_analyzer.analyze(sample_articles[0])
        assert result.analysis_type == AnalysisType.QUALITY
        assert result.data['quality_level'] in ['high', 'medium']
        assert not result.data['should_filter']
        
        # Test spam/clickbait article
        result = filter_analyzer.analyze(sample_articles[2])
        assert result.data['should_filter'] or result.data['overall_score'] < 0.4
        assert result.data['clickbait_score'] > 0.5
    
    def test_topic_clustering(self, sample_articles):
        """Test automatic topic clustering."""
        clusterer = TopicClusterer()
        assert clusterer.initialize()
        
        # Filter to AI-related articles only
        ai_articles = sample_articles[:4]  # Exclude cookie recipe
        
        clustering_result = clusterer.cluster_articles(ai_articles)
        assert clustering_result is not None
        assert clustering_result.total_articles == len(ai_articles)
        assert len(clustering_result.clusters) > 0
        
        # Check that similar articles are clustered together
        for cluster in clustering_result.clusters:
            assert len(cluster.articles) >= 1
            assert cluster.coherence_score >= 0.0
            assert len(cluster.centroid_keywords) > 0
    
    def test_content_summarization(self, sample_articles):
        """Test content summarization."""
        summarizer = ContentSummarizer()
        assert summarizer.initialize()
        
        # Test summarization of high-quality article
        result = summarizer.analyze(sample_articles[0])
        assert result.analysis_type == AnalysisType.SUMMARY
        assert result.confidence > 0.5
        
        summary_text = result.data['primary_summary']
        assert len(summary_text) > 0
        assert len(summary_text) < len(sample_articles[0].content)
        
        # Test bullet points generation
        bullet_points = result.data['bullet_points']
        assert isinstance(bullet_points, list)
        assert len(bullet_points) > 0
    
    def test_integrated_processing_pipeline(self, sample_articles, processor_config):
        """Test the complete integrated processing pipeline."""
        processor = ContentProcessor(processor_config)
        assert processor.initialize()
        
        # Process all articles
        analyses = processor.process_articles(sample_articles)
        
        # Should filter out low-quality content
        assert len(analyses) < len(sample_articles)
        
        # Check that high-quality articles are processed
        high_quality_found = False
        for analysis in analyses:
            if analysis.article.url == sample_articles[0].url:  # High-quality article
                high_quality_found = True
                
                # Should have all analysis types
                analysis_types = {result.analysis_type for result in analysis.analysis_results}
                expected_types = {
                    AnalysisType.SENTIMENT, AnalysisType.TOPIC, AnalysisType.TREND,
                    AnalysisType.KEYWORD, AnalysisType.RELEVANCE, AnalysisType.QUALITY,
                    AnalysisType.SUMMARY
                }
                assert analysis_types.intersection(expected_types)
                
                # Check quality metrics
                quality_result = analysis.get_result(AnalysisType.QUALITY)
                assert quality_result is not None
                assert not quality_result.data['should_filter']
                
                # Check relevance score
                relevance_result = analysis.get_result(AnalysisType.RELEVANCE)
                assert relevance_result is not None
                assert relevance_result.data['overall_score'] > 0.5
                
                break
        
        assert high_quality_found, "High-quality article should be processed"
    
    def test_clustering_integration(self, sample_articles, processor_config):
        """Test clustering integration with the processor."""
        processor = ContentProcessor(processor_config)
        assert processor.initialize()
        
        # Test clustering functionality
        clustering_result = processor.cluster_articles(sample_articles)
        assert clustering_result is not None
        assert clustering_result.total_articles == len(sample_articles)
        
        # Should identify some clusters
        assert len(clustering_result.clusters) > 0
        
        # Check trending clusters identification
        if clustering_result.trending_clusters:
            assert len(clustering_result.trending_clusters) > 0
    
    def test_analysis_summary_generation(self, sample_articles, processor_config):
        """Test analysis summary generation with Phase 8 components."""
        processor = ContentProcessor(processor_config)
        assert processor.initialize()
        
        analyses = processor.process_articles(sample_articles)
        summary = processor.get_analysis_summary(analyses)
        
        # Check that Phase 8 summaries are included
        assert 'relevance_summary' in summary
        assert 'quality_summary' in summary
        assert 'summary_summary' in summary
        
        # Check relevance summary
        relevance_summary = summary['relevance_summary']
        if relevance_summary:
            assert 'average_relevance_score' in relevance_summary
            assert 'relevance_distribution' in relevance_summary
        
        # Check quality summary
        quality_summary = summary['quality_summary']
        if quality_summary:
            assert 'quality_level_distribution' in quality_summary
            assert 'filter_rate' in quality_summary
        
        # Check summary summary
        summary_summary = summary['summary_summary']
        if summary_summary:
            assert 'average_compression_ratio' in summary_summary
            assert 'summaries_generated' in summary_summary
    
    def test_performance_metrics(self, sample_articles, processor_config):
        """Test performance metrics collection."""
        processor = ContentProcessor(processor_config)
        assert processor.initialize()
        
        # Process articles and collect stats
        analyses = processor.process_articles(sample_articles)
        stats = processor.get_processing_stats()
        
        assert stats['articles_processed'] > 0
        assert stats['total_processing_time_ms'] > 0
        assert stats['average_processing_time_ms'] > 0
        assert 'success_rate' in stats
        assert 'analysis_type_stats' in stats
        
        # Should have processed some articles successfully
        assert stats['successful_analyses'] > 0
