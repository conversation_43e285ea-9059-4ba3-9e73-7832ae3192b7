"""
Tests for the web scraping framework.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from scrapers.base.scraper import <PERSON><PERSON><PERSON><PERSON><PERSON>, ScraperConfig
from scrapers.base.rate_limiter import RateLimiter, RateLimit
from scrapers.base.content_parser import <PERSON><PERSON>ars<PERSON>, ParsedArticle
from scrapers.base.exceptions import ScrapingError, RateLimitError, ContentParsingError
from scrapers.utils.duplicate_detector import DuplicateDetector
from scrapers.utils.user_agent_manager import UserAgentManager
from scrapers.sources.news_scraper import NewsScraper


class TestRateLimiter:
    """Test rate limiting functionality."""
    
    def test_rate_limiter_initialization(self):
        """Test rate limiter initialization."""
        limiter = RateLimiter()
        assert limiter._domain_limits == {}
        assert limiter._last_request_times == {}
    
    def test_domain_extraction(self):
        """Test domain extraction from URLs."""
        limiter = RateLimiter()
        
        url = "https://example.com/article/123"
        domain = limiter.get_domain_from_url(url)
        assert domain == "https://example.com"
    
    def test_rate_limiting(self):
        """Test basic rate limiting functionality."""
        limiter = RateLimiter()

        # Set a rate limit without robots.txt checking
        rate_limit = RateLimit(delay_between_requests=0.1, respect_robots_txt=False)
        limiter.set_rate_limit("https://example.com", rate_limit)

        # First request should not wait
        start_time = time.time()
        limiter.wait_if_needed("https://example.com/page1")
        first_duration = time.time() - start_time

        # Second request should wait
        start_time = time.time()
        limiter.wait_if_needed("https://example.com/page2")
        second_duration = time.time() - start_time

        assert first_duration < 0.05  # Should be very fast
        assert second_duration >= 0.1  # Should wait at least 0.1 seconds


class TestContentParser:
    """Test content parsing functionality."""
    
    def test_parser_initialization(self):
        """Test content parser initialization."""
        parser = ContentParser()
        assert len(parser.ai_keywords) > 0
        assert 'artificial intelligence' in parser.ai_keywords
    
    def test_title_extraction(self):
        """Test title extraction from HTML."""
        parser = ContentParser()

        html = """
        <html>
            <head><title>Test Article Title</title></head>
            <body>
                <h1>Test Article Title</h1>
                <p>Article content here</p>
            </body>
        </html>
        """

        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, 'html.parser')
        title = parser._extract_title(soup)
        assert title == "Test Article Title"
    
    def test_ai_keyword_detection(self):
        """Test AI keyword detection."""
        parser = ContentParser()
        
        # Create test articles
        ai_article = ParsedArticle(
            title="New AI Model Breakthrough",
            content="This article discusses artificial intelligence and machine learning advances.",
            url="https://example.com/ai-article",
            tags=["ai", "technology"]
        )
        
        non_ai_article = ParsedArticle(
            title="Cooking Recipe",
            content="How to make delicious pasta with tomato sauce.",
            url="https://example.com/recipe",
            tags=["cooking", "food"]
        )
        
        assert parser.is_ai_related(ai_article) == True
        assert parser.is_ai_related(non_ai_article) == False
    
    def test_content_hash_generation(self):
        """Test content hash generation for duplicate detection."""
        article = ParsedArticle(
            title="Test Title",
            content="Test content for hashing",
            url="https://example.com/test"
        )
        
        # Hash should be consistent
        hash1 = article.generate_content_hash()
        hash2 = article.generate_content_hash()
        assert hash1 == hash2
        
        # Different content should produce different hash
        article2 = ParsedArticle(
            title="Different Title",
            content="Different content for hashing",
            url="https://example.com/test2"
        )
        
        assert hash1 != article2.generate_content_hash()


class TestDuplicateDetector:
    """Test duplicate detection functionality."""
    
    def test_detector_initialization(self):
        """Test duplicate detector initialization."""
        detector = DuplicateDetector()
        assert detector.similarity_threshold == 0.85
        assert len(detector.url_cache) == 0
    
    def test_url_normalization(self):
        """Test URL normalization for duplicate detection."""
        detector = DuplicateDetector()
        
        # Test tracking parameter removal
        url_with_tracking = "https://example.com/article?utm_source=twitter&utm_campaign=test"
        normalized = detector.normalize_url(url_with_tracking)
        assert "utm_source" not in normalized
        assert "utm_campaign" not in normalized
        
        # Test case normalization
        url_mixed_case = "https://Example.COM/Article"
        normalized = detector.normalize_url(url_mixed_case)
        assert normalized == "https://example.com/article"
    
    def test_duplicate_detection(self):
        """Test duplicate article detection."""
        detector = DuplicateDetector()
        
        # Create test articles
        article1 = ParsedArticle(
            title="AI Breakthrough",
            content="This is about artificial intelligence advances.",
            url="https://example.com/ai-news"
        )
        
        article2 = ParsedArticle(
            title="AI Breakthrough",
            content="This is about artificial intelligence advances.",
            url="https://example.com/ai-news-duplicate"
        )
        
        # First article should not be duplicate
        assert detector.add_article(article1) == True
        
        # Second article should be detected as duplicate
        assert detector.is_duplicate(article2) == True
    
    def test_similarity_calculation(self):
        """Test text similarity calculation."""
        detector = DuplicateDetector()
        
        text1 = "This is a test article about artificial intelligence"
        text2 = "This is a test article about artificial intelligence"
        text3 = "This is completely different content about cooking"
        
        # Identical texts should have similarity of 1.0
        similarity1 = detector.calculate_text_similarity(text1, text2)
        assert similarity1 == 1.0
        
        # Different texts should have lower similarity
        similarity2 = detector.calculate_text_similarity(text1, text3)
        assert similarity2 < 0.5


class TestUserAgentManager:
    """Test user agent management."""
    
    def test_manager_initialization(self):
        """Test user agent manager initialization."""
        manager = UserAgentManager()
        assert len(manager.user_agents) > 0
    
    def test_random_user_agent(self):
        """Test random user agent selection."""
        manager = UserAgentManager()
        
        # Get multiple user agents
        agents = [manager.get_random_user_agent() for _ in range(10)]
        
        # Should get valid user agent strings
        for agent in agents:
            assert isinstance(agent, str)
            assert len(agent) > 50  # User agents are typically long
            assert "Mozilla" in agent  # Most user agents contain Mozilla
    
    def test_filtered_user_agents(self):
        """Test filtered user agent selection."""
        manager = UserAgentManager()
        
        # Test device type filtering
        desktop_agent = manager.get_desktop_user_agent()
        mobile_agent = manager.get_mobile_user_agent()
        
        assert isinstance(desktop_agent, str)
        assert isinstance(mobile_agent, str)
        
        # Test browser filtering
        chrome_agent = manager.get_chrome_user_agent()
        firefox_agent = manager.get_firefox_user_agent()
        
        assert "Chrome" in chrome_agent
        assert "Firefox" in firefox_agent


class MockScraper(BaseScraper):
    """Mock scraper for testing base functionality."""
    
    def get_article_urls(self):
        """Mock implementation."""
        return ["https://example.com/article1", "https://example.com/article2"]
    
    def scrape_article(self, url):
        """Mock implementation."""
        return ParsedArticle(
            title="Mock Article",
            content="This is mock content about artificial intelligence.",
            url=url
        )


class TestBaseScraper:
    """Test base scraper functionality."""
    
    def test_scraper_initialization(self):
        """Test scraper initialization."""
        config = ScraperConfig(
            name="test_scraper",
            base_url="https://example.com"
        )
        
        scraper = MockScraper(config)
        assert scraper.config.name == "test_scraper"
        assert scraper.config.base_url == "https://example.com"
        assert scraper.session is not None
    
    def test_user_agent_rotation(self):
        """Test user agent rotation."""
        config = ScraperConfig(
            name="test_scraper",
            base_url="https://example.com",
            user_agents=["Agent1", "Agent2", "Agent3"]
        )
        
        scraper = MockScraper(config)
        
        # Get multiple user agents
        agents = [scraper._get_random_user_agent() for _ in range(10)]
        
        # Should get agents from the configured list
        for agent in agents:
            assert agent in config.user_agents
    
    @patch('requests.Session.get')
    def test_request_making(self, mock_get):
        """Test HTTP request making with mocking."""
        config = ScraperConfig(
            name="test_scraper",
            base_url="https://example.com"
        )
        
        scraper = MockScraper(config)
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<html><body>Test content</body></html>"
        mock_get.return_value = mock_response
        
        # Make request
        response = scraper.make_request("https://example.com/test")
        
        assert response.status_code == 200
        assert "Test content" in response.text
        assert scraper.stats['requests_made'] == 1
    
    def test_statistics_tracking(self):
        """Test scraper statistics tracking."""
        config = ScraperConfig(
            name="test_scraper",
            base_url="https://example.com"
        )
        
        scraper = MockScraper(config)
        
        # Initial stats
        stats = scraper.get_stats()
        assert stats['requests_made'] == 0
        assert stats['articles_scraped'] == 0
        assert stats['errors_encountered'] == 0
        
        # Reset stats
        scraper.reset_stats()
        stats = scraper.get_stats()
        assert stats['requests_made'] == 0


class TestNewsScraper:
    """Test news scraper implementation."""
    
    def test_url_validation(self):
        """Test article URL validation."""
        config = ScraperConfig(
            name="news_scraper",
            base_url="https://example.com"
        )
        
        scraper = NewsScraper(config)
        
        # Valid URLs
        assert scraper._is_valid_article_url("https://example.com/article/123") == True
        assert scraper._is_valid_article_url("https://example.com/news/ai-breakthrough") == True
        
        # Invalid URLs
        assert scraper._is_valid_article_url("https://other-site.com/article/123") == False
        assert scraper._is_valid_article_url("https://example.com/tag/ai") == False
        assert scraper._is_valid_article_url("https://example.com/image.jpg") == False


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
